;(function($,window,document,undefined){var name='glide',defaults={autoplay:4000,animationTime:500,arrows:true,arrowsWrapperClass:'slider-arrows',arrowMainClass:'slider-arrow',arrowRightClass:'slider-arrow--right',arrowRightText:' ',arrowLeftClass:'slider-arrow--left',arrowLeftText:' ',nav:true,navCenter:true,navClass:'slider-nav',navItemClass:'slider-nav__item',navCurrentItemClass:'slider-nav__item--current',touchDistance:60,slideCurrentItemClass:'slide__item--current'};function Glide(parent,options){var _=this;_.options=$.extend({},defaults,options);_.parent=parent;_.wrapper=_.parent.children();_.slides=_.wrapper.children();_.currentSlide=0;_.CSS3support=true;_.init();_.build();_.play();if(_.options.touchDistance){_.swipe();}
$(document).on('keyup',function(k){if(k.keyCode===39)_.slide(1);if(k.keyCode===37)_.slide(-1);});_.parent.add(_.arrows).add(_.nav).on('mouseover mouseout',function(e){_.pause();if(e.type==='mouseout')_.play();});$(window).on('resize',function(){_.init();_.slide(0);});return{current:function(){return-(_.currentSlide)+1;},play:function(){_.play();},pause:function(){_.pause();},next:function(callback){_.slide(1,false,callback);},prev:function(callback){_.slide(-1,false,callback);},jump:function(distance,callback){_.slide(distance-1,true,callback);},nav:function(target){if(_.navWrapper){_.navWrapper.remove();}
_.options.nav=(target)?target:_.options.nav;_.navigation();},arrows:function(target){if(_.arrowsWrapper){_.arrowsWrapper.remove();}
_.options.arrows=(target)?target:_.options.arrows;_.arrows();}};}
Glide.prototype.build=function(){var _=this;if(_.options.arrows)_.arrows();if(_.options.nav)_.navigation();};Glide.prototype.navigation=function(){var _=this;if(_.slides.length>1){var o=_.options,target=(_.options.nav===true)?_.parent:_.options.nav;_.navWrapper=$('<ul />',{'class':o.navClass}).appendTo(target);var nav=_.navWrapper,item;for(var i=0;i<_.slides.length;i++){item=$('<li class="'+o.navItemClass+'" data-distance="'+i+'">').appendTo(nav);nav[i+1]=item;}
var navChildren=nav.children();navChildren.eq(0).addClass(o.navCurrentItemClass);_.slides.eq(0).addClass(o.slideCurrentItemClass);if(o.navCenter){nav.css({'left':'50%','width':navChildren.outerWidth(true)*navChildren.length,'margin-left':-nav.outerWidth(true)/2});}
navChildren.on('click touchstart',function(e){e.preventDefault();_.slide($(this).data('distance'),true);});}};Glide.prototype.arrows=function(){var _=this;if(_.slides.length>1){var o=_.options,target=(_.options.arrows===true)?_.parent:_.options.arrows;_.arrowsWrapper=$('<div />',{'class':o.arrowsWrapperClass}).appendTo(target);var arrows=_.arrowsWrapper;arrows.right=$('<a />',{'href':'#','class':o.arrowMainClass+' '+o.arrowRightClass,'data-distance':'1','html':o.arrowRightText}).appendTo(arrows);arrows.left=$('<a />',{'href':'#','class':o.arrowMainClass+' '+o.arrowLeftClass,'data-distance':'-1','html':o.arrowLeftText}).appendTo(arrows);arrows.children().on('click touchstart',function(e){e.preventDefault();_.slide($(this).data('distance'),false);});}};Glide.prototype.slide=function(distance,jump,callback){var _=this,currentSlide=(jump)?0:_.currentSlide,slidesLength=-(_.slides.length-1),navCurrentClass=_.options.navCurrentItemClass,slidesSpread=_.slides.spread;slideCurrentClass=_.options.slideCurrentItemClass,_.pause();if(currentSlide===0&&distance===-1){currentSlide=slidesLength;}else if(currentSlide===slidesLength&&distance===1){currentSlide=0;}else{currentSlide=currentSlide+(-distance);}
var translate=slidesSpread*currentSlide+'px';if(_.CSS3support){_.wrapper.css({'-webkit-transform':'translate3d('+translate+', 0px, 0px)','-moz-transform':'translate3d('+translate+', 0px, 0px)','-ms-transform':'translate3d('+translate+', 0px, 0px)','-o-transform':'translate3d('+translate+', 0px, 0px)','transform':'translate3d('+translate+', 0px, 0px)'});}else{_.wrapper.stop().animate({'margin-left':translate},_.options.animationTime);}
if(_.options.nav){_.navWrapper.children().eq(-currentSlide).addClass(navCurrentClass).siblings().removeClass(navCurrentClass);}
_.currentSlide=currentSlide;_.slides.eq(-currentSlide).addClass(slideCurrentClass).siblings().removeClass(slideCurrentClass);if((callback!=='undefined')&&(typeof callback==='function'))callback();_.play();};Glide.prototype.play=function(){var _=this;if(_.options.autoplay){_.auto=setInterval(function(){_.slide(1,false);},_.options.autoplay);}};Glide.prototype.pause=function(){if(this.options.autoplay){this.auto=clearInterval(this.auto);}};Glide.prototype.swipe=function(){var _=this,touch,touchDistance,touchStartX,touchStartY,touchEndX,touchEndY,touchHypotenuse,touchCathetus,touchSin,MathPI=180/Math.PI,subExSx,subEySy,powEX,powEY;_.parent.on('touchstart',function(e){touch=e.originalEvent.touches[0]||e.originalEvent.changedTouches[0];touchStartX=touch.pageX;touchStartY=touch.pageY;});_.parent.on('touchmove',function(e){touch=e.originalEvent.touches[0]||e.originalEvent.changedTouches[0];touchEndX=touch.pageX;touchEndY=touch.pageY;subExSx=touchEndX-touchStartX;subEySy=touchEndY-touchStartY;powEX=Math.abs(subExSx<<2);powEY=Math.abs(subEySy<<2);touchHypotenuse=Math.sqrt(powEX+powEY);touchCathetus=Math.sqrt(powEY);touchSin=Math.asin(touchCathetus/touchHypotenuse);if((touchSin*MathPI)<32)e.preventDefault();});_.parent.on('touchend',function(e){touch=e.originalEvent.touches[0]||e.originalEvent.changedTouches[0];touchDistance=touch.pageX-touchStartX;if(touchDistance>_.options.touchDistance){_.slide(-1);}else if(touchDistance<-_.options.touchDistance){_.slide(1);}});};Glide.prototype.init=function(){var _=this,sliderWidth=_.parent.width();_.slides.spread=sliderWidth;_.wrapper.width(sliderWidth*_.slides.length);_.slides.width(_.slides.spread);if(!isCssSupported("transition")||!isCssSupported("transform"))_.CSS3support=false;};function isCssSupported(declaration){var supported=false,prefixes='Khtml ms O Moz Webkit'.split(' '),clone=document.createElement('div'),declarationCapital=null;declaration=declaration.toLowerCase();if(clone.style[declaration]!==undefined)supported=true;if(supported===false){declarationCapital=declaration.charAt(0).toUpperCase()+declaration.substr(1);for(var i=0;i<prefixes.length;i++){if(clone.style[prefixes[i]+declarationCapital]!==undefined){supported=true;break;}}}
if(window.opera){if(window.opera.version()<13)supported=false;}
return supported;}
$.fn[name]=function(options){return this.each(function(){if(!$.data(this,'api_'+name)){$.data(this,'api_'+name,new Glide($(this),options));}});};})(jQuery,window,document);