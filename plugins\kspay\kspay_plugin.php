<?php
class kspay_plugin
{
	static public $info = [
		'name' => 'kspay',
		'showname' => '快手支付通道',
		'author' => '彩虹',
		'link' => '',
		'types' => ['alipay', 'wxpay'],
		'inputs' => [
			'appurl' => [
				'name' => '快手ID',
				'type' => 'input',
				'note' => '快手ID',
			],
		],
		'select' => null,
		'note' => '注意支付金额最小为0.1元 = 1快币 支付金额若非十的倍数则会导致无法发起支付!<br>将cron.php文件覆盖至易支付根目录后增加计划任务,根据自己需求设置监控推荐为秒级。<br>例:https://你的易支付地址/cron.php?do=kspay&key=监控密钥',
		'bindwxmp' => false,
		'bindwxa' => false,
	];
	static public function submit()
	{
		global $order, $sitename;
		return ['type' => 'jump', 'url' => '/pay/' . $order['typename'] . '/' . TRADE_NO . '/?sitename=' . $sitename];
	}
	static public function mapi()
	{
		global $order;
		$typename = $order['typename'];
		return self::$typename();
	}
	// 支付宝下单
	static public function alipay()
	{
		$code_url = self::qrcode();
		if (strpos($_SERVER['HTTP_USER_AGENT'], 'MicroMessenger') !== false) {
			return ['type' => 'page', 'page' => 'wxopen'];
		} elseif (strpos($_SERVER['HTTP_USER_AGENT'], 'AlipayClient') !== false) {
			return ['type' => 'qrcode', 'page' => 'alipay_wap', 'url' => $code_url];
		} elseif (checkmobile() && !isset($_GET['qrcode'])) {
			return ['type' => 'qrcode', 'page' => 'alipay_qrcode', 'url' => $code_url];
		} else {
			return ['type' => 'qrcode', 'page' => 'alipay_qrcode', 'url' => $code_url];
		}
	}
	// 微信下单
	static public function wxpay()
	{
		$code_url = self::qrcode();
		if (strpos($_SERVER['HTTP_USER_AGENT'], 'MicroMessenger') !== false) {
			return ['type' => 'jump', 'url' => $code_url];
		} elseif (checkmobile() && !isset($_GET['qrcode'])) {
			return ['type' => 'qrcode', 'page' => 'wxpay_wap', 'url' => $code_url];
		} else {
			return ['type' => 'qrcode', 'page' => 'wxpay_qrcode', 'url' => $code_url];
		}
	}

	static public function qrcode()
	{
		global $channel, $order, $ordername, $DB;
		$kscsId = self::kscsId($channel['appurl']);
		$amount = $order['money'];
		$orderData = [
			'ksCoin' => ($amount * 10),
			'userId' => $kscsId,
			'kpn' => 'KUAISHOU',
			'customize' => 'false',
			'source' => 'PC_WEB',
			'kpf' => 'PC_WEB',
			'fen' => ($amount * 100)
		];
		$orderResponse = self::curlRequest(
			'https://pay.ssl.kuaishou.com/rest/k/pay/kscoin/deposit/nlogin/kspay/cashier',
			'POST',
			['User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0'],
			$orderData
		);
		$outOrderNo = $orderResponse['outOrderNo'] ?? '';
		$merchantId = $orderResponse['merchantId'] ?? '';
		$ksOrderId = $orderResponse['ksOrderId'] ?? '';
		$DB->query('INSERT INTO pre_order (trade_no, api_trade_no) VALUES (?, ?) ON DUPLICATE KEY UPDATE api_trade_no = VALUES(api_trade_no)', [TRADE_NO, $ksOrderId]);
		$qrcodeUrlResponse = self::curlRequest(
			"https://www.kuaishoupay.com/pay/order/pc/trade/cashier?merchant_id={$merchantId}&out_order_no={$outOrderNo}&js_sdk_version=3.0.4",
			'GET'
		);
		$qrcode_url = $qrcodeUrlResponse['qrcode_url'] ?? '';
		return $qrcode_url;
	}
	// 状态监控-计划任务版
	static public function btjk($payid, array $channel, $ksOrderId)
	{
		global $DB;
		$headers = ['User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0'];
		$confirmResponse = self::curlRequest(
			'https://pay.ssl.kuaishou.com/rest/k/pay/kscoin/deposit/nlogin/kspay/confirm',
			'POST',
			$headers,
			['ksOrderId' => $ksOrderId, 'kpn' => 'KUAISHOU', 'kpf' => 'PC_WEB']
		);
		if (empty($confirmResponse['result']) || $confirmResponse['result'] != 1) {
			return json_encode(["StatusCode" => 0, "TradeNo" => $payid, "TradeStatus" => "订单数据查询中!"], JSON_UNESCAPED_UNICODE);
		}
		$order = $DB->getRow('select * from pre_order where trade_no = ? limit 1', [$payid]);
		if (empty($order)) {
			return json_encode(["StatusCode" => -1, "TradeNo" => $payid, "TradeStatus" => "订单不存在!"], JSON_UNESCAPED_UNICODE);
		}
		processNotify($order, $payid);
		return json_encode(["StatusCode" => 1, "TradeNo" => $payid, "TradeStatus" => "订单已支付!"], JSON_UNESCAPED_UNICODE);
	}
	// 获取快手初始ID
	static public function kscsId($ksId)
	{
		$userInfo = self::curlRequest(
			'https://pay.ssl.kuaishou.com/rest/k/pay/userInfo',
			'POST',
			['User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0'],
			['id' => $ksId]
		);
		return $userInfo['userId'] ?? '';
	}
	// 请求
	static public function curlRequest($url, $method, $headers = [], $data = [])
	{
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		if ($method === 'POST') {
			curl_setopt($ch, CURLOPT_POST, true);
			curl_setopt($ch, CURLOPT_POSTFIELDS, $data ? json_encode($data) : null);
			$headers[] = 'Content-Type: application/json;charset=utf-8';
		} elseif ($method === 'GET') {
			if (!empty($data)) {
				$url .= '?' . http_build_query($data);
			}
		}
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		$response = curl_exec($ch);
		curl_close($ch);
		return json_decode($response, true);
	}
}