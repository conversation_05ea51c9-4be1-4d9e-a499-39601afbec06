/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.21.4
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):(t="undefined"!=typeof globalThis?globalThis:t||self).BootstrapTable=e(t.jQuery)}(this,(function(t){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(t)}function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function n(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=n.key,r=void 0,"symbol"==typeof(r=function(t,e){if("object"!=typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(o,"string"))?r:String(r)),n)}var o,r}function o(t,e,i){return e&&n(t.prototype,e),i&&n(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t}function r(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var i=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=i){var n,o,r,a,s=[],l=!0,c=!1;try{if(r=(i=i.call(t)).next,0===e){if(Object(i)!==i)return;l=!1}else for(;!(l=(n=r.call(i)).done)&&(s.push(n.value),s.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=i.return&&(a=i.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(t,e)||s(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(t){return function(t){if(Array.isArray(t))return l(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||s(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,e){if(t){if("string"==typeof t)return l(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?l(t,e):void 0}}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function c(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=s(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,a=!0,l=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return a=t.done,t},e:function(t){l=!0,r=t},f:function(){try{a||null==i.return||i.return()}finally{if(l)throw r}}}}var h="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},u=function(t){return t&&t.Math==Math&&t},d=u("object"==typeof globalThis&&globalThis)||u("object"==typeof window&&window)||u("object"==typeof self&&self)||u("object"==typeof h&&h)||function(){return this}()||Function("return this")(),p={},f=function(t){try{return!!t()}catch(t){return!0}},g=!f((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),v=!f((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),b=v,m=Function.prototype.call,y=b?m.bind(m):function(){return m.apply(m,arguments)},w={},S={}.propertyIsEnumerable,x=Object.getOwnPropertyDescriptor,k=x&&!S.call({1:2},1);w.f=k?function(t){var e=x(this,t);return!!e&&e.enumerable}:S;var O,C,T=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},I=v,P=Function.prototype,A=P.call,$=I&&P.bind.bind(A,A),R=I?$:function(t){return function(){return A.apply(t,arguments)}},E=R,j=E({}.toString),_=E("".slice),F=function(t){return _(j(t),8,-1)},D=f,N=F,V=Object,B=R("".split),L=D((function(){return!V("z").propertyIsEnumerable(0)}))?function(t){return"String"==N(t)?B(t,""):V(t)}:V,H=function(t){return null==t},M=H,U=TypeError,z=function(t){if(M(t))throw U("Can't call method on "+t);return t},q=L,W=z,G=function(t){return q(W(t))},K="object"==typeof document&&document.all,Y={all:K,IS_HTMLDDA:void 0===K&&void 0!==K},J=Y.all,X=Y.IS_HTMLDDA?function(t){return"function"==typeof t||t===J}:function(t){return"function"==typeof t},Q=X,Z=Y.all,tt=Y.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:Q(t)||t===Z}:function(t){return"object"==typeof t?null!==t:Q(t)},et=d,it=X,nt=function(t){return it(t)?t:void 0},ot=function(t,e){return arguments.length<2?nt(et[t]):et[t]&&et[t][e]},rt=R({}.isPrototypeOf),at="undefined"!=typeof navigator&&String(navigator.userAgent)||"",st=d,lt=at,ct=st.process,ht=st.Deno,ut=ct&&ct.versions||ht&&ht.version,dt=ut&&ut.v8;dt&&(C=(O=dt.split("."))[0]>0&&O[0]<4?1:+(O[0]+O[1])),!C&&lt&&(!(O=lt.match(/Edge\/(\d+)/))||O[1]>=74)&&(O=lt.match(/Chrome\/(\d+)/))&&(C=+O[1]);var pt=C,ft=pt,gt=f,vt=!!Object.getOwnPropertySymbols&&!gt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),bt=vt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,mt=ot,yt=X,wt=rt,St=Object,xt=bt?function(t){return"symbol"==typeof t}:function(t){var e=mt("Symbol");return yt(e)&&wt(e.prototype,St(t))},kt=String,Ot=function(t){try{return kt(t)}catch(t){return"Object"}},Ct=X,Tt=Ot,It=TypeError,Pt=function(t){if(Ct(t))return t;throw It(Tt(t)+" is not a function")},At=Pt,$t=H,Rt=function(t,e){var i=t[e];return $t(i)?void 0:At(i)},Et=y,jt=X,_t=tt,Ft=TypeError,Dt={},Nt={get exports(){return Dt},set exports(t){Dt=t}},Vt=d,Bt=Object.defineProperty,Lt=function(t,e){try{Bt(Vt,t,{value:e,configurable:!0,writable:!0})}catch(i){Vt[t]=e}return e},Ht=Lt,Mt="__core-js_shared__",Ut=d[Mt]||Ht(Mt,{}),zt=Ut;(Nt.exports=function(t,e){return zt[t]||(zt[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.29.0",mode:"global",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.29.0/LICENSE",source:"https://github.com/zloirock/core-js"});var qt=z,Wt=Object,Gt=function(t){return Wt(qt(t))},Kt=Gt,Yt=R({}.hasOwnProperty),Jt=Object.hasOwn||function(t,e){return Yt(Kt(t),e)},Xt=R,Qt=0,Zt=Math.random(),te=Xt(1..toString),ee=function(t){return"Symbol("+(void 0===t?"":t)+")_"+te(++Qt+Zt,36)},ie=Dt,ne=Jt,oe=ee,re=vt,ae=bt,se=d.Symbol,le=ie("wks"),ce=ae?se.for||se:se&&se.withoutSetter||oe,he=function(t){return ne(le,t)||(le[t]=re&&ne(se,t)?se[t]:ce("Symbol."+t)),le[t]},ue=y,de=tt,pe=xt,fe=Rt,ge=function(t,e){var i,n;if("string"===e&&jt(i=t.toString)&&!_t(n=Et(i,t)))return n;if(jt(i=t.valueOf)&&!_t(n=Et(i,t)))return n;if("string"!==e&&jt(i=t.toString)&&!_t(n=Et(i,t)))return n;throw Ft("Can't convert object to primitive value")},ve=TypeError,be=he("toPrimitive"),me=function(t,e){if(!de(t)||pe(t))return t;var i,n=fe(t,be);if(n){if(void 0===e&&(e="default"),i=ue(n,t,e),!de(i)||pe(i))return i;throw ve("Can't convert object to primitive value")}return void 0===e&&(e="number"),ge(t,e)},ye=me,we=xt,Se=function(t){var e=ye(t,"string");return we(e)?e:e+""},xe=tt,ke=d.document,Oe=xe(ke)&&xe(ke.createElement),Ce=function(t){return Oe?ke.createElement(t):{}},Te=Ce,Ie=!g&&!f((function(){return 7!=Object.defineProperty(Te("div"),"a",{get:function(){return 7}}).a})),Pe=g,Ae=y,$e=w,Re=T,Ee=G,je=Se,_e=Jt,Fe=Ie,De=Object.getOwnPropertyDescriptor;p.f=Pe?De:function(t,e){if(t=Ee(t),e=je(e),Fe)try{return De(t,e)}catch(t){}if(_e(t,e))return Re(!Ae($e.f,t,e),t[e])};var Ne={},Ve=g&&f((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Be=tt,Le=String,He=TypeError,Me=function(t){if(Be(t))return t;throw He(Le(t)+" is not an object")},Ue=g,ze=Ie,qe=Ve,We=Me,Ge=Se,Ke=TypeError,Ye=Object.defineProperty,Je=Object.getOwnPropertyDescriptor,Xe="enumerable",Qe="configurable",Ze="writable";Ne.f=Ue?qe?function(t,e,i){if(We(t),e=Ge(e),We(i),"function"==typeof t&&"prototype"===e&&"value"in i&&Ze in i&&!i.writable){var n=Je(t,e);n&&n.writable&&(t[e]=i.value,i={configurable:Qe in i?i.configurable:n.configurable,enumerable:Xe in i?i.enumerable:n.enumerable,writable:!1})}return Ye(t,e,i)}:Ye:function(t,e,i){if(We(t),e=Ge(e),We(i),ze)try{return Ye(t,e,i)}catch(t){}if("get"in i||"set"in i)throw Ke("Accessors not supported");return"value"in i&&(t[e]=i.value),t};var ti=Ne,ei=T,ii=g?function(t,e,i){return ti.f(t,e,ei(1,i))}:function(t,e,i){return t[e]=i,t},ni={},oi={get exports(){return ni},set exports(t){ni=t}},ri=g,ai=Jt,si=Function.prototype,li=ri&&Object.getOwnPropertyDescriptor,ci=ai(si,"name"),hi={EXISTS:ci,PROPER:ci&&"something"===function(){}.name,CONFIGURABLE:ci&&(!ri||ri&&li(si,"name").configurable)},ui=X,di=Ut,pi=R(Function.toString);ui(di.inspectSource)||(di.inspectSource=function(t){return pi(t)});var fi,gi,vi,bi=di.inspectSource,mi=X,yi=d.WeakMap,wi=mi(yi)&&/native code/.test(String(yi)),Si=ee,xi=Dt("keys"),ki=function(t){return xi[t]||(xi[t]=Si(t))},Oi={},Ci=wi,Ti=d,Ii=tt,Pi=ii,Ai=Jt,$i=Ut,Ri=ki,Ei=Oi,ji="Object already initialized",_i=Ti.TypeError,Fi=Ti.WeakMap;if(Ci||$i.state){var Di=$i.state||($i.state=new Fi);Di.get=Di.get,Di.has=Di.has,Di.set=Di.set,fi=function(t,e){if(Di.has(t))throw _i(ji);return e.facade=t,Di.set(t,e),e},gi=function(t){return Di.get(t)||{}},vi=function(t){return Di.has(t)}}else{var Ni=Ri("state");Ei[Ni]=!0,fi=function(t,e){if(Ai(t,Ni))throw _i(ji);return e.facade=t,Pi(t,Ni,e),e},gi=function(t){return Ai(t,Ni)?t[Ni]:{}},vi=function(t){return Ai(t,Ni)}}var Vi={set:fi,get:gi,has:vi,enforce:function(t){return vi(t)?gi(t):fi(t,{})},getterFor:function(t){return function(e){var i;if(!Ii(e)||(i=gi(e)).type!==t)throw _i("Incompatible receiver, "+t+" required");return i}}},Bi=R,Li=f,Hi=X,Mi=Jt,Ui=g,zi=hi.CONFIGURABLE,qi=bi,Wi=Vi.enforce,Gi=Vi.get,Ki=String,Yi=Object.defineProperty,Ji=Bi("".slice),Xi=Bi("".replace),Qi=Bi([].join),Zi=Ui&&!Li((function(){return 8!==Yi((function(){}),"length",{value:8}).length})),tn=String(String).split("String"),en=oi.exports=function(t,e,i){"Symbol("===Ji(Ki(e),0,7)&&(e="["+Xi(Ki(e),/^Symbol\(([^)]*)\)/,"$1")+"]"),i&&i.getter&&(e="get "+e),i&&i.setter&&(e="set "+e),(!Mi(t,"name")||zi&&t.name!==e)&&(Ui?Yi(t,"name",{value:e,configurable:!0}):t.name=e),Zi&&i&&Mi(i,"arity")&&t.length!==i.arity&&Yi(t,"length",{value:i.arity});try{i&&Mi(i,"constructor")&&i.constructor?Ui&&Yi(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var n=Wi(t);return Mi(n,"source")||(n.source=Qi(tn,"string"==typeof e?e:"")),t};Function.prototype.toString=en((function(){return Hi(this)&&Gi(this).source||qi(this)}),"toString");var nn=X,on=Ne,rn=ni,an=Lt,sn=function(t,e,i,n){n||(n={});var o=n.enumerable,r=void 0!==n.name?n.name:e;if(nn(i)&&rn(i,r,n),n.global)o?t[e]=i:an(e,i);else{try{n.unsafe?t[e]&&(o=!0):delete t[e]}catch(t){}o?t[e]=i:on.f(t,e,{value:i,enumerable:!1,configurable:!n.nonConfigurable,writable:!n.nonWritable})}return t},ln={},cn=Math.ceil,hn=Math.floor,un=Math.trunc||function(t){var e=+t;return(e>0?hn:cn)(e)},dn=function(t){var e=+t;return e!=e||0===e?0:un(e)},pn=dn,fn=Math.max,gn=Math.min,vn=function(t,e){var i=pn(t);return i<0?fn(i+e,0):gn(i,e)},bn=dn,mn=Math.min,yn=function(t){return t>0?mn(bn(t),9007199254740991):0},wn=yn,Sn=function(t){return wn(t.length)},xn=G,kn=vn,On=Sn,Cn=function(t){return function(e,i,n){var o,r=xn(e),a=On(r),s=kn(n,a);if(t&&i!=i){for(;a>s;)if((o=r[s++])!=o)return!0}else for(;a>s;s++)if((t||s in r)&&r[s]===i)return t||s||0;return!t&&-1}},Tn={includes:Cn(!0),indexOf:Cn(!1)},In=Jt,Pn=G,An=Tn.indexOf,$n=Oi,Rn=R([].push),En=function(t,e){var i,n=Pn(t),o=0,r=[];for(i in n)!In($n,i)&&In(n,i)&&Rn(r,i);for(;e.length>o;)In(n,i=e[o++])&&(~An(r,i)||Rn(r,i));return r},jn=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],_n=En,Fn=jn.concat("length","prototype");ln.f=Object.getOwnPropertyNames||function(t){return _n(t,Fn)};var Dn={};Dn.f=Object.getOwnPropertySymbols;var Nn=ot,Vn=ln,Bn=Dn,Ln=Me,Hn=R([].concat),Mn=Nn("Reflect","ownKeys")||function(t){var e=Vn.f(Ln(t)),i=Bn.f;return i?Hn(e,i(t)):e},Un=Jt,zn=Mn,qn=p,Wn=Ne,Gn=f,Kn=X,Yn=/#|\.prototype\./,Jn=function(t,e){var i=Qn[Xn(t)];return i==to||i!=Zn&&(Kn(e)?Gn(e):!!e)},Xn=Jn.normalize=function(t){return String(t).replace(Yn,".").toLowerCase()},Qn=Jn.data={},Zn=Jn.NATIVE="N",to=Jn.POLYFILL="P",eo=Jn,io=d,no=p.f,oo=ii,ro=sn,ao=Lt,so=function(t,e,i){for(var n=zn(e),o=Wn.f,r=qn.f,a=0;a<n.length;a++){var s=n[a];Un(t,s)||i&&Un(i,s)||o(t,s,r(e,s))}},lo=eo,co=function(t,e){var i,n,o,r,a,s=t.target,l=t.global,c=t.stat;if(i=l?io:c?io[s]||ao(s,{}):(io[s]||{}).prototype)for(n in e){if(r=e[n],o=t.dontCallGetSet?(a=no(i,n))&&a.value:i[n],!lo(l?n:s+(c?".":"#")+n,t.forced)&&void 0!==o){if(typeof r==typeof o)continue;so(r,o)}(t.sham||o&&o.sham)&&oo(r,"sham",!0),ro(i,n,r,t)}},ho=En,uo=jn,po=Object.keys||function(t){return ho(t,uo)},fo=g,go=R,vo=y,bo=f,mo=po,yo=Dn,wo=w,So=Gt,xo=L,ko=Object.assign,Oo=Object.defineProperty,Co=go([].concat),To=!ko||bo((function(){if(fo&&1!==ko({b:1},ko(Oo({},"a",{enumerable:!0,get:function(){Oo(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},i=Symbol(),n="abcdefghijklmnopqrst";return t[i]=7,n.split("").forEach((function(t){e[t]=t})),7!=ko({},t)[i]||mo(ko({},e)).join("")!=n}))?function(t,e){for(var i=So(t),n=arguments.length,o=1,r=yo.f,a=wo.f;n>o;)for(var s,l=xo(arguments[o++]),c=r?Co(mo(l),r(l)):mo(l),h=c.length,u=0;h>u;)s=c[u++],fo&&!vo(a,l,s)||(i[s]=l[s]);return i}:ko,Io=To;co({target:"Object",stat:!0,arity:2,forced:Object.assign!==Io},{assign:Io});var Po={};Po[he("toStringTag")]="z";var Ao="[object z]"===String(Po),$o=Ao,Ro=X,Eo=F,jo=he("toStringTag"),_o=Object,Fo="Arguments"==Eo(function(){return arguments}()),Do=$o?Eo:function(t){var e,i,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(i=function(t,e){try{return t[e]}catch(t){}}(e=_o(t),jo))?i:Fo?Eo(e):"Object"==(n=Eo(e))&&Ro(e.callee)?"Arguments":n},No=Do,Vo=String,Bo=function(t){if("Symbol"===No(t))throw TypeError("Cannot convert a Symbol value to a string");return Vo(t)},Lo="\t\n\v\f\r                　\u2028\u2029\ufeff",Ho=z,Mo=Bo,Uo=Lo,zo=R("".replace),qo=RegExp("^["+Uo+"]+"),Wo=RegExp("(^|[^"+Uo+"])["+Uo+"]+$"),Go=function(t){return function(e){var i=Mo(Ho(e));return 1&t&&(i=zo(i,qo,"")),2&t&&(i=zo(i,Wo,"$1")),i}},Ko={start:Go(1),end:Go(2),trim:Go(3)},Yo=hi.PROPER,Jo=f,Xo=Lo,Qo=Ko.trim;co({target:"String",proto:!0,forced:function(t){return Jo((function(){return!!Xo[t]()||"​᠎"!=="​᠎"[t]()||Yo&&Xo[t].name!==t}))}("trim")},{trim:function(){return Qo(this)}});var Zo=f,tr=function(t,e){var i=[][t];return!!i&&Zo((function(){i.call(null,e||function(){return 1},1)}))},er=co,ir=L,nr=G,or=tr,rr=R([].join);er({target:"Array",proto:!0,forced:ir!=Object||!or("join",",")},{join:function(t){return rr(nr(this),void 0===t?",":t)}});var ar=Me,sr=function(){var t=ar(this),e="";return t.hasIndices&&(e+="d"),t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.unicodeSets&&(e+="v"),t.sticky&&(e+="y"),e},lr=f,cr=d.RegExp,hr=lr((function(){var t=cr("a","y");return t.lastIndex=2,null!=t.exec("abcd")})),ur=hr||lr((function(){return!cr("a","y").sticky})),dr={BROKEN_CARET:hr||lr((function(){var t=cr("^r","gy");return t.lastIndex=2,null!=t.exec("str")})),MISSED_STICKY:ur,UNSUPPORTED_Y:hr},pr={},fr=g,gr=Ve,vr=Ne,br=Me,mr=G,yr=po;pr.f=fr&&!gr?Object.defineProperties:function(t,e){br(t);for(var i,n=mr(e),o=yr(e),r=o.length,a=0;r>a;)vr.f(t,i=o[a++],n[i]);return t};var wr,Sr=ot("document","documentElement"),xr=Me,kr=pr,Or=jn,Cr=Oi,Tr=Sr,Ir=Ce,Pr=ki("IE_PROTO"),Ar=function(){},$r=function(t){return"<script>"+t+"</"+"script>"},Rr=function(t){t.write($r("")),t.close();var e=t.parentWindow.Object;return t=null,e},Er=function(){try{wr=new ActiveXObject("htmlfile")}catch(t){}var t,e;Er="undefined"!=typeof document?document.domain&&wr?Rr(wr):((e=Ir("iframe")).style.display="none",Tr.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write($r("document.F=Object")),t.close(),t.F):Rr(wr);for(var i=Or.length;i--;)delete Er.prototype[Or[i]];return Er()};Cr[Pr]=!0;var jr=Object.create||function(t,e){var i;return null!==t?(Ar.prototype=xr(t),i=new Ar,Ar.prototype=null,i[Pr]=t):i=Er(),void 0===e?i:kr.f(i,e)},_r=f,Fr=d.RegExp,Dr=_r((function(){var t=Fr(".","s");return!(t.dotAll&&t.exec("\n")&&"s"===t.flags)})),Nr=f,Vr=d.RegExp,Br=Nr((function(){var t=Vr("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")})),Lr=y,Hr=R,Mr=Bo,Ur=sr,zr=dr,qr=jr,Wr=Vi.get,Gr=Dr,Kr=Br,Yr=Dt("native-string-replace",String.prototype.replace),Jr=RegExp.prototype.exec,Xr=Jr,Qr=Hr("".charAt),Zr=Hr("".indexOf),ta=Hr("".replace),ea=Hr("".slice),ia=function(){var t=/a/,e=/b*/g;return Lr(Jr,t,"a"),Lr(Jr,e,"a"),0!==t.lastIndex||0!==e.lastIndex}(),na=zr.BROKEN_CARET,oa=void 0!==/()??/.exec("")[1];(ia||oa||na||Gr||Kr)&&(Xr=function(t){var e,i,n,o,r,a,s,l=this,c=Wr(l),h=Mr(t),u=c.raw;if(u)return u.lastIndex=l.lastIndex,e=Lr(Xr,u,h),l.lastIndex=u.lastIndex,e;var d=c.groups,p=na&&l.sticky,f=Lr(Ur,l),g=l.source,v=0,b=h;if(p&&(f=ta(f,"y",""),-1===Zr(f,"g")&&(f+="g"),b=ea(h,l.lastIndex),l.lastIndex>0&&(!l.multiline||l.multiline&&"\n"!==Qr(h,l.lastIndex-1))&&(g="(?: "+g+")",b=" "+b,v++),i=new RegExp("^(?:"+g+")",f)),oa&&(i=new RegExp("^"+g+"$(?!\\s)",f)),ia&&(n=l.lastIndex),o=Lr(Jr,p?i:l,b),p?o?(o.input=ea(o.input,v),o[0]=ea(o[0],v),o.index=l.lastIndex,l.lastIndex+=o[0].length):l.lastIndex=0:ia&&o&&(l.lastIndex=l.global?o.index+o[0].length:n),oa&&o&&o.length>1&&Lr(Yr,o[0],i,(function(){for(r=1;r<arguments.length-2;r++)void 0===arguments[r]&&(o[r]=void 0)})),o&&d)for(o.groups=a=qr(null),r=0;r<d.length;r++)a[(s=d[r])[0]]=o[s[1]];return o});var ra=Xr;co({target:"RegExp",proto:!0,forced:/./.exec!==ra},{exec:ra});var aa=v,sa=Function.prototype,la=sa.apply,ca=sa.call,ha="object"==typeof Reflect&&Reflect.apply||(aa?ca.bind(la):function(){return ca.apply(la,arguments)}),ua=F,da=R,pa=function(t){if("Function"===ua(t))return da(t)},fa=pa,ga=sn,va=ra,ba=f,ma=he,ya=ii,wa=ma("species"),Sa=RegExp.prototype,xa=function(t,e,i,n){var o=ma(t),r=!ba((function(){var e={};return e[o]=function(){return 7},7!=""[t](e)})),a=r&&!ba((function(){var e=!1,i=/a/;return"split"===t&&((i={}).constructor={},i.constructor[wa]=function(){return i},i.flags="",i[o]=/./[o]),i.exec=function(){return e=!0,null},i[o](""),!e}));if(!r||!a||i){var s=fa(/./[o]),l=e(o,""[t],(function(t,e,i,n,o){var a=fa(t),l=e.exec;return l===va||l===Sa.exec?r&&!o?{done:!0,value:s(e,i,n)}:{done:!0,value:a(i,e,n)}:{done:!1}}));ga(String.prototype,t,l[0]),ga(Sa,o,l[1])}n&&ya(Sa[o],"sham",!0)},ka=tt,Oa=F,Ca=he("match"),Ta=function(t){var e;return ka(t)&&(void 0!==(e=t[Ca])?!!e:"RegExp"==Oa(t))},Ia=R,Pa=f,Aa=X,$a=Do,Ra=bi,Ea=function(){},ja=[],_a=ot("Reflect","construct"),Fa=/^\s*(?:class|function)\b/,Da=Ia(Fa.exec),Na=!Fa.exec(Ea),Va=function(t){if(!Aa(t))return!1;try{return _a(Ea,ja,t),!0}catch(t){return!1}},Ba=function(t){if(!Aa(t))return!1;switch($a(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Na||!!Da(Fa,Ra(t))}catch(t){return!0}};Ba.sham=!0;var La=!_a||Pa((function(){var t;return Va(Va.call)||!Va(Object)||!Va((function(){t=!0}))||t}))?Ba:Va,Ha=La,Ma=Ot,Ua=TypeError,za=Me,qa=function(t){if(Ha(t))return t;throw Ua(Ma(t)+" is not a constructor")},Wa=H,Ga=he("species"),Ka=R,Ya=dn,Ja=Bo,Xa=z,Qa=Ka("".charAt),Za=Ka("".charCodeAt),ts=Ka("".slice),es=function(t){return function(e,i){var n,o,r=Ja(Xa(e)),a=Ya(i),s=r.length;return a<0||a>=s?t?"":void 0:(n=Za(r,a))<55296||n>56319||a+1===s||(o=Za(r,a+1))<56320||o>57343?t?Qa(r,a):n:t?ts(r,a,a+2):o-56320+(n-55296<<10)+65536}},is={codeAt:es(!1),charAt:es(!0)}.charAt,ns=function(t,e,i){return e+(i?is(t,e).length:1)},os=Se,rs=Ne,as=T,ss=function(t,e,i){var n=os(e);n in t?rs.f(t,n,as(0,i)):t[n]=i},ls=vn,cs=Sn,hs=ss,us=Array,ds=Math.max,ps=function(t,e,i){for(var n=cs(t),o=ls(e,n),r=ls(void 0===i?n:i,n),a=us(ds(r-o,0)),s=0;o<r;o++,s++)hs(a,s,t[o]);return a.length=s,a},fs=y,gs=Me,vs=X,bs=F,ms=ra,ys=TypeError,ws=function(t,e){var i=t.exec;if(vs(i)){var n=fs(i,t,e);return null!==n&&gs(n),n}if("RegExp"===bs(t))return fs(ms,t,e);throw ys("RegExp#exec called on incompatible receiver")},Ss=ha,xs=y,ks=R,Os=xa,Cs=Me,Ts=H,Is=Ta,Ps=z,As=function(t,e){var i,n=za(t).constructor;return void 0===n||Wa(i=za(n)[Ga])?e:qa(i)},$s=ns,Rs=yn,Es=Bo,js=Rt,_s=ps,Fs=ws,Ds=ra,Ns=f,Vs=dr.UNSUPPORTED_Y,Bs=4294967295,Ls=Math.min,Hs=[].push,Ms=ks(/./.exec),Us=ks(Hs),zs=ks("".slice),qs=!Ns((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var i="ab".split(t);return 2!==i.length||"a"!==i[0]||"b"!==i[1]}));Os("split",(function(t,e,i){var n;return n="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(t,i){var n=Es(Ps(this)),o=void 0===i?Bs:i>>>0;if(0===o)return[];if(void 0===t)return[n];if(!Is(t))return xs(e,n,t,o);for(var r,a,s,l=[],c=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),h=0,u=new RegExp(t.source,c+"g");(r=xs(Ds,u,n))&&!((a=u.lastIndex)>h&&(Us(l,zs(n,h,r.index)),r.length>1&&r.index<n.length&&Ss(Hs,l,_s(r,1)),s=r[0].length,h=a,l.length>=o));)u.lastIndex===r.index&&u.lastIndex++;return h===n.length?!s&&Ms(u,"")||Us(l,""):Us(l,zs(n,h)),l.length>o?_s(l,0,o):l}:"0".split(void 0,0).length?function(t,i){return void 0===t&&0===i?[]:xs(e,this,t,i)}:e,[function(e,i){var o=Ps(this),r=Ts(e)?void 0:js(e,t);return r?xs(r,e,o,i):xs(n,Es(o),e,i)},function(t,o){var r=Cs(this),a=Es(t),s=i(n,r,a,o,n!==e);if(s.done)return s.value;var l=As(r,RegExp),c=r.unicode,h=(r.ignoreCase?"i":"")+(r.multiline?"m":"")+(r.unicode?"u":"")+(Vs?"g":"y"),u=new l(Vs?"^(?:"+r.source+")":r,h),d=void 0===o?Bs:o>>>0;if(0===d)return[];if(0===a.length)return null===Fs(u,a)?[a]:[];for(var p=0,f=0,g=[];f<a.length;){u.lastIndex=Vs?0:f;var v,b=Fs(u,Vs?zs(a,f):a);if(null===b||(v=Ls(Rs(u.lastIndex+(Vs?f:0)),a.length))===p)f=$s(a,f,c);else{if(Us(g,zs(a,p,f)),g.length===d)return g;for(var m=1;m<=b.length-1;m++)if(Us(g,b[m]),g.length===d)return g;f=p=v}}return Us(g,zs(a,p)),g}]}),!qs,Vs);var Ws=g,Gs=R,Ks=po,Ys=G,Js=Gs(w.f),Xs=Gs([].push),Qs=function(t){return function(e){for(var i,n=Ys(e),o=Ks(n),r=o.length,a=0,s=[];r>a;)i=o[a++],Ws&&!Js(n,i)||Xs(s,t?[i,n[i]]:n[i]);return s}},Zs={entries:Qs(!0),values:Qs(!1)}.entries;co({target:"Object",stat:!0},{entries:function(t){return Zs(t)}});var tl=he,el=jr,il=Ne.f,nl=tl("unscopables"),ol=Array.prototype;null==ol[nl]&&il(ol,nl,{configurable:!0,value:el(null)});var rl=function(t){ol[nl][t]=!0},al=Tn.includes,sl=rl;co({target:"Array",proto:!0,forced:f((function(){return!Array(1).includes()}))},{includes:function(t){return al(this,t,arguments.length>1?arguments[1]:void 0)}}),sl("includes");var ll=F,cl=Array.isArray||function(t){return"Array"==ll(t)},hl=TypeError,ul=function(t){if(t>9007199254740991)throw hl("Maximum allowed index exceeded");return t},dl=cl,pl=La,fl=tt,gl=he("species"),vl=Array,bl=function(t){var e;return dl(t)&&(e=t.constructor,(pl(e)&&(e===vl||dl(e.prototype))||fl(e)&&null===(e=e[gl]))&&(e=void 0)),void 0===e?vl:e},ml=function(t,e){return new(bl(t))(0===e?0:e)},yl=f,wl=pt,Sl=he("species"),xl=function(t){return wl>=51||!yl((function(){var e=[];return(e.constructor={})[Sl]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},kl=co,Ol=f,Cl=cl,Tl=tt,Il=Gt,Pl=Sn,Al=ul,$l=ss,Rl=ml,El=xl,jl=pt,_l=he("isConcatSpreadable"),Fl=jl>=51||!Ol((function(){var t=[];return t[_l]=!1,t.concat()[0]!==t})),Dl=function(t){if(!Tl(t))return!1;var e=t[_l];return void 0!==e?!!e:Cl(t)};kl({target:"Array",proto:!0,arity:1,forced:!Fl||!El("concat")},{concat:function(t){var e,i,n,o,r,a=Il(this),s=Rl(a,0),l=0;for(e=-1,n=arguments.length;e<n;e++)if(Dl(r=-1===e?a:arguments[e]))for(o=Pl(r),Al(l+o),i=0;i<o;i++,l++)i in r&&$l(s,l,r[i]);else Al(l+1),$l(s,l++,r);return s.length=l,s}});var Nl=Pt,Vl=v,Bl=pa(pa.bind),Ll=function(t,e){return Nl(t),void 0===e?t:Vl?Bl(t,e):function(){return t.apply(e,arguments)}},Hl=L,Ml=Gt,Ul=Sn,zl=ml,ql=R([].push),Wl=function(t){var e=1==t,i=2==t,n=3==t,o=4==t,r=6==t,a=7==t,s=5==t||r;return function(l,c,h,u){for(var d,p,f=Ml(l),g=Hl(f),v=Ll(c,h),b=Ul(g),m=0,y=u||zl,w=e?y(l,b):i||a?y(l,0):void 0;b>m;m++)if((s||m in g)&&(p=v(d=g[m],m,f),t))if(e)w[m]=p;else if(p)switch(t){case 3:return!0;case 5:return d;case 6:return m;case 2:ql(w,d)}else switch(t){case 4:return!1;case 7:ql(w,d)}return r?-1:n||o?o:w}},Gl={forEach:Wl(0),map:Wl(1),filter:Wl(2),some:Wl(3),every:Wl(4),find:Wl(5),findIndex:Wl(6),filterReject:Wl(7)},Kl=co,Yl=Gl.find,Jl=rl,Xl="find",Ql=!0;Xl in[]&&Array(1).find((function(){Ql=!1})),Kl({target:"Array",proto:!0,forced:Ql},{find:function(t){return Yl(this,t,arguments.length>1?arguments[1]:void 0)}}),Jl(Xl);var Zl=Do,tc=Ao?{}.toString:function(){return"[object "+Zl(this)+"]"};Ao||sn(Object.prototype,"toString",tc,{unsafe:!0});var ec=Ta,ic=TypeError,nc=function(t){if(ec(t))throw ic("The method doesn't accept regular expressions");return t},oc=he("match"),rc=function(t){var e=/./;try{"/./"[t](e)}catch(i){try{return e[oc]=!1,"/./"[t](e)}catch(t){}}return!1},ac=co,sc=nc,lc=z,cc=Bo,hc=rc,uc=R("".indexOf);ac({target:"String",proto:!0,forced:!hc("includes")},{includes:function(t){return!!~uc(cc(lc(this)),cc(sc(t)),arguments.length>1?arguments[1]:void 0)}});var dc={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},pc=Ce("span").classList,fc=pc&&pc.constructor&&pc.constructor.prototype,gc=fc===Object.prototype?void 0:fc,vc=Gl.forEach,bc=tr("forEach")?[].forEach:function(t){return vc(this,t,arguments.length>1?arguments[1]:void 0)},mc=d,yc=dc,wc=gc,Sc=bc,xc=ii,kc=function(t){if(t&&t.forEach!==Sc)try{xc(t,"forEach",Sc)}catch(e){t.forEach=Sc}};for(var Oc in yc)yc[Oc]&&kc(mc[Oc]&&mc[Oc].prototype);kc(wc);var Cc=d,Tc=f,Ic=Bo,Pc=Ko.trim,Ac=R("".charAt),$c=Cc.parseFloat,Rc=Cc.Symbol,Ec=Rc&&Rc.iterator,jc=1/$c("\t\n\v\f\r                　\u2028\u2029\ufeff-0")!=-1/0||Ec&&!Tc((function(){$c(Object(Ec))}))?function(t){var e=Pc(Ic(t)),i=$c(e);return 0===i&&"-"==Ac(e,0)?-0:i}:$c;co({global:!0,forced:parseFloat!=jc},{parseFloat:jc});var _c=Gt,Fc=po;co({target:"Object",stat:!0,forced:f((function(){Fc(1)}))},{keys:function(t){return Fc(_c(t))}});var Dc=co,Nc=Tn.indexOf,Vc=tr,Bc=pa([].indexOf),Lc=!!Bc&&1/Bc([1],1,-0)<0;Dc({target:"Array",proto:!0,forced:Lc||!Vc("indexOf")},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return Lc?Bc(this,t,e)||0:Nc(this,t,e)}});var Hc=Ot,Mc=TypeError,Uc=function(t,e){if(!delete t[e])throw Mc("Cannot delete property "+Hc(e)+" of "+Hc(t))},zc=ps,qc=Math.floor,Wc=function(t,e){var i=t.length,n=qc(i/2);return i<8?Gc(t,e):Kc(t,Wc(zc(t,0,n),e),Wc(zc(t,n),e),e)},Gc=function(t,e){for(var i,n,o=t.length,r=1;r<o;){for(n=r,i=t[r];n&&e(t[n-1],i)>0;)t[n]=t[--n];n!==r++&&(t[n]=i)}return t},Kc=function(t,e,i,n){for(var o=e.length,r=i.length,a=0,s=0;a<o||s<r;)t[a+s]=a<o&&s<r?n(e[a],i[s])<=0?e[a++]:i[s++]:a<o?e[a++]:i[s++];return t},Yc=Wc,Jc=at.match(/firefox\/(\d+)/i),Xc=!!Jc&&+Jc[1],Qc=/MSIE|Trident/.test(at),Zc=at.match(/AppleWebKit\/(\d+)\./),th=!!Zc&&+Zc[1],eh=co,ih=R,nh=Pt,oh=Gt,rh=Sn,ah=Uc,sh=Bo,lh=f,ch=Yc,hh=tr,uh=Xc,dh=Qc,ph=pt,fh=th,gh=[],vh=ih(gh.sort),bh=ih(gh.push),mh=lh((function(){gh.sort(void 0)})),yh=lh((function(){gh.sort(null)})),wh=hh("sort"),Sh=!lh((function(){if(ph)return ph<70;if(!(uh&&uh>3)){if(dh)return!0;if(fh)return fh<603;var t,e,i,n,o="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:i=3;break;case 68:case 71:i=4;break;default:i=2}for(n=0;n<47;n++)gh.push({k:e+n,v:i})}for(gh.sort((function(t,e){return e.v-t.v})),n=0;n<gh.length;n++)e=gh[n].k.charAt(0),o.charAt(o.length-1)!==e&&(o+=e);return"DGBEFHACIJK"!==o}}));eh({target:"Array",proto:!0,forced:mh||!yh||!wh||!Sh},{sort:function(t){void 0!==t&&nh(t);var e=oh(this);if(Sh)return void 0===t?vh(e):vh(e,t);var i,n,o=[],r=rh(e);for(n=0;n<r;n++)n in e&&bh(o,e[n]);for(ch(o,function(t){return function(e,i){return void 0===i?-1:void 0===e?1:void 0!==t?+t(e,i)||0:sh(e)>sh(i)?1:-1}}(t)),i=rh(o),n=0;n<i;)e[n]=o[n++];for(;n<r;)ah(e,n++);return e}});var xh=R,kh=Gt,Oh=Math.floor,Ch=xh("".charAt),Th=xh("".replace),Ih=xh("".slice),Ph=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,Ah=/\$([$&'`]|\d{1,2})/g,$h=ha,Rh=y,Eh=R,jh=xa,_h=f,Fh=Me,Dh=X,Nh=H,Vh=dn,Bh=yn,Lh=Bo,Hh=z,Mh=ns,Uh=Rt,zh=function(t,e,i,n,o,r){var a=i+t.length,s=n.length,l=Ah;return void 0!==o&&(o=kh(o),l=Ph),Th(r,l,(function(r,l){var c;switch(Ch(l,0)){case"$":return"$";case"&":return t;case"`":return Ih(e,0,i);case"'":return Ih(e,a);case"<":c=o[Ih(l,1,-1)];break;default:var h=+l;if(0===h)return r;if(h>s){var u=Oh(h/10);return 0===u?r:u<=s?void 0===n[u-1]?Ch(l,1):n[u-1]+Ch(l,1):r}c=n[h-1]}return void 0===c?"":c}))},qh=ws,Wh=he("replace"),Gh=Math.max,Kh=Math.min,Yh=Eh([].concat),Jh=Eh([].push),Xh=Eh("".indexOf),Qh=Eh("".slice),Zh="$0"==="a".replace(/./,"$0"),tu=!!/./[Wh]&&""===/./[Wh]("a","$0");jh("replace",(function(t,e,i){var n=tu?"$":"$0";return[function(t,i){var n=Hh(this),o=Nh(t)?void 0:Uh(t,Wh);return o?Rh(o,t,n,i):Rh(e,Lh(n),t,i)},function(t,o){var r=Fh(this),a=Lh(t);if("string"==typeof o&&-1===Xh(o,n)&&-1===Xh(o,"$<")){var s=i(e,r,a,o);if(s.done)return s.value}var l=Dh(o);l||(o=Lh(o));var c=r.global;if(c){var h=r.unicode;r.lastIndex=0}for(var u=[];;){var d=qh(r,a);if(null===d)break;if(Jh(u,d),!c)break;""===Lh(d[0])&&(r.lastIndex=Mh(a,Bh(r.lastIndex),h))}for(var p,f="",g=0,v=0;v<u.length;v++){for(var b=Lh((d=u[v])[0]),m=Gh(Kh(Vh(d.index),a.length),0),y=[],w=1;w<d.length;w++)Jh(y,void 0===(p=d[w])?p:String(p));var S=d.groups;if(l){var x=Yh([b],y,m,a);void 0!==S&&Jh(x,S);var k=Lh($h(o,void 0,x))}else k=zh(b,a,m,y,S,o);m>=g&&(f+=Qh(a,g,m)+k,g=m+b.length)}return f+Qh(a,g)}]}),!!_h((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}))||!Zh||tu);var eu=Gl.filter;co({target:"Array",proto:!0,forced:!xl("filter")},{filter:function(t){return eu(this,t,arguments.length>1?arguments[1]:void 0)}});var iu=Object.is||function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e},nu=y,ou=Me,ru=H,au=z,su=iu,lu=Bo,cu=Rt,hu=ws;xa("search",(function(t,e,i){return[function(e){var i=au(this),n=ru(e)?void 0:cu(e,t);return n?nu(n,e,i):new RegExp(e)[t](lu(i))},function(t){var n=ou(this),o=lu(t),r=i(e,n,o);if(r.done)return r.value;var a=n.lastIndex;su(a,0)||(n.lastIndex=0);var s=hu(n,o);return su(n.lastIndex,a)||(n.lastIndex=a),null===s?-1:s.index}]}));var uu=d,du=f,pu=R,fu=Bo,gu=Ko.trim,vu=Lo,bu=uu.parseInt,mu=uu.Symbol,yu=mu&&mu.iterator,wu=/^[+-]?0x/i,Su=pu(wu.exec),xu=8!==bu(vu+"08")||22!==bu(vu+"0x16")||yu&&!du((function(){bu(Object(yu))}))?function(t,e){var i=gu(fu(t));return bu(i,e>>>0||(Su(wu,i)?16:10))}:bu;co({global:!0,forced:parseInt!=xu},{parseInt:xu});var ku=Gl.map;co({target:"Array",proto:!0,forced:!xl("map")},{map:function(t){return ku(this,t,arguments.length>1?arguments[1]:void 0)}});var Ou=co,Cu=Gl.findIndex,Tu=rl,Iu="findIndex",Pu=!0;Iu in[]&&Array(1).findIndex((function(){Pu=!1})),Ou({target:"Array",proto:!0,forced:Pu},{findIndex:function(t){return Cu(this,t,arguments.length>1?arguments[1]:void 0)}}),Tu(Iu);var Au=R,$u=Pt,Ru=X,Eu=String,ju=TypeError,_u=function(t,e,i){try{return Au($u(Object.getOwnPropertyDescriptor(t,e)[i]))}catch(t){}},Fu=Me,Du=function(t){if("object"==typeof t||Ru(t))return t;throw ju("Can't set "+Eu(t)+" as a prototype")},Nu=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,i={};try{(t=_u(Object.prototype,"__proto__","set"))(i,[]),e=i instanceof Array}catch(t){}return function(i,n){return Fu(i),Du(n),e?t(i,n):i.__proto__=n,i}}():void 0),Vu=X,Bu=tt,Lu=Nu,Hu=function(t,e,i){var n,o;return Lu&&Vu(n=e.constructor)&&n!==i&&Bu(o=n.prototype)&&o!==i.prototype&&Lu(t,o),t},Mu=y,Uu=Jt,zu=rt,qu=sr,Wu=RegExp.prototype,Gu=function(t){var e=t.flags;return void 0!==e||"flags"in Wu||Uu(t,"flags")||!zu(Wu,t)?e:Mu(qu,t)},Ku=Ne.f,Yu=ni,Ju=Ne,Xu=ot,Qu=function(t,e,i){return i.get&&Yu(i.get,e,{getter:!0}),i.set&&Yu(i.set,e,{setter:!0}),Ju.f(t,e,i)},Zu=g,td=he("species"),ed=g,id=d,nd=R,od=eo,rd=Hu,ad=ii,sd=ln.f,ld=rt,cd=Ta,hd=Bo,ud=Gu,dd=dr,pd=function(t,e,i){i in t||Ku(t,i,{configurable:!0,get:function(){return e[i]},set:function(t){e[i]=t}})},fd=sn,gd=f,vd=Jt,bd=Vi.enforce,md=function(t){var e=Xu(t);Zu&&e&&!e[td]&&Qu(e,td,{configurable:!0,get:function(){return this}})},yd=Dr,wd=Br,Sd=he("match"),xd=id.RegExp,kd=xd.prototype,Od=id.SyntaxError,Cd=nd(kd.exec),Td=nd("".charAt),Id=nd("".replace),Pd=nd("".indexOf),Ad=nd("".slice),$d=/^\?<[^\s\d!#%&*+<=>@^][^\s!#%&*+<=>@^]*>/,Rd=/a/g,Ed=/a/g,jd=new xd(Rd)!==Rd,_d=dd.MISSED_STICKY,Fd=dd.UNSUPPORTED_Y,Dd=ed&&(!jd||_d||yd||wd||gd((function(){return Ed[Sd]=!1,xd(Rd)!=Rd||xd(Ed)==Ed||"/a/i"!=xd(Rd,"i")})));if(od("RegExp",Dd)){for(var Nd=function(t,e){var i,n,o,r,a,s,l=ld(kd,this),c=cd(t),h=void 0===e,u=[],d=t;if(!l&&c&&h&&t.constructor===Nd)return t;if((c||ld(kd,t))&&(t=t.source,h&&(e=ud(d))),t=void 0===t?"":hd(t),e=void 0===e?"":hd(e),d=t,yd&&"dotAll"in Rd&&(n=!!e&&Pd(e,"s")>-1)&&(e=Id(e,/s/g,"")),i=e,_d&&"sticky"in Rd&&(o=!!e&&Pd(e,"y")>-1)&&Fd&&(e=Id(e,/y/g,"")),wd&&(r=function(t){for(var e,i=t.length,n=0,o="",r=[],a={},s=!1,l=!1,c=0,h="";n<=i;n++){if("\\"===(e=Td(t,n)))e+=Td(t,++n);else if("]"===e)s=!1;else if(!s)switch(!0){case"["===e:s=!0;break;case"("===e:Cd($d,Ad(t,n+1))&&(n+=2,l=!0),o+=e,c++;continue;case">"===e&&l:if(""===h||vd(a,h))throw new Od("Invalid capture group name");a[h]=!0,r[r.length]=[h,c],l=!1,h="";continue}l?h+=e:o+=e}return[o,r]}(t),t=r[0],u=r[1]),a=rd(xd(t,e),l?this:kd,Nd),(n||o||u.length)&&(s=bd(a),n&&(s.dotAll=!0,s.raw=Nd(function(t){for(var e,i=t.length,n=0,o="",r=!1;n<=i;n++)"\\"!==(e=Td(t,n))?r||"."!==e?("["===e?r=!0:"]"===e&&(r=!1),o+=e):o+="[\\s\\S]":o+=e+Td(t,++n);return o}(t),i)),o&&(s.sticky=!0),u.length&&(s.groups=u)),t!==d)try{ad(a,"source",""===d?"(?:)":d)}catch(t){}return a},Vd=sd(xd),Bd=0;Vd.length>Bd;)pd(Nd,xd,Vd[Bd++]);kd.constructor=Nd,Nd.prototype=kd,fd(id,"RegExp",Nd,{constructor:!0})}md("RegExp");var Ld=hi.PROPER,Hd=sn,Md=Me,Ud=Bo,zd=f,qd=Gu,Wd="toString",Gd=RegExp.prototype.toString,Kd=zd((function(){return"/a/b"!=Gd.call({source:"a",flags:"b"})})),Yd=Ld&&Gd.name!=Wd;(Kd||Yd)&&Hd(RegExp.prototype,Wd,(function(){var t=Md(this);return"/"+Ud(t.source)+"/"+Ud(qd(t))}),{unsafe:!0});var Jd=R([].slice),Xd=co,Qd=cl,Zd=La,tp=tt,ep=vn,ip=Sn,np=G,op=ss,rp=he,ap=Jd,sp=xl("slice"),lp=rp("species"),cp=Array,hp=Math.max;Xd({target:"Array",proto:!0,forced:!sp},{slice:function(t,e){var i,n,o,r=np(this),a=ip(r),s=ep(t,a),l=ep(void 0===e?a:e,a);if(Qd(r)&&(i=r.constructor,(Zd(i)&&(i===cp||Qd(i.prototype))||tp(i)&&null===(i=i[lp]))&&(i=void 0),i===cp||void 0===i))return ap(r,s,l);for(n=new(void 0===i?cp:i)(hp(l-s,0)),o=0;s<l;s++,o++)s in r&&op(n,o,r[s]);return n.length=o,n}});var up,dp,pp,fp={},gp=!f((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),vp=Jt,bp=X,mp=Gt,yp=gp,wp=ki("IE_PROTO"),Sp=Object,xp=Sp.prototype,kp=yp?Sp.getPrototypeOf:function(t){var e=mp(t);if(vp(e,wp))return e[wp];var i=e.constructor;return bp(i)&&e instanceof i?i.prototype:e instanceof Sp?xp:null},Op=f,Cp=X,Tp=tt,Ip=kp,Pp=sn,Ap=he("iterator"),$p=!1;[].keys&&("next"in(pp=[].keys())?(dp=Ip(Ip(pp)))!==Object.prototype&&(up=dp):$p=!0);var Rp=!Tp(up)||Op((function(){var t={};return up[Ap].call(t)!==t}));Rp&&(up={}),Cp(up[Ap])||Pp(up,Ap,(function(){return this}));var Ep={IteratorPrototype:up,BUGGY_SAFARI_ITERATORS:$p},jp=Ne.f,_p=Jt,Fp=he("toStringTag"),Dp=function(t,e,i){t&&!i&&(t=t.prototype),t&&!_p(t,Fp)&&jp(t,Fp,{configurable:!0,value:e})},Np=Ep.IteratorPrototype,Vp=jr,Bp=T,Lp=Dp,Hp=fp,Mp=function(){return this},Up=co,zp=y,qp=X,Wp=function(t,e,i,n){var o=e+" Iterator";return t.prototype=Vp(Np,{next:Bp(+!n,i)}),Lp(t,o,!1),Hp[o]=Mp,t},Gp=kp,Kp=Nu,Yp=Dp,Jp=ii,Xp=sn,Qp=fp,Zp=hi.PROPER,tf=hi.CONFIGURABLE,ef=Ep.IteratorPrototype,nf=Ep.BUGGY_SAFARI_ITERATORS,of=he("iterator"),rf="keys",af="values",sf="entries",lf=function(){return this},cf=G,hf=rl,uf=fp,df=Vi,pf=Ne.f,ff=function(t,e,i,n,o,r,a){Wp(i,e,n);var s,l,c,h=function(t){if(t===o&&g)return g;if(!nf&&t in p)return p[t];switch(t){case rf:case af:case sf:return function(){return new i(this,t)}}return function(){return new i(this)}},u=e+" Iterator",d=!1,p=t.prototype,f=p[of]||p["@@iterator"]||o&&p[o],g=!nf&&f||h(o),v="Array"==e&&p.entries||f;if(v&&(s=Gp(v.call(new t)))!==Object.prototype&&s.next&&(Gp(s)!==ef&&(Kp?Kp(s,ef):qp(s[of])||Xp(s,of,lf)),Yp(s,u,!0)),Zp&&o==af&&f&&f.name!==af&&(tf?Jp(p,"name",af):(d=!0,g=function(){return zp(f,this)})),o)if(l={values:h(af),keys:r?g:h(rf),entries:h(sf)},a)for(c in l)(nf||d||!(c in p))&&Xp(p,c,l[c]);else Up({target:e,proto:!0,forced:nf||d},l);return p[of]!==g&&Xp(p,of,g,{name:o}),Qp[e]=g,l},gf=function(t,e){return{value:t,done:e}},vf=g,bf="Array Iterator",mf=df.set,yf=df.getterFor(bf),wf=ff(Array,"Array",(function(t,e){mf(this,{type:bf,target:cf(t),index:0,kind:e})}),(function(){var t=yf(this),e=t.target,i=t.kind,n=t.index++;return!e||n>=e.length?(t.target=void 0,gf(void 0,!0)):gf("keys"==i?n:"values"==i?e[n]:[n,e[n]],!1)}),"values"),Sf=uf.Arguments=uf.Array;if(hf("keys"),hf("values"),hf("entries"),vf&&"values"!==Sf.name)try{pf(Sf,"name",{value:"values"})}catch(t){}var xf=d,kf=dc,Of=gc,Cf=wf,Tf=ii,If=he,Pf=If("iterator"),Af=If("toStringTag"),$f=Cf.values,Rf=function(t,e){if(t){if(t[Pf]!==$f)try{Tf(t,Pf,$f)}catch(e){t[Pf]=$f}if(t[Af]||Tf(t,Af,e),kf[e])for(var i in Cf)if(t[i]!==Cf[i])try{Tf(t,i,Cf[i])}catch(e){t[i]=Cf[i]}}};for(var Ef in kf)Rf(xf[Ef]&&xf[Ef].prototype,Ef);Rf(Of,"DOMTokenList");var jf=g,_f=cl,Ff=TypeError,Df=Object.getOwnPropertyDescriptor,Nf=jf&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}(),Vf=co,Bf=Gt,Lf=vn,Hf=dn,Mf=Sn,Uf=Nf?function(t,e){if(_f(t)&&!Df(t,"length").writable)throw Ff("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e},zf=ul,qf=ml,Wf=ss,Gf=Uc,Kf=xl("splice"),Yf=Math.max,Jf=Math.min;Vf({target:"Array",proto:!0,forced:!Kf},{splice:function(t,e){var i,n,o,r,a,s,l=Bf(this),c=Mf(l),h=Lf(t,c),u=arguments.length;for(0===u?i=n=0:1===u?(i=0,n=c-h):(i=u-2,n=Jf(Yf(Hf(e),0),c-h)),zf(c+i-n),o=qf(l,n),r=0;r<n;r++)(a=h+r)in l&&Wf(o,r,l[a]);if(o.length=n,i<n){for(r=h;r<c-n;r++)s=r+i,(a=r+n)in l?l[s]=l[a]:Gf(l,s);for(r=c;r>c-n+i;r--)Gf(l,r-1)}else if(i>n)for(r=c-n;r>h;r--)s=r+i-1,(a=r+n-1)in l?l[s]=l[a]:Gf(l,s);for(r=0;r<i;r++)l[r+h]=arguments[r+2];return Uf(l,c-n+i),o}});var Xf=d,Qf=R(1..valueOf),Zf=co,tg=g,eg=d,ig=Xf,ng=R,og=eo,rg=Jt,ag=Hu,sg=rt,lg=xt,cg=me,hg=f,ug=ln.f,dg=p.f,pg=Ne.f,fg=Qf,gg=Ko.trim,vg="Number",bg=eg.Number;ig.Number;var mg=bg.prototype,yg=eg.TypeError,wg=ng("".slice),Sg=ng("".charCodeAt),xg=function(t){var e=cg(t,"number");return"bigint"==typeof e?e:kg(e)},kg=function(t){var e,i,n,o,r,a,s,l,c=cg(t,"number");if(lg(c))throw yg("Cannot convert a Symbol value to a number");if("string"==typeof c&&c.length>2)if(c=gg(c),43===(e=Sg(c,0))||45===e){if(88===(i=Sg(c,2))||120===i)return NaN}else if(48===e){switch(Sg(c,1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+c}for(a=(r=wg(c,2)).length,s=0;s<a;s++)if((l=Sg(r,s))<48||l>o)return NaN;return parseInt(r,n)}return+c},Og=og(vg,!bg(" 0o1")||!bg("0b1")||bg("+0x1")),Cg=function(t){return sg(mg,t)&&hg((function(){fg(t)}))},Tg=function(t){var e=arguments.length<1?0:bg(xg(t));return Cg(this)?ag(Object(e),this,Tg):e};Tg.prototype=mg,Og&&(mg.constructor=Tg),Zf({global:!0,constructor:!0,wrap:!0,forced:Og},{Number:Tg});Og&&function(t,e){for(var i,n=tg?ug(e):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),o=0;n.length>o;o++)rg(e,i=n[o])&&!rg(t,i)&&pg(t,i,dg(e,i))}(ig.Number,bg);var Ig=co,Pg=cl,Ag=R([].reverse),$g=[1,2];Ig({target:"Array",proto:!0,forced:String($g)===String($g.reverse())},{reverse:function(){return Pg(this)&&(this.length=this.length),Ag(this)}});var Rg=y,Eg=Me,jg=H,_g=yn,Fg=Bo,Dg=z,Ng=Rt,Vg=ns,Bg=ws;xa("match",(function(t,e,i){return[function(e){var i=Dg(this),n=jg(e)?void 0:Ng(e,t);return n?Rg(n,e,i):new RegExp(e)[t](Fg(i))},function(t){var n=Eg(this),o=Fg(t),r=i(e,n,o);if(r.done)return r.value;if(!n.global)return Bg(n,o);var a=n.unicode;n.lastIndex=0;for(var s,l=[],c=0;null!==(s=Bg(n,o));){var h=Fg(s[0]);l[c]=h,""===h&&(n.lastIndex=Vg(o,_g(n.lastIndex),a)),c++}return 0===c?null:l}]}));var Lg,Hg=co,Mg=pa,Ug=p.f,zg=yn,qg=Bo,Wg=nc,Gg=z,Kg=rc,Yg=Mg("".startsWith),Jg=Mg("".slice),Xg=Math.min,Qg=Kg("startsWith");Hg({target:"String",proto:!0,forced:!!(Qg||(Lg=Ug(String.prototype,"startsWith"),!Lg||Lg.writable))&&!Qg},{startsWith:function(t){var e=qg(Gg(this));Wg(t);var i=zg(Xg(arguments.length>1?arguments[1]:void 0,e.length)),n=qg(t);return Yg?Yg(e,n,i):Jg(e,i,i+n.length)===n}});var Zg=co,tv=pa,ev=p.f,iv=yn,nv=Bo,ov=nc,rv=z,av=rc,sv=tv("".endsWith),lv=tv("".slice),cv=Math.min,hv=av("endsWith"),uv=!hv&&!!function(){var t=ev(String.prototype,"endsWith");return t&&!t.writable}();Zg({target:"String",proto:!0,forced:!uv&&!hv},{endsWith:function(t){var e=nv(rv(this));ov(t);var i=arguments.length>1?arguments[1]:void 0,n=e.length,o=void 0===i?n:cv(iv(i),n),r=nv(t);return sv?sv(e,r,o):lv(e,o-r.length,o)===r}});var dv={getBootstrapVersion:function(){var e=5;try{var i=t.fn.dropdown.Constructor.VERSION;void 0!==i&&(e=parseInt(i,10))}catch(t){}try{var n=bootstrap.Tooltip.VERSION;void 0!==n&&(e=parseInt(n,10))}catch(t){}return e},getIconsPrefix:function(t){return{bootstrap3:"glyphicon",bootstrap4:"fa",bootstrap5:"bi","bootstrap-table":"icon",bulma:"fa",foundation:"fa",materialize:"material-icons",semantic:"fa"}[t]||"fa"},getIcons:function(t){return{glyphicon:{paginationSwitchDown:"glyphicon-collapse-down icon-chevron-down",paginationSwitchUp:"glyphicon-collapse-up icon-chevron-up",refresh:"glyphicon-refresh icon-refresh",toggleOff:"glyphicon-list-alt icon-list-alt",toggleOn:"glyphicon-list-alt icon-list-alt",columns:"glyphicon-th icon-th",detailOpen:"glyphicon-plus icon-plus",detailClose:"glyphicon-minus icon-minus",fullscreen:"glyphicon-fullscreen",search:"glyphicon-search",clearSearch:"glyphicon-trash"},fa:{paginationSwitchDown:"fa-caret-square-down",paginationSwitchUp:"fa-caret-square-up",refresh:"fa-sync",toggleOff:"fa-toggle-off",toggleOn:"fa-toggle-on",columns:"fa-th-list",detailOpen:"fa-plus",detailClose:"fa-minus",fullscreen:"fa-arrows-alt",search:"fa-search",clearSearch:"fa-trash"},bi:{paginationSwitchDown:"bi-caret-down-square",paginationSwitchUp:"bi-caret-up-square",refresh:"bi-arrow-clockwise",toggleOff:"bi-toggle-off",toggleOn:"bi-toggle-on",columns:"bi-list-ul",detailOpen:"bi-plus",detailClose:"bi-dash",fullscreen:"bi-arrows-move",search:"bi-search",clearSearch:"bi-trash"},icon:{paginationSwitchDown:"icon-arrow-up-circle",paginationSwitchUp:"icon-arrow-down-circle",refresh:"icon-refresh-cw",toggleOff:"icon-toggle-right",toggleOn:"icon-toggle-right",columns:"icon-list",detailOpen:"icon-plus",detailClose:"icon-minus",fullscreen:"icon-maximize",search:"icon-search",clearSearch:"icon-trash-2"},"material-icons":{paginationSwitchDown:"grid_on",paginationSwitchUp:"grid_off",refresh:"refresh",toggleOff:"tablet",toggleOn:"tablet_android",columns:"view_list",detailOpen:"add",detailClose:"remove",fullscreen:"fullscreen",sort:"sort",search:"search",clearSearch:"delete"}}[t]},getSearchInput:function(e){return"string"==typeof e.options.searchSelector?t(e.options.searchSelector):e.$toolbar.find(".search input")},extend:function(){for(var t=this,i=arguments.length,n=new Array(i),o=0;o<i;o++)n[o]=arguments[o];var r,a=n[0]||{},s=1,l=!1;for("boolean"==typeof a&&(l=a,a=n[s]||{},s++),"object"!==e(a)&&"function"!=typeof a&&(a={});s<n.length;s++){var c=n[s];if(null!=c)for(var h in c){var u=c[h];if("__proto__"!==h&&a!==u){var d=Array.isArray(u);if(l&&u&&(this.isObject(u)||d)){var p=a[h];if(d&&Array.isArray(p)&&p.every((function(e){return!t.isObject(e)&&!Array.isArray(e)}))){a[h]=u;continue}r=d&&!Array.isArray(p)?[]:d||this.isObject(p)?p:{},a[h]=this.extend(l,r,u)}else void 0!==u&&(a[h]=u)}}}return a},sprintf:function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];var o=!0,r=0,a=t.replace(/%s/g,(function(){var t=i[r++];return void 0===t?(o=!1,""):t}));return o?a:""},isObject:function(t){return"object"===e(t)&&null!==t&&!Array.isArray(t)},isEmptyObject:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return 0===Object.entries(t).length&&t.constructor===Object},isNumeric:function(t){return!isNaN(parseFloat(t))&&isFinite(t)},getFieldTitle:function(t,e){var i,n=c(t);try{for(n.s();!(i=n.n()).done;){var o=i.value;if(o.field===e)return o.title}}catch(t){n.e(t)}finally{n.f()}return""},setFieldIndex:function(t){var e,i=0,n=[],o=c(t[0]);try{for(o.s();!(e=o.n()).done;){i+=e.value.colspan||1}}catch(t){o.e(t)}finally{o.f()}for(var r=0;r<t.length;r++){n[r]=[];for(var a=0;a<i;a++)n[r][a]=!1}for(var s=0;s<t.length;s++){var l,h=c(t[s]);try{for(h.s();!(l=h.n()).done;){var u=l.value,d=u.rowspan||1,p=u.colspan||1,f=n[s].indexOf(!1);u.colspanIndex=f,1===p?(u.fieldIndex=f,void 0===u.field&&(u.field=f)):u.colspanGroup=u.colspan;for(var g=0;g<d;g++)for(var v=0;v<p;v++)n[s+g][f+v]=!0}}catch(t){h.e(t)}finally{h.f()}}},normalizeAccent:function(t){return"string"!=typeof t?t:t.normalize("NFD").replace(/[\u0300-\u036f]/g,"")},updateFieldGroup:function(t,e){var i,n,o=(i=[]).concat.apply(i,a(t)),r=c(t);try{for(r.s();!(n=r.n()).done;){var s,l=c(n.value);try{for(l.s();!(s=l.n()).done;){var h=s.value;if(h.colspanGroup>1){for(var u=0,d=function(t){o.find((function(e){return e.fieldIndex===t})).visible&&u++},p=h.colspanIndex;p<h.colspanIndex+h.colspanGroup;p++)d(p);h.colspan=u,h.visible=u>0}}}catch(t){l.e(t)}finally{l.f()}}}catch(t){r.e(t)}finally{r.f()}if(!(t.length<2)){var f,g=c(e);try{var v=function(){var t=f.value,e=o.filter((function(e){return e.fieldIndex===t.fieldIndex}));if(e.length>1){var i,n=c(e);try{for(n.s();!(i=n.n()).done;){i.value.visible=t.visible}}catch(t){n.e(t)}finally{n.f()}}};for(g.s();!(f=g.n()).done;)v()}catch(t){g.e(t)}finally{g.f()}}},getScrollBarWidth:function(){if(void 0===this.cachedWidth){var e=t("<div/>").addClass("fixed-table-scroll-inner"),i=t("<div/>").addClass("fixed-table-scroll-outer");i.append(e),t("body").append(i);var n=e[0].offsetWidth;i.css("overflow","scroll");var o=e[0].offsetWidth;n===o&&(o=i[0].clientWidth),i.remove(),this.cachedWidth=n-o}return this.cachedWidth},calculateObjectValue:function(t,i,n,o){var r=i;if("string"==typeof i){var s=i.split(".");if(s.length>1){r=window;var l,h=c(s);try{for(h.s();!(l=h.n()).done;){r=r[l.value]}}catch(t){h.e(t)}finally{h.f()}}else r=window[i]}return null!==r&&"object"===e(r)?r:"function"==typeof r?r.apply(t,n||[]):!r&&"string"==typeof i&&n&&this.sprintf.apply(this,[i].concat(a(n)))?this.sprintf.apply(this,[i].concat(a(n))):o},compareObjects:function(t,e,i){var n=Object.keys(t),o=Object.keys(e);if(i&&n.length!==o.length)return!1;for(var r=0,a=n;r<a.length;r++){var s=a[r];if(o.includes(s)&&t[s]!==e[s])return!1}return!0},regexCompare:function(t,e){try{var i=e.match(/^\/(.*?)\/([gim]*)$/);if(-1!==t.toString().search(i?new RegExp(i[1],i[2]):new RegExp(e,"gim")))return!0}catch(t){return!1}return!1},escapeApostrophe:function(t){return t.toString().replace(/'/g,"&#39;")},escapeHTML:function(t){return t?t.toString().replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;"):t},unescapeHTML:function(t){return"string"==typeof t&&t?t.toString().replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#39;/g,"'"):t},removeHTML:function(t){return t?t.toString().replace(/(<([^>]+)>)/gi,"").replace(/&[#A-Za-z0-9]+;/gi,"").trim():t},getRealDataAttr:function(t){for(var e=0,i=Object.entries(t);e<i.length;e++){var n=r(i[e],2),o=n[0],a=n[1],s=o.split(/(?=[A-Z])/).join("-").toLowerCase();s!==o&&(t[s]=a,delete t[o])}return t},getItemField:function(t,e,i){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:void 0,o=t;if(void 0!==n&&(i=n),"string"!=typeof e||t.hasOwnProperty(e))return i?this.escapeHTML(t[e]):t[e];var r,a=e.split("."),s=c(a);try{for(s.s();!(r=s.n()).done;){var l=r.value;o=o&&o[l]}}catch(t){s.e(t)}finally{s.f()}return i?this.escapeHTML(o):o},isIEBrowser:function(){return navigator.userAgent.includes("MSIE ")||/Trident.*rv:11\./.test(navigator.userAgent)},findIndex:function(t,e){var i,n=c(t);try{for(n.s();!(i=n.n()).done;){var o=i.value;if(JSON.stringify(o)===JSON.stringify(e))return t.indexOf(o)}}catch(t){n.e(t)}finally{n.f()}return-1},trToData:function(e,i){var n=this,o=[],r=[];return i.each((function(i,a){var s=t(a),l={};l._id=s.attr("id"),l._class=s.attr("class"),l._data=n.getRealDataAttr(s.data()),l._style=s.attr("style"),s.find(">td,>th").each((function(o,a){for(var s=t(a),c=+s.attr("colspan")||1,h=+s.attr("rowspan")||1,u=o;r[i]&&r[i][u];u++);for(var d=u;d<u+c;d++)for(var p=i;p<i+h;p++)r[p]||(r[p]=[]),r[p][d]=!0;var f=e[u].field;l[f]=n.escapeApostrophe(s.html().trim()),l["_".concat(f,"_id")]=s.attr("id"),l["_".concat(f,"_class")]=s.attr("class"),l["_".concat(f,"_rowspan")]=s.attr("rowspan"),l["_".concat(f,"_colspan")]=s.attr("colspan"),l["_".concat(f,"_title")]=s.attr("title"),l["_".concat(f,"_data")]=n.getRealDataAttr(s.data()),l["_".concat(f,"_style")]=s.attr("style")})),o.push(l)})),o},sort:function(t,e,i,n,o,r){if(null==t&&(t=""),null==e&&(e=""),n.sortStable&&t===e&&(t=o,e=r),this.isNumeric(t)&&this.isNumeric(e))return(t=parseFloat(t))<(e=parseFloat(e))?-1*i:t>e?i:0;if(n.sortEmptyLast){if(""===t)return 1;if(""===e)return-1}return t===e?0:("string"!=typeof t&&(t=t.toString()),-1===t.localeCompare(e)?-1*i:i)},getEventName:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e=e||"".concat(+new Date).concat(~~(1e6*Math.random())),"".concat(t,"-").concat(e)},hasDetailViewIcon:function(t){return t.detailView&&t.detailViewIcon&&!t.cardView},getDetailViewIndexOffset:function(t){return this.hasDetailViewIcon(t)&&"right"!==t.detailViewAlign?1:0},checkAutoMergeCells:function(t){var e,i=c(t);try{for(i.s();!(e=i.n()).done;)for(var n=e.value,o=0,r=Object.keys(n);o<r.length;o++){var a=r[o];if(a.startsWith("_")&&(a.endsWith("_rowspan")||a.endsWith("_colspan")))return!0}}catch(t){i.e(t)}finally{i.f()}return!1},deepCopy:function(t){return void 0===t?t:this.extend(!0,Array.isArray(t)?[]:{},t)},debounce:function(t,e,i){var n;return function(){var o=this,r=arguments,a=function(){n=null,i||t.apply(o,r)},s=i&&!n;clearTimeout(n),n=setTimeout(a,e),s&&t.apply(o,r)}}},pv=dv.getBootstrapVersion(),fv={3:{classes:{buttonsPrefix:"btn",buttons:"default",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"pull",inputGroup:"input-group",inputPrefix:"input-",input:"form-control",select:"form-control",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{toolbarDropdown:['<ul class="dropdown-menu" role="menu">',"</ul>"],toolbarDropdownItem:'<li class="dropdown-item-marker" role="menuitem"><label>%s</label></li>',toolbarDropdownSeparator:'<li class="divider"></li>',pageDropdown:['<ul class="dropdown-menu" role="menu">',"</ul>"],pageDropdownItem:'<li role="menuitem" class="%s"><a href="#">%s</a></li>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s<span class="input-group-btn">%s</span></div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}},4:{classes:{buttonsPrefix:"btn",buttons:"secondary",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"float",inputGroup:"btn-group",inputPrefix:"form-control-",input:"form-control",select:"form-control",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{toolbarDropdown:['<div class="dropdown-menu dropdown-menu-right">',"</div>"],toolbarDropdownItem:'<label class="dropdown-item dropdown-item-marker">%s</label>',pageDropdown:['<div class="dropdown-menu">',"</div>"],pageDropdownItem:'<a class="dropdown-item %s" href="#">%s</a>',toolbarDropdownSeparator:'<div class="dropdown-divider"></div>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s<div class="input-group-append">%s</div></div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}},5:{classes:{buttonsPrefix:"btn",buttons:"secondary",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"float",inputGroup:"btn-group",inputPrefix:"form-control-",input:"form-control",select:"form-select",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{dataToggle:"data-bs-toggle",toolbarDropdown:['<div class="dropdown-menu dropdown-menu-right">',"</div>"],toolbarDropdownItem:'<label class="dropdown-item dropdown-item-marker">%s</label>',pageDropdown:['<div class="dropdown-menu">',"</div>"],pageDropdownItem:'<a class="dropdown-item %s" href="#">%s</a>',toolbarDropdownSeparator:'<div class="dropdown-divider"></div>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s%s</div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}}}[pv],gv={height:void 0,classes:"table table-bordered table-hover",buttons:{},theadClasses:"",headerStyle:function(t){return{}},rowStyle:function(t,e){return{}},rowAttributes:function(t,e){return{}},undefinedText:"-",locale:void 0,virtualScroll:!1,virtualScrollItemHeight:void 0,sortable:!0,sortClass:void 0,silentSort:!0,sortEmptyLast:!1,sortName:void 0,sortOrder:void 0,sortReset:!1,sortStable:!1,sortResetPage:!1,rememberOrder:!1,serverSort:!0,customSort:void 0,columns:[[]],data:[],url:void 0,method:"get",cache:!0,contentType:"application/json",dataType:"json",ajax:void 0,ajaxOptions:{},queryParams:function(t){return t},queryParamsType:"limit",responseHandler:function(t){return t},totalField:"total",totalNotFilteredField:"totalNotFiltered",dataField:"rows",footerField:"footer",pagination:!1,paginationParts:["pageInfo","pageSize","pageList"],showExtendedPagination:!1,paginationLoop:!0,sidePagination:"client",totalRows:0,totalNotFiltered:0,pageNumber:1,pageSize:10,pageList:[10,25,50,100],paginationHAlign:"right",paginationVAlign:"bottom",paginationDetailHAlign:"left",paginationPreText:"&lsaquo;",paginationNextText:"&rsaquo;",paginationSuccessivelySize:5,paginationPagesBySide:1,paginationUseIntermediate:!1,search:!1,searchable:!1,searchHighlight:!1,searchOnEnterKey:!1,strictSearch:!1,regexSearch:!1,searchSelector:!1,visibleSearch:!1,showButtonIcons:!0,showButtonText:!1,showSearchButton:!1,showSearchClearButton:!1,trimOnSearch:!0,searchAlign:"right",searchTimeOut:500,searchText:"",customSearch:void 0,showHeader:!0,showFooter:!1,footerStyle:function(t){return{}},searchAccentNeutralise:!1,showColumns:!1,showColumnsToggleAll:!1,showColumnsSearch:!1,minimumCountColumns:1,showPaginationSwitch:!1,showRefresh:!1,showToggle:!1,showFullscreen:!1,smartDisplay:!0,escape:!1,escapeTitle:!0,filterOptions:{filterAlgorithm:"and"},idField:void 0,selectItemName:"btSelectItem",clickToSelect:!1,ignoreClickToSelectOn:function(t){var e=t.tagName;return["A","BUTTON"].includes(e)},singleSelect:!1,checkboxHeader:!0,maintainMetaData:!1,multipleSelectRow:!1,uniqueId:void 0,cardView:!1,detailView:!1,detailViewIcon:!0,detailViewByClick:!1,detailViewAlign:"left",detailFormatter:function(t,e){return""},detailFilter:function(t,e){return!0},toolbar:void 0,toolbarAlign:"left",buttonsToolbar:void 0,buttonsAlign:"right",buttonsOrder:["paginationSwitch","refresh","toggle","fullscreen","columns"],buttonsPrefix:fv.classes.buttonsPrefix,buttonsClass:fv.classes.buttons,iconsPrefix:void 0,icons:{},iconSize:void 0,loadingFontSize:"auto",loadingTemplate:function(t){return'<span class="loading-wrap">\n      <span class="loading-text">'.concat(t,'</span>\n      <span class="animation-wrap"><span class="animation-dot"></span></span>\n      </span>\n    ')},onAll:function(t,e){return!1},onClickCell:function(t,e,i,n){return!1},onDblClickCell:function(t,e,i,n){return!1},onClickRow:function(t,e){return!1},onDblClickRow:function(t,e){return!1},onSort:function(t,e){return!1},onCheck:function(t){return!1},onUncheck:function(t){return!1},onCheckAll:function(t){return!1},onUncheckAll:function(t){return!1},onCheckSome:function(t){return!1},onUncheckSome:function(t){return!1},onLoadSuccess:function(t){return!1},onLoadError:function(t){return!1},onColumnSwitch:function(t,e){return!1},onColumnSwitchAll:function(t){return!1},onPageChange:function(t,e){return!1},onSearch:function(t){return!1},onToggle:function(t){return!1},onPreBody:function(t){return!1},onPostBody:function(){return!1},onPostHeader:function(){return!1},onPostFooter:function(){return!1},onExpandRow:function(t,e,i){return!1},onCollapseRow:function(t,e){return!1},onRefreshOptions:function(t){return!1},onRefresh:function(t){return!1},onResetView:function(){return!1},onScrollBody:function(){return!1},onTogglePagination:function(t){return!1},onVirtualScroll:function(t,e){return!1}},vv={formatLoadingMessage:function(){return"Loading, please wait"},formatRecordsPerPage:function(t){return"".concat(t," rows per page")},formatShowingRows:function(t,e,i,n){return void 0!==n&&n>0&&n>i?"Showing ".concat(t," to ").concat(e," of ").concat(i," rows (filtered from ").concat(n," total rows)"):"Showing ".concat(t," to ").concat(e," of ").concat(i," rows")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatSearch:function(){return"Search"},formatClearSearch:function(){return"Clear Search"},formatNoMatches:function(){return"No matching records found"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Refresh"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Columns"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"}},bv={field:void 0,title:void 0,titleTooltip:void 0,class:void 0,width:void 0,widthUnit:"px",rowspan:void 0,colspan:void 0,align:void 0,halign:void 0,falign:void 0,valign:void 0,cellStyle:void 0,radio:!1,checkbox:!1,checkboxEnabled:!0,clickToSelect:!0,showSelectTitle:!1,sortable:!1,sortName:void 0,order:"asc",sorter:void 0,visible:!0,switchable:!0,cardVisible:!0,searchable:!0,formatter:void 0,footerFormatter:void 0,detailFormatter:void 0,searchFormatter:!0,searchHighlightFormatter:!1,escape:void 0,events:void 0};Object.assign(gv,vv);var mv={VERSION:"1.21.4",THEME:"bootstrap".concat(pv),CONSTANTS:fv,DEFAULTS:gv,COLUMN_DEFAULTS:bv,METHODS:["getOptions","refreshOptions","getData","getSelections","load","append","prepend","remove","removeAll","insertRow","updateRow","getRowByUniqueId","updateByUniqueId","removeByUniqueId","updateCell","updateCellByUniqueId","showRow","hideRow","getHiddenRows","showColumn","hideColumn","getVisibleColumns","getHiddenColumns","showAllColumns","hideAllColumns","mergeCells","checkAll","uncheckAll","checkInvert","check","uncheck","checkBy","uncheckBy","refresh","destroy","resetView","showLoading","hideLoading","togglePagination","toggleFullscreen","toggleView","resetSearch","filterBy","scrollTo","getScrollPosition","selectPage","prevPage","nextPage","toggleDetailView","expandRow","collapseRow","expandRowByUniqueId","collapseRowByUniqueId","expandAllRows","collapseAllRows","updateColumnTitle","updateFormatText"],EVENTS:{"all.bs.table":"onAll","click-row.bs.table":"onClickRow","dbl-click-row.bs.table":"onDblClickRow","click-cell.bs.table":"onClickCell","dbl-click-cell.bs.table":"onDblClickCell","sort.bs.table":"onSort","check.bs.table":"onCheck","uncheck.bs.table":"onUncheck","check-all.bs.table":"onCheckAll","uncheck-all.bs.table":"onUncheckAll","check-some.bs.table":"onCheckSome","uncheck-some.bs.table":"onUncheckSome","load-success.bs.table":"onLoadSuccess","load-error.bs.table":"onLoadError","column-switch.bs.table":"onColumnSwitch","column-switch-all.bs.table":"onColumnSwitchAll","page-change.bs.table":"onPageChange","search.bs.table":"onSearch","toggle.bs.table":"onToggle","pre-body.bs.table":"onPreBody","post-body.bs.table":"onPostBody","post-header.bs.table":"onPostHeader","post-footer.bs.table":"onPostFooter","expand-row.bs.table":"onExpandRow","collapse-row.bs.table":"onCollapseRow","refresh-options.bs.table":"onRefreshOptions","reset-view.bs.table":"onResetView","refresh.bs.table":"onRefresh","scroll-body.bs.table":"onScrollBody","toggle-pagination.bs.table":"onTogglePagination","virtual-scroll.bs.table":"onVirtualScroll"},LOCALES:{en:vv,"en-US":vv}},yv=function(){function t(e){var n=this;i(this,t),this.rows=e.rows,this.scrollEl=e.scrollEl,this.contentEl=e.contentEl,this.callback=e.callback,this.itemHeight=e.itemHeight,this.cache={},this.scrollTop=this.scrollEl.scrollTop,this.initDOM(this.rows,e.fixedScroll),this.scrollEl.scrollTop=this.scrollTop,this.lastCluster=0;var o=function(){n.lastCluster!==(n.lastCluster=n.getNum())&&(n.initDOM(n.rows),n.callback(n.startIndex,n.endIndex))};this.scrollEl.addEventListener("scroll",o,!1),this.destroy=function(){n.contentEl.innerHtml="",n.scrollEl.removeEventListener("scroll",o,!1)}}return o(t,[{key:"initDOM",value:function(t,e){void 0===this.clusterHeight&&(this.cache.scrollTop=this.scrollEl.scrollTop,this.cache.data=this.contentEl.innerHTML=t[0]+t[0]+t[0],this.getRowsHeight(t));var i=this.initData(t,this.getNum(e)),n=i.rows.join(""),o=this.checkChanges("data",n),r=this.checkChanges("top",i.topOffset),a=this.checkChanges("bottom",i.bottomOffset),s=[];o&&r?(i.topOffset&&s.push(this.getExtra("top",i.topOffset)),s.push(n),i.bottomOffset&&s.push(this.getExtra("bottom",i.bottomOffset)),this.startIndex=i.start,this.endIndex=i.end,this.contentEl.innerHTML=s.join(""),e&&(this.contentEl.scrollTop=this.cache.scrollTop)):a&&(this.contentEl.lastChild.style.height="".concat(i.bottomOffset,"px"))}},{key:"getRowsHeight",value:function(){if(void 0===this.itemHeight){var t=this.contentEl.children,e=t[Math.floor(t.length/2)];this.itemHeight=e.offsetHeight}this.blockHeight=50*this.itemHeight,this.clusterRows=200,this.clusterHeight=4*this.blockHeight}},{key:"getNum",value:function(t){return this.scrollTop=t?this.cache.scrollTop:this.scrollEl.scrollTop,Math.floor(this.scrollTop/(this.clusterHeight-this.blockHeight))||0}},{key:"initData",value:function(t,e){if(t.length<50)return{topOffset:0,bottomOffset:0,rowsAbove:0,rows:t};var i=Math.max((this.clusterRows-50)*e,0),n=i+this.clusterRows,o=Math.max(i*this.itemHeight,0),r=Math.max((t.length-n)*this.itemHeight,0),a=[],s=i;o<1&&s++;for(var l=i;l<n;l++)t[l]&&a.push(t[l]);return{start:i,end:n,topOffset:o,bottomOffset:r,rowsAbove:s,rows:a}}},{key:"checkChanges",value:function(t,e){var i=e!==this.cache[t];return this.cache[t]=e,i}},{key:"getExtra",value:function(t,e){var i=document.createElement("tr");return i.className="virtual-scroll-".concat(t),e&&(i.style.height="".concat(e,"px")),i.outerHTML}}]),t}(),wv=function(){function n(e,o){i(this,n),this.options=o,this.$el=t(e),this.$el_=this.$el.clone(),this.timeoutId_=0,this.timeoutFooter_=0}return o(n,[{key:"init",value:function(){this.initConstants(),this.initLocale(),this.initContainer(),this.initTable(),this.initHeader(),this.initData(),this.initHiddenRows(),this.initToolbar(),this.initPagination(),this.initBody(),this.initSearchText(),this.initServer()}},{key:"initConstants",value:function(){var i=this.options;this.constants=mv.CONSTANTS,this.constants.theme=t.fn.bootstrapTable.theme,this.constants.dataToggle=this.constants.html.dataToggle||"data-toggle";var n=dv.getIconsPrefix(t.fn.bootstrapTable.theme),o=dv.getIcons(n);"string"==typeof i.icons&&(i.icons=dv.calculateObjectValue(null,i.icons)),i.iconsPrefix=i.iconsPrefix||t.fn.bootstrapTable.defaults.iconsPrefix||n,i.icons=Object.assign(o,t.fn.bootstrapTable.defaults.icons,i.icons);var r=i.buttonsPrefix?"".concat(i.buttonsPrefix,"-"):"";this.constants.buttonsClass=[i.buttonsPrefix,r+i.buttonsClass,dv.sprintf("".concat(r,"%s"),i.iconSize)].join(" ").trim(),this.buttons=dv.calculateObjectValue(this,i.buttons,[],{}),"object"!==e(this.buttons)&&(this.buttons={})}},{key:"initLocale",value:function(){if(this.options.locale){var e=t.fn.bootstrapTable.locales,i=this.options.locale.split(/-|_/);i[0]=i[0].toLowerCase(),i[1]&&(i[1]=i[1].toUpperCase());var o={};e[this.options.locale]?o=e[this.options.locale]:e[i.join("-")]?o=e[i.join("-")]:e[i[0]]&&(o=e[i[0]]);for(var a=0,s=Object.entries(o);a<s.length;a++){var l=r(s[a],2),c=l[0],h=l[1];this.options[c]===n.DEFAULTS[c]&&(this.options[c]=h)}}}},{key:"initContainer",value:function(){var e=["top","both"].includes(this.options.paginationVAlign)?'<div class="fixed-table-pagination clearfix"></div>':"",i=["bottom","both"].includes(this.options.paginationVAlign)?'<div class="fixed-table-pagination"></div>':"",n=dv.calculateObjectValue(this.options,this.options.loadingTemplate,[this.options.formatLoadingMessage()]);this.$container=t('\n      <div class="bootstrap-table '.concat(this.constants.theme,'">\n      <div class="fixed-table-toolbar"></div>\n      ').concat(e,'\n      <div class="fixed-table-container">\n      <div class="fixed-table-header"><table></table></div>\n      <div class="fixed-table-body">\n      <div class="fixed-table-loading">\n      ').concat(n,'\n      </div>\n      </div>\n      <div class="fixed-table-footer"></div>\n      </div>\n      ').concat(i,"\n      </div>\n    ")),this.$container.insertAfter(this.$el),this.$tableContainer=this.$container.find(".fixed-table-container"),this.$tableHeader=this.$container.find(".fixed-table-header"),this.$tableBody=this.$container.find(".fixed-table-body"),this.$tableLoading=this.$container.find(".fixed-table-loading"),this.$tableFooter=this.$el.find("tfoot"),this.options.buttonsToolbar?this.$toolbar=t("body").find(this.options.buttonsToolbar):this.$toolbar=this.$container.find(".fixed-table-toolbar"),this.$pagination=this.$container.find(".fixed-table-pagination"),this.$tableBody.append(this.$el),this.$container.after('<div class="clearfix"></div>'),this.$el.addClass(this.options.classes),this.$tableLoading.addClass(this.options.classes),this.options.height&&(this.$tableContainer.addClass("fixed-height"),this.options.showFooter&&this.$tableContainer.addClass("has-footer"),this.options.classes.split(" ").includes("table-bordered")&&(this.$tableBody.append('<div class="fixed-table-border"></div>'),this.$tableBorder=this.$tableBody.find(".fixed-table-border"),this.$tableLoading.addClass("fixed-table-border")),this.$tableFooter=this.$container.find(".fixed-table-footer"))}},{key:"initTable",value:function(){var e=this,i=[];if(this.$header=this.$el.find(">thead"),this.$header.length?this.options.theadClasses&&this.$header.addClass(this.options.theadClasses):this.$header=t('<thead class="'.concat(this.options.theadClasses,'"></thead>')).appendTo(this.$el),this._headerTrClasses=[],this._headerTrStyles=[],this.$header.find("tr").each((function(n,o){var r=t(o),a=[];r.find("th").each((function(e,i){var n=t(i);void 0!==n.data("field")&&n.data("field","".concat(n.data("field")));var o=Object.assign({},n.data());for(var r in o)t.fn.bootstrapTable.columnDefaults.hasOwnProperty(r)&&delete o[r];a.push(dv.extend({},{_data:dv.getRealDataAttr(o),title:n.html(),class:n.attr("class"),titleTooltip:n.attr("title"),rowspan:n.attr("rowspan")?+n.attr("rowspan"):void 0,colspan:n.attr("colspan")?+n.attr("colspan"):void 0},n.data()))})),i.push(a),r.attr("class")&&e._headerTrClasses.push(r.attr("class")),r.attr("style")&&e._headerTrStyles.push(r.attr("style"))})),Array.isArray(this.options.columns[0])||(this.options.columns=[this.options.columns]),this.options.columns=dv.extend(!0,[],i,this.options.columns),this.columns=[],this.fieldsColumnsIndex=[],dv.setFieldIndex(this.options.columns),this.options.columns.forEach((function(t,i){t.forEach((function(t,o){var r=dv.extend({},n.COLUMN_DEFAULTS,t,{passed:t});void 0!==r.fieldIndex&&(e.columns[r.fieldIndex]=r,e.fieldsColumnsIndex[r.field]=r.fieldIndex),e.options.columns[i][o]=r}))})),!this.options.data.length){var o=dv.trToData(this.columns,this.$el.find(">tbody>tr"));o.length&&(this.options.data=o,this.fromHtml=!0)}this.options.pagination&&"server"!==this.options.sidePagination||(this.footerData=dv.trToData(this.columns,this.$el.find(">tfoot>tr"))),this.footerData&&this.$el.find("tfoot").html("<tr></tr>"),!this.options.showFooter||this.options.cardView?this.$tableFooter.hide():this.$tableFooter.show()}},{key:"initHeader",value:function(){var i=this,n={},o=[];this.header={fields:[],styles:[],classes:[],formatters:[],detailFormatters:[],events:[],sorters:[],sortNames:[],cellStyles:[],searchables:[]},dv.updateFieldGroup(this.options.columns,this.columns),this.options.columns.forEach((function(t,a){var s=[];s.push("<tr".concat(dv.sprintf(' class="%s"',i._headerTrClasses[a])," ").concat(dv.sprintf(' style="%s"',i._headerTrStyles[a]),">"));var l="";if(0===a&&dv.hasDetailViewIcon(i.options)){var c=i.options.columns.length>1?' rowspan="'.concat(i.options.columns.length,'"'):"";l='<th class="detail"'.concat(c,'>\n          <div class="fht-cell"></div>\n          </th>')}l&&"right"!==i.options.detailViewAlign&&s.push(l),t.forEach((function(t,o){var l=dv.sprintf(' class="%s"',t.class),c=t.widthUnit,h=parseFloat(t.width),u=t.halign?t.halign:t.align,d=dv.sprintf("text-align: %s; ",u),p=dv.sprintf("text-align: %s; ",t.align),f=dv.sprintf("vertical-align: %s; ",t.valign);if(f+=dv.sprintf("width: %s; ",!t.checkbox&&!t.radio||h?h?h+c:void 0:t.showSelectTitle?void 0:"36px"),void 0!==t.fieldIndex||t.visible){var g=dv.calculateObjectValue(null,i.options.headerStyle,[t]),v=[],b=[],m="";if(g&&g.css)for(var y=0,w=Object.entries(g.css);y<w.length;y++){var S=r(w[y],2),x=S[0],k=S[1];v.push("".concat(x,": ").concat(k))}if(g&&g.classes&&(m=dv.sprintf(' class="%s"',t.class?[t.class,g.classes].join(" "):g.classes)),void 0!==t.fieldIndex){if(i.header.fields[t.fieldIndex]=t.field,i.header.styles[t.fieldIndex]=p+f,i.header.classes[t.fieldIndex]=l,i.header.formatters[t.fieldIndex]=t.formatter,i.header.detailFormatters[t.fieldIndex]=t.detailFormatter,i.header.events[t.fieldIndex]=t.events,i.header.sorters[t.fieldIndex]=t.sorter,i.header.sortNames[t.fieldIndex]=t.sortName,i.header.cellStyles[t.fieldIndex]=t.cellStyle,i.header.searchables[t.fieldIndex]=t.searchable,!t.visible)return;if(i.options.cardView&&!t.cardVisible)return;n[t.field]=t}if(Object.keys(t._data||{}).length>0)for(var O=0,C=Object.entries(t._data);O<C.length;O++){var T=r(C[O],2),I=T[0],P=T[1];b.push("data-".concat(I,"='").concat("object"===e(P)?JSON.stringify(P):P,"'"))}s.push("<th".concat(dv.sprintf(' title="%s"',t.titleTooltip)),t.checkbox||t.radio?dv.sprintf(' class="bs-checkbox %s"',t.class||""):m||l,dv.sprintf(' style="%s"',d+f+v.join("; ")),dv.sprintf(' rowspan="%s"',t.rowspan),dv.sprintf(' colspan="%s"',t.colspan),dv.sprintf(' data-field="%s"',t.field),0===o&&a>0?" data-not-first-th":"",b.length>0?b.join(" "):"",">"),s.push(dv.sprintf('<div class="th-inner %s">',i.options.sortable&&t.sortable?"sortable".concat("center"===u?" sortable-center":""," both"):""));var A=i.options.escape&&i.options.escapeTitle?dv.escapeHTML(t.title):t.title,$=A;t.checkbox&&(A="",!i.options.singleSelect&&i.options.checkboxHeader&&(A='<label><input name="btSelectAll" type="checkbox" /><span></span></label>'),i.header.stateField=t.field),t.radio&&(A="",i.header.stateField=t.field),!A&&t.showSelectTitle&&(A+=$),s.push(A),s.push("</div>"),s.push('<div class="fht-cell"></div>'),s.push("</div>"),s.push("</th>")}})),l&&"right"===i.options.detailViewAlign&&s.push(l),s.push("</tr>"),s.length>3&&o.push(s.join(""))})),this.$header.html(o.join("")),this.$header.find("th[data-field]").each((function(e,i){t(i).data(n[t(i).data("field")])})),this.$container.off("click",".th-inner").on("click",".th-inner",(function(e){var n=t(e.currentTarget);if(i.options.detailView&&!n.parent().hasClass("bs-checkbox")&&n.closest(".bootstrap-table")[0]!==i.$container[0])return!1;i.options.sortable&&n.parent().data().sortable&&i.onSort(e)}));var a=dv.getEventName("resize.bootstrap-table",this.$el.attr("id"));t(window).off(a),!this.options.showHeader||this.options.cardView?(this.$header.hide(),this.$tableHeader.hide(),this.$tableLoading.css("top",0)):(this.$header.show(),this.$tableHeader.show(),this.$tableLoading.css("top",this.$header.outerHeight()+1),this.getCaret(),t(window).on(a,(function(){return i.resetView()}))),this.$selectAll=this.$header.find('[name="btSelectAll"]'),this.$selectAll.off("click").on("click",(function(e){e.stopPropagation();var n=t(e.currentTarget).prop("checked");i[n?"checkAll":"uncheckAll"](),i.updateSelected()}))}},{key:"initData",value:function(t,e){"append"===e?this.options.data=this.options.data.concat(t):"prepend"===e?this.options.data=[].concat(t).concat(this.options.data):(t=t||dv.deepCopy(this.options.data),this.options.data=Array.isArray(t)?t:t[this.options.dataField]),this.data=a(this.options.data),this.options.sortReset&&(this.unsortedData=a(this.data)),"server"!==this.options.sidePagination&&this.initSort()}},{key:"initSort",value:function(){var t=this,e=this.options.sortName,i="desc"===this.options.sortOrder?-1:1,n=this.header.fields.indexOf(this.options.sortName),o=0;-1!==n?(this.options.sortStable&&this.data.forEach((function(t,e){t.hasOwnProperty("_position")||(t._position=e)})),this.options.customSort?dv.calculateObjectValue(this.options,this.options.customSort,[this.options.sortName,this.options.sortOrder,this.data]):this.data.sort((function(o,r){t.header.sortNames[n]&&(e=t.header.sortNames[n]);var a=dv.getItemField(o,e,t.options.escape),s=dv.getItemField(r,e,t.options.escape),l=dv.calculateObjectValue(t.header,t.header.sorters[n],[a,s,o,r]);return void 0!==l?t.options.sortStable&&0===l?i*(o._position-r._position):i*l:dv.sort(a,s,i,t.options,o._position,r._position)})),void 0!==this.options.sortClass&&(clearTimeout(o),o=setTimeout((function(){t.$el.removeClass(t.options.sortClass);var e=t.$header.find('[data-field="'.concat(t.options.sortName,'"]')).index();t.$el.find("tr td:nth-child(".concat(e+1,")")).addClass(t.options.sortClass)}),250))):this.options.sortReset&&(this.data=a(this.unsortedData))}},{key:"onSort",value:function(e){var i=e.type,n=e.currentTarget,o="keypress"===i?t(n):t(n).parent(),r=this.$header.find("th").eq(o.index());if(this.$header.add(this.$header_).find("span.order").remove(),this.options.sortName===o.data("field")){var a=this.options.sortOrder,s=this.columns[this.fieldsColumnsIndex[o.data("field")]].sortOrder||this.columns[this.fieldsColumnsIndex[o.data("field")]].order;void 0===a?this.options.sortOrder="asc":"asc"===a?this.options.sortOrder=this.options.sortReset?"asc"===s?"desc":void 0:"desc":"desc"===this.options.sortOrder&&(this.options.sortOrder=this.options.sortReset?"desc"===s?"asc":void 0:"asc"),void 0===this.options.sortOrder&&(this.options.sortName=void 0)}else this.options.sortName=o.data("field"),this.options.rememberOrder?this.options.sortOrder="asc"===o.data("order")?"desc":"asc":this.options.sortOrder=this.columns[this.fieldsColumnsIndex[o.data("field")]].sortOrder||this.columns[this.fieldsColumnsIndex[o.data("field")]].order;if(this.trigger("sort",this.options.sortName,this.options.sortOrder),o.add(r).data("order",this.options.sortOrder),this.getCaret(),"server"===this.options.sidePagination&&this.options.serverSort)return this.options.pageNumber=1,void this.initServer(this.options.silentSort);this.options.pagination&&this.options.sortResetPage&&(this.options.pageNumber=1,this.initPagination()),this.initSort(),this.initBody()}},{key:"initToolbar",value:function(){var i,n=this,o=this.options,a=[],s=0,l=0;this.$toolbar.find(".bs-bars").children().length&&t("body").append(t(o.toolbar)),this.$toolbar.html(""),"string"!=typeof o.toolbar&&"object"!==e(o.toolbar)||t(dv.sprintf('<div class="bs-bars %s-%s"></div>',this.constants.classes.pull,o.toolbarAlign)).appendTo(this.$toolbar).append(t(o.toolbar)),a=['<div class="'.concat(["columns","columns-".concat(o.buttonsAlign),this.constants.classes.buttonsGroup,"".concat(this.constants.classes.pull,"-").concat(o.buttonsAlign)].join(" "),'">')],"string"==typeof o.buttonsOrder&&(o.buttonsOrder=o.buttonsOrder.replace(/\[|\]| |'/g,"").split(",")),this.buttons=Object.assign(this.buttons,{paginationSwitch:{text:o.pagination?o.formatPaginationSwitchUp():o.formatPaginationSwitchDown(),icon:o.pagination?o.icons.paginationSwitchDown:o.icons.paginationSwitchUp,render:!1,event:this.togglePagination,attributes:{"aria-label":o.formatPaginationSwitch(),title:o.formatPaginationSwitch()}},refresh:{text:o.formatRefresh(),icon:o.icons.refresh,render:!1,event:this.refresh,attributes:{"aria-label":o.formatRefresh(),title:o.formatRefresh()}},toggle:{text:o.formatToggleOn(),icon:o.icons.toggleOff,render:!1,event:this.toggleView,attributes:{"aria-label":o.formatToggleOn(),title:o.formatToggleOn()}},fullscreen:{text:o.formatFullscreen(),icon:o.icons.fullscreen,render:!1,event:this.toggleFullscreen,attributes:{"aria-label":o.formatFullscreen(),title:o.formatFullscreen()}},columns:{render:!1,html:function(){var t=[];if(t.push('<div class="keep-open '.concat(n.constants.classes.buttonsDropdown,'" title="').concat(o.formatColumns(),'">\n            <button class="').concat(n.constants.buttonsClass,' dropdown-toggle" type="button" ').concat(n.constants.dataToggle,'="dropdown"\n            aria-label="').concat(o.formatColumns(),'" title="').concat(o.formatColumns(),'">\n            ').concat(o.showButtonIcons?dv.sprintf(n.constants.html.icon,o.iconsPrefix,o.icons.columns):"","\n            ").concat(o.showButtonText?o.formatColumns():"","\n            ").concat(n.constants.html.dropdownCaret,"\n            </button>\n            ").concat(n.constants.html.toolbarDropdown[0])),o.showColumnsSearch&&(t.push(dv.sprintf(n.constants.html.toolbarDropdownItem,dv.sprintf('<input type="text" class="%s" name="columnsSearch" placeholder="%s" autocomplete="off">',n.constants.classes.input,o.formatSearch()))),t.push(n.constants.html.toolbarDropdownSeparator)),o.showColumnsToggleAll){var e=n.getVisibleColumns().length===n.columns.filter((function(t){return!n.isSelectionColumn(t)})).length;t.push(dv.sprintf(n.constants.html.toolbarDropdownItem,dv.sprintf('<input type="checkbox" class="toggle-all" %s> <span>%s</span>',e?'checked="checked"':"",o.formatColumnsToggleAll()))),t.push(n.constants.html.toolbarDropdownSeparator)}var i=0;return n.columns.forEach((function(t){t.visible&&i++})),n.columns.forEach((function(e,r){if(!n.isSelectionColumn(e)&&(!o.cardView||e.cardVisible)){var a=e.visible?' checked="checked"':"",s=i<=o.minimumCountColumns&&a?' disabled="disabled"':"";e.switchable&&(t.push(dv.sprintf(n.constants.html.toolbarDropdownItem,dv.sprintf('<input type="checkbox" data-field="%s" value="%s"%s%s> <span>%s</span>',e.field,r,a,s,e.title))),l++)}})),t.push(n.constants.html.toolbarDropdown[1],"</div>"),t.join("")}}});for(var h={},u=0,d=Object.entries(this.buttons);u<d.length;u++){var p=r(d[u],2),f=p[0],g=p[1],v=void 0;if(g.hasOwnProperty("html"))"function"==typeof g.html?v=g.html():"string"==typeof g.html&&(v=g.html);else{if(v='<button class="'.concat(this.constants.buttonsClass,'" type="button" name="').concat(f,'"'),g.hasOwnProperty("attributes"))for(var b=0,m=Object.entries(g.attributes);b<m.length;b++){var y=r(m[b],2),w=y[0],S=y[1];v+=" ".concat(w,'="').concat(S,'"')}v+=">",o.showButtonIcons&&g.hasOwnProperty("icon")&&(v+="".concat(dv.sprintf(this.constants.html.icon,o.iconsPrefix,g.icon)," ")),o.showButtonText&&g.hasOwnProperty("text")&&(v+=g.text),v+="</button>"}h[f]=v;var x="show".concat(f.charAt(0).toUpperCase()).concat(f.substring(1)),k=o[x];!(!g.hasOwnProperty("render")||g.hasOwnProperty("render")&&g.render)||void 0!==k&&!0!==k||(o[x]=!0),o.buttonsOrder.includes(f)||o.buttonsOrder.push(f)}var O,C=c(o.buttonsOrder);try{for(C.s();!(O=C.n()).done;){var T=O.value;o["show".concat(T.charAt(0).toUpperCase()).concat(T.substring(1))]&&a.push(h[T])}}catch(t){C.e(t)}finally{C.f()}a.push("</div>"),(this.showToolbar||a.length>2)&&this.$toolbar.append(a.join(""));for(var I=0,P=Object.entries(this.buttons);I<P.length;I++){var A=r(P[I],2),$=A[0],R=A[1];if(R.hasOwnProperty("event")){if("function"==typeof R.event||"string"==typeof R.event)if("continue"===function(){var t="string"==typeof R.event?window[R.event]:R.event;return n.$toolbar.find('button[name="'.concat($,'"]')).off("click").on("click",(function(){return t.call(n)})),"continue"}())continue;for(var E=function(){var t=r(_[j],2),e=t[0],i=t[1],o="string"==typeof i?window[i]:i;n.$toolbar.find('button[name="'.concat($,'"]')).off(e).on(e,(function(){return o.call(n)}))},j=0,_=Object.entries(R.event);j<_.length;j++)E()}}if(o.showColumns){var F=(i=this.$toolbar.find(".keep-open")).find('input[type="checkbox"]:not(".toggle-all")'),D=i.find('input[type="checkbox"].toggle-all');if(l<=o.minimumCountColumns&&i.find("input").prop("disabled",!0),i.find("li, label").off("click").on("click",(function(t){t.stopImmediatePropagation()})),F.off("click").on("click",(function(e){var i=e.currentTarget,o=t(i);n._toggleColumn(o.val(),o.prop("checked"),!1),n.trigger("column-switch",o.data("field"),o.prop("checked")),D.prop("checked",F.filter(":checked").length===n.columns.filter((function(t){return!n.isSelectionColumn(t)})).length)})),D.off("click").on("click",(function(e){var i=e.currentTarget;n._toggleAllColumns(t(i).prop("checked")),n.trigger("column-switch-all",t(i).prop("checked"))})),o.showColumnsSearch){var N=i.find('[name="columnsSearch"]'),V=i.find(".dropdown-item-marker");N.on("keyup paste change",(function(e){var i=e.currentTarget,n=t(i).val().toLowerCase();V.show(),F.each((function(e,i){var o=t(i).parents(".dropdown-item-marker");o.text().toLowerCase().includes(n)||o.hide()}))}))}}var B=function(t){var e=t.is("select")?"change":"keyup drop blur mouseup";t.off(e).on(e,(function(t){o.searchOnEnterKey&&13!==t.keyCode||[37,38,39,40].includes(t.keyCode)||(clearTimeout(s),s=setTimeout((function(){n.onSearch({currentTarget:t.currentTarget})}),o.searchTimeOut))}))};if((o.search||this.showSearchClearButton)&&"string"!=typeof o.searchSelector){a=[];var L=dv.sprintf(this.constants.html.searchButton,this.constants.buttonsClass,o.formatSearch(),o.showButtonIcons?dv.sprintf(this.constants.html.icon,o.iconsPrefix,o.icons.search):"",o.showButtonText?o.formatSearch():""),H=dv.sprintf(this.constants.html.searchClearButton,this.constants.buttonsClass,o.formatClearSearch(),o.showButtonIcons?dv.sprintf(this.constants.html.icon,o.iconsPrefix,o.icons.clearSearch):"",o.showButtonText?o.formatClearSearch():""),M='<input class="'.concat(this.constants.classes.input,"\n        ").concat(dv.sprintf(" %s%s",this.constants.classes.inputPrefix,o.iconSize),'\n        search-input" type="search" aria-label="').concat(o.formatSearch(),'" placeholder="').concat(o.formatSearch(),'" autocomplete="off">'),U=M;if(o.showSearchButton||o.showSearchClearButton){var z=(o.showSearchButton?L:"")+(o.showSearchClearButton?H:"");U=o.search?dv.sprintf(this.constants.html.inputGroup,M,z):z}a.push(dv.sprintf('\n        <div class="'.concat(this.constants.classes.pull,"-").concat(o.searchAlign," search ").concat(this.constants.classes.inputGroup,'">\n          %s\n        </div>\n      '),U)),this.$toolbar.append(a.join(""));var q=dv.getSearchInput(this);o.showSearchButton?(this.$toolbar.find(".search button[name=search]").off("click").on("click",(function(){clearTimeout(s),s=setTimeout((function(){n.onSearch({currentTarget:q})}),o.searchTimeOut)})),o.searchOnEnterKey&&B(q)):B(q),o.showSearchClearButton&&this.$toolbar.find(".search button[name=clearSearch]").click((function(){n.resetSearch()}))}else if("string"==typeof o.searchSelector){B(dv.getSearchInput(this))}}},{key:"onSearch",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=e.currentTarget,n=e.firedByInitSearchText,o=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(void 0!==i&&t(i).length&&o){var r=t(i).val().trim();if(this.options.trimOnSearch&&t(i).val()!==r&&t(i).val(r),this.searchText===r)return;var a=dv.getSearchInput(this),s=i instanceof jQuery?i:t(i);(s.is(a)||s.hasClass("search-input"))&&(this.searchText=r,this.options.searchText=r)}n||(this.options.pageNumber=1),this.initSearch(),n?"client"===this.options.sidePagination&&this.updatePagination():this.updatePagination(),this.trigger("search",this.searchText)}},{key:"initSearch",value:function(){var t=this;if(this.filterOptions=this.filterOptions||this.options.filterOptions,"server"!==this.options.sidePagination){if(this.options.customSearch)return this.data=dv.calculateObjectValue(this.options,this.options.customSearch,[this.options.data,this.searchText,this.filterColumns]),this.options.sortReset&&(this.unsortedData=a(this.data)),void this.initSort();var e=this.searchText&&(this.fromHtml?dv.escapeHTML(this.searchText):this.searchText),i=e?e.toLowerCase():"",n=dv.isEmptyObject(this.filterColumns)?null:this.filterColumns;this.options.searchAccentNeutralise&&(i=dv.normalizeAccent(i)),"function"==typeof this.filterOptions.filterAlgorithm?this.data=this.options.data.filter((function(e){return t.filterOptions.filterAlgorithm.apply(null,[e,n])})):"string"==typeof this.filterOptions.filterAlgorithm&&(this.data=n?this.options.data.filter((function(e){var i=t.filterOptions.filterAlgorithm;if("and"===i){for(var o in n)if(Array.isArray(n[o])&&!n[o].includes(e[o])||!Array.isArray(n[o])&&e[o]!==n[o])return!1}else if("or"===i){var r=!1;for(var a in n)(Array.isArray(n[a])&&n[a].includes(e[a])||!Array.isArray(n[a])&&e[a]===n[a])&&(r=!0);return r}return!0})):a(this.options.data));var o=this.getVisibleFields();this.data=i?this.data.filter((function(n,r){for(var a=0;a<t.header.fields.length;a++)if(t.header.searchables[a]&&(!t.options.visibleSearch||-1!==o.indexOf(t.header.fields[a]))){var s=dv.isNumeric(t.header.fields[a])?parseInt(t.header.fields[a],10):t.header.fields[a],l=t.columns[t.fieldsColumnsIndex[s]],c=void 0;if("string"==typeof s){c=n;for(var h=s.split("."),u=0;u<h.length;u++){if(null===c[h[u]]){c=null;break}c=c[h[u]]}}else c=n[s];if(t.options.searchAccentNeutralise&&(c=dv.normalizeAccent(c)),l&&l.searchFormatter&&(c=dv.calculateObjectValue(l,t.header.formatters[a],[c,n,r,l.field],c)),"string"==typeof c||"number"==typeof c){if(t.options.strictSearch&&"".concat(c).toLowerCase()===i||t.options.regexSearch&&dv.regexCompare(c,e))return!0;var d=/(?:(<=|=>|=<|>=|>|<)(?:\s+)?(-?\d+)?|(-?\d+)?(\s+)?(<=|=>|=<|>=|>|<))/gm.exec(t.searchText),p=!1;if(d){var f=d[1]||"".concat(d[5],"l"),g=d[2]||d[3],v=parseInt(c,10),b=parseInt(g,10);switch(f){case">":case"<l":p=v>b;break;case"<":case">l":p=v<b;break;case"<=":case"=<":case">=l":case"=>l":p=v<=b;break;case">=":case"=>":case"<=l":case"=<l":p=v>=b}}if(p||"".concat(c).toLowerCase().includes(i))return!0}}return!1})):this.data,this.options.sortReset&&(this.unsortedData=a(this.data)),this.initSort()}}},{key:"initPagination",value:function(){var t=this,e=this.options;if(e.pagination){this.$pagination.show();var i,n,o,r,a,s,l,c=[],h=!1,u=this.getData({includeHiddenRows:!1}),d=e.pageList;if("string"==typeof d&&(d=d.replace(/\[|\]| /g,"").toLowerCase().split(",")),d=d.map((function(t){return"string"==typeof t?t.toLowerCase()===e.formatAllRows().toLowerCase()||["all","unlimited"].includes(t.toLowerCase())?e.formatAllRows():+t:t})),this.paginationParts=e.paginationParts,"string"==typeof this.paginationParts&&(this.paginationParts=this.paginationParts.replace(/\[|\]| |'/g,"").split(",")),"server"!==e.sidePagination&&(e.totalRows=u.length),this.totalPages=0,e.totalRows&&(e.pageSize===e.formatAllRows()&&(e.pageSize=e.totalRows,h=!0),this.totalPages=1+~~((e.totalRows-1)/e.pageSize),e.totalPages=this.totalPages),this.totalPages>0&&e.pageNumber>this.totalPages&&(e.pageNumber=this.totalPages),this.pageFrom=(e.pageNumber-1)*e.pageSize+1,this.pageTo=e.pageNumber*e.pageSize,this.pageTo>e.totalRows&&(this.pageTo=e.totalRows),this.options.pagination&&"server"!==this.options.sidePagination&&(this.options.totalNotFiltered=this.options.data.length),this.options.showExtendedPagination||(this.options.totalNotFiltered=void 0),(this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")||this.paginationParts.includes("pageSize"))&&c.push('<div class="'.concat(this.constants.classes.pull,"-").concat(e.paginationDetailHAlign,' pagination-detail">')),this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")){var p=this.paginationParts.includes("pageInfoShort")?e.formatDetailPagination(e.totalRows):e.formatShowingRows(this.pageFrom,this.pageTo,e.totalRows,e.totalNotFiltered);c.push('<span class="pagination-info">\n      '.concat(p,"\n      </span>"))}if(this.paginationParts.includes("pageSize")){c.push('<div class="page-list">');var f=['<div class="'.concat(this.constants.classes.paginationDropdown,'">\n        <button class="').concat(this.constants.buttonsClass,' dropdown-toggle" type="button" ').concat(this.constants.dataToggle,'="dropdown">\n        <span class="page-size">\n        ').concat(h?e.formatAllRows():e.pageSize,"\n        </span>\n        ").concat(this.constants.html.dropdownCaret,"\n        </button>\n        ").concat(this.constants.html.pageDropdown[0])];d.forEach((function(i,n){var o;(!e.smartDisplay||0===n||d[n-1]<e.totalRows||i===e.formatAllRows())&&(o=h?i===e.formatAllRows()?t.constants.classes.dropdownActive:"":i===e.pageSize?t.constants.classes.dropdownActive:"",f.push(dv.sprintf(t.constants.html.pageDropdownItem,o,i)))})),f.push("".concat(this.constants.html.pageDropdown[1],"</div>")),c.push(e.formatRecordsPerPage(f.join("")))}if((this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")||this.paginationParts.includes("pageSize"))&&c.push("</div></div>"),this.paginationParts.includes("pageList")){c.push('<div class="'.concat(this.constants.classes.pull,"-").concat(e.paginationHAlign,' pagination">'),dv.sprintf(this.constants.html.pagination[0],dv.sprintf(" pagination-%s",e.iconSize)),dv.sprintf(this.constants.html.paginationItem," page-pre",e.formatSRPaginationPreText(),e.paginationPreText)),this.totalPages<e.paginationSuccessivelySize?(n=1,o=this.totalPages):o=(n=e.pageNumber-e.paginationPagesBySide)+2*e.paginationPagesBySide,e.pageNumber<e.paginationSuccessivelySize-1&&(o=e.paginationSuccessivelySize),e.paginationSuccessivelySize>this.totalPages-n&&(n=n-(e.paginationSuccessivelySize-(this.totalPages-n))+1),n<1&&(n=1),o>this.totalPages&&(o=this.totalPages);var g=Math.round(e.paginationPagesBySide/2),v=function(i){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return dv.sprintf(t.constants.html.paginationItem,n+(i===e.pageNumber?" ".concat(t.constants.classes.paginationActive):""),e.formatSRPaginationPageText(i),i)};if(n>1){var b=e.paginationPagesBySide;for(b>=n&&(b=n-1),i=1;i<=b;i++)c.push(v(i));n-1===b+1?(i=n-1,c.push(v(i))):n-1>b&&(n-2*e.paginationPagesBySide>e.paginationPagesBySide&&e.paginationUseIntermediate?(i=Math.round((n-g)/2+g),c.push(v(i," page-intermediate"))):c.push(dv.sprintf(this.constants.html.paginationItem," page-first-separator disabled","","...")))}for(i=n;i<=o;i++)c.push(v(i));if(this.totalPages>o){var m=this.totalPages-(e.paginationPagesBySide-1);for(o>=m&&(m=o+1),o+1===m-1?(i=o+1,c.push(v(i))):m>o+1&&(this.totalPages-o>2*e.paginationPagesBySide&&e.paginationUseIntermediate?(i=Math.round((this.totalPages-g-o)/2+o),c.push(v(i," page-intermediate"))):c.push(dv.sprintf(this.constants.html.paginationItem," page-last-separator disabled","","..."))),i=m;i<=this.totalPages;i++)c.push(v(i))}c.push(dv.sprintf(this.constants.html.paginationItem," page-next",e.formatSRPaginationNextText(),e.paginationNextText)),c.push(this.constants.html.pagination[1],"</div>")}this.$pagination.html(c.join(""));var y=["bottom","both"].includes(e.paginationVAlign)?" ".concat(this.constants.classes.dropup):"";this.$pagination.last().find(".page-list > div").addClass(y),e.onlyInfoPagination||(r=this.$pagination.find(".page-list a"),a=this.$pagination.find(".page-pre"),s=this.$pagination.find(".page-next"),l=this.$pagination.find(".page-item").not(".page-next, .page-pre, .page-last-separator, .page-first-separator"),this.totalPages<=1&&this.$pagination.find("div.pagination").hide(),e.smartDisplay&&(d.length<2||e.totalRows<=d[0])&&this.$pagination.find("div.page-list").hide(),this.$pagination[this.getData().length?"show":"hide"](),e.paginationLoop||(1===e.pageNumber&&a.addClass("disabled"),e.pageNumber===this.totalPages&&s.addClass("disabled")),h&&(e.pageSize=e.formatAllRows()),r.off("click").on("click",(function(e){return t.onPageListChange(e)})),a.off("click").on("click",(function(e){return t.onPagePre(e)})),s.off("click").on("click",(function(e){return t.onPageNext(e)})),l.off("click").on("click",(function(e){return t.onPageNumber(e)})))}else this.$pagination.hide()}},{key:"updatePagination",value:function(e){e&&t(e.currentTarget).hasClass("disabled")||(this.options.maintainMetaData||this.resetRows(),this.initPagination(),this.trigger("page-change",this.options.pageNumber,this.options.pageSize),"server"===this.options.sidePagination?this.initServer():this.initBody())}},{key:"onPageListChange",value:function(e){e.preventDefault();var i=t(e.currentTarget);return i.parent().addClass(this.constants.classes.dropdownActive).siblings().removeClass(this.constants.classes.dropdownActive),this.options.pageSize=i.text().toUpperCase()===this.options.formatAllRows().toUpperCase()?this.options.formatAllRows():+i.text(),this.$toolbar.find(".page-size").text(this.options.pageSize),this.updatePagination(e),!1}},{key:"onPagePre",value:function(e){if(!t(e.target).hasClass("disabled"))return e.preventDefault(),this.options.pageNumber-1==0?this.options.pageNumber=this.options.totalPages:this.options.pageNumber--,this.updatePagination(e),!1}},{key:"onPageNext",value:function(e){if(!t(e.target).hasClass("disabled"))return e.preventDefault(),this.options.pageNumber+1>this.options.totalPages?this.options.pageNumber=1:this.options.pageNumber++,this.updatePagination(e),!1}},{key:"onPageNumber",value:function(e){if(e.preventDefault(),this.options.pageNumber!==+t(e.currentTarget).text())return this.options.pageNumber=+t(e.currentTarget).text(),this.updatePagination(e),!1}},{key:"initRow",value:function(t,i,n,o){var a=this,s=[],l={},c=[],h="",u={},d=[];if(!(dv.findIndex(this.hiddenRows,t)>-1)){if((l=dv.calculateObjectValue(this.options,this.options.rowStyle,[t,i],l))&&l.css)for(var p=0,f=Object.entries(l.css);p<f.length;p++){var g=r(f[p],2),v=g[0],b=g[1];c.push("".concat(v,": ").concat(b))}if(u=dv.calculateObjectValue(this.options,this.options.rowAttributes,[t,i],u))for(var m=0,y=Object.entries(u);m<y.length;m++){var w=r(y[m],2),S=w[0],x=w[1];d.push("".concat(S,'="').concat(dv.escapeHTML(x),'"'))}if(t._data&&!dv.isEmptyObject(t._data))for(var k=0,O=Object.entries(t._data);k<O.length;k++){var C=r(O[k],2),T=C[0],I=C[1];if("index"===T)return;h+=" data-".concat(T,"='").concat("object"===e(I)?JSON.stringify(I):I,"'")}s.push("<tr",dv.sprintf(" %s",d.length?d.join(" "):void 0),dv.sprintf(' id="%s"',Array.isArray(t)?void 0:t._id),dv.sprintf(' class="%s"',l.classes||(Array.isArray(t)?void 0:t._class)),dv.sprintf(' style="%s"',Array.isArray(t)?void 0:t._style),' data-index="'.concat(i,'"'),dv.sprintf(' data-uniqueid="%s"',dv.getItemField(t,this.options.uniqueId,!1)),dv.sprintf(' data-has-detail-view="%s"',this.options.detailView&&dv.calculateObjectValue(null,this.options.detailFilter,[i,t])?"true":void 0),dv.sprintf("%s",h),">"),this.options.cardView&&s.push('<td colspan="'.concat(this.header.fields.length,'"><div class="card-views">'));var P="";return dv.hasDetailViewIcon(this.options)&&(P="<td>",dv.calculateObjectValue(null,this.options.detailFilter,[i,t])&&(P+='\n          <a class="detail-icon" href="#">\n          '.concat(dv.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailOpen),"\n          </a>\n        ")),P+="</td>"),P&&"right"!==this.options.detailViewAlign&&s.push(P),this.header.fields.forEach((function(e,n){var o=a.columns[n],l="",h=dv.getItemField(t,e,a.options.escape,o.escape),u="",d="",p={},f="",g=a.header.classes[n],v="",b="",m="",y="",w="",S="";if((!a.fromHtml&&!a.autoMergeCells||void 0!==h||o.checkbox||o.radio)&&o.visible&&(!a.options.cardView||o.cardVisible)){if(c.concat([a.header.styles[n]]).length&&(b+="".concat(c.concat([a.header.styles[n]]).join("; "))),t["_".concat(e,"_style")]&&(b+="".concat(t["_".concat(e,"_style")])),b&&(v=' style="'.concat(b,'"')),t["_".concat(e,"_id")]&&(f=dv.sprintf(' id="%s"',t["_".concat(e,"_id")])),t["_".concat(e,"_class")]&&(g=dv.sprintf(' class="%s"',t["_".concat(e,"_class")])),t["_".concat(e,"_rowspan")]&&(y=dv.sprintf(' rowspan="%s"',t["_".concat(e,"_rowspan")])),t["_".concat(e,"_colspan")]&&(w=dv.sprintf(' colspan="%s"',t["_".concat(e,"_colspan")])),t["_".concat(e,"_title")]&&(S=dv.sprintf(' title="%s"',t["_".concat(e,"_title")])),(p=dv.calculateObjectValue(a.header,a.header.cellStyles[n],[h,t,i,e],p)).classes&&(g=' class="'.concat(p.classes,'"')),p.css){for(var x=[],k=0,O=Object.entries(p.css);k<O.length;k++){var C=r(O[k],2),T=C[0],I=C[1];x.push("".concat(T,": ").concat(I))}v=' style="'.concat(x.concat(a.header.styles[n]).join("; "),'"')}if(u=dv.calculateObjectValue(o,a.header.formatters[n],[h,t,i,e],h),o.checkbox||o.radio||(u=null==u?a.options.undefinedText:u),o.searchable&&a.searchText&&a.options.searchHighlight&&!o.checkbox&&!o.radio){var P="",A=a.searchText.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");if(a.options.searchAccentNeutralise){var $=new RegExp("".concat(dv.normalizeAccent(A)),"gmi").exec(dv.normalizeAccent(u));$&&(A=u.substring($.index,$.index+A.length))}var R=new RegExp("(".concat(A,")"),"gim"),E="<mark>$1</mark>";if(u&&/<(?=.*? .*?\/ ?>|br|hr|input|!--|wbr)[a-z]+.*?>|<([a-z]+).*?<\/\1>/i.test(u)){var j=(new DOMParser).parseFromString(u.toString(),"text/html").documentElement.textContent,_=j.replace(R,E);j=j.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),P=u.replace(new RegExp("(>\\s*)(".concat(j,")(\\s*)"),"gm"),"$1".concat(_,"$3"))}else P=u.toString().replace(R,E);u=dv.calculateObjectValue(o,o.searchHighlightFormatter,[u,a.searchText],P)}if(t["_".concat(e,"_data")]&&!dv.isEmptyObject(t["_".concat(e,"_data")]))for(var F=0,D=Object.entries(t["_".concat(e,"_data")]);F<D.length;F++){var N=r(D[F],2),V=N[0],B=N[1];if("index"===V)return;m+=" data-".concat(V,'="').concat(B,'"')}if(o.checkbox||o.radio){d=o.checkbox?"checkbox":d,d=o.radio?"radio":d;var L=o.class||"",H=dv.isObject(u)&&u.hasOwnProperty("checked")?u.checked:(!0===u||h)&&!1!==u,M=!o.checkboxEnabled||u&&u.disabled;l=[a.options.cardView?'<div class="card-view '.concat(L,'">'):'<td class="bs-checkbox '.concat(L,'"').concat(g).concat(v,">"),'<label>\n            <input\n            data-index="'.concat(i,'"\n            name="').concat(a.options.selectItemName,'"\n            type="').concat(d,'"\n            ').concat(dv.sprintf('value="%s"',t[a.options.idField]),"\n            ").concat(dv.sprintf('checked="%s"',H?"checked":void 0),"\n            ").concat(dv.sprintf('disabled="%s"',M?"disabled":void 0)," />\n            <span></span>\n            </label>"),a.header.formatters[n]&&"string"==typeof u?u:"",a.options.cardView?"</div>":"</td>"].join(""),t[a.header.stateField]=!0===u||!!h||u&&u.checked}else if(a.options.cardView){var U=a.options.showHeader?'<span class="card-view-title '.concat(p.classes||"",'"').concat(v,">").concat(dv.getFieldTitle(a.columns,e),"</span>"):"";l='<div class="card-view">'.concat(U,'<span class="card-view-value ').concat(p.classes||"",'"').concat(v,">").concat(u,"</span></div>"),a.options.smartDisplay&&""===u&&(l='<div class="card-view"></div>')}else l="<td".concat(f).concat(g).concat(v).concat(m).concat(y).concat(w).concat(S,">").concat(u,"</td>");s.push(l)}})),P&&"right"===this.options.detailViewAlign&&s.push(P),this.options.cardView&&s.push("</div></td>"),s.push("</tr>"),s.join("")}}},{key:"initBody",value:function(e,i){var n=this,o=this.getData();this.trigger("pre-body",o),this.$body=this.$el.find(">tbody"),this.$body.length||(this.$body=t("<tbody></tbody>").appendTo(this.$el)),this.options.pagination&&"server"!==this.options.sidePagination||(this.pageFrom=1,this.pageTo=o.length);var r=[],a=t(document.createDocumentFragment()),s=!1,l=[];this.autoMergeCells=dv.checkAutoMergeCells(o.slice(this.pageFrom-1,this.pageTo));for(var c=this.pageFrom-1;c<this.pageTo;c++){var h=o[c],u=this.initRow(h,c,o,a);if(s=s||!!u,u&&"string"==typeof u){var d=this.options.uniqueId;if(d&&h.hasOwnProperty(d)){var p=h[d],f=this.$body.find(dv.sprintf('> tr[data-uniqueid="%s"][data-has-detail-view]',p)).next();f.is("tr.detail-view")&&(l.push(c),i&&p===i||(u+=f[0].outerHTML))}this.options.virtualScroll?r.push(u):a.append(u)}}s?this.options.virtualScroll?(this.virtualScroll&&this.virtualScroll.destroy(),this.virtualScroll=new yv({rows:r,fixedScroll:e,scrollEl:this.$tableBody[0],contentEl:this.$body[0],itemHeight:this.options.virtualScrollItemHeight,callback:function(t,e){n.fitHeader(),n.initBodyEvent(),n.trigger("virtual-scroll",t,e)}})):this.$body.html(a):this.$body.html('<tr class="no-records-found">'.concat(dv.sprintf('<td colspan="%s">%s</td>',this.getVisibleFields().length+dv.getDetailViewIndexOffset(this.options),this.options.formatNoMatches()),"</tr>")),l.forEach((function(t){n.expandRow(t)})),e||this.scrollTo(0),this.initBodyEvent(),this.initFooter(),this.resetView(),this.updateSelected(),"server"!==this.options.sidePagination&&(this.options.totalRows=o.length),this.trigger("post-body",o)}},{key:"initBodyEvent",value:function(){var e=this;this.$body.find("> tr[data-index] > td").off("click dblclick").on("click dblclick",(function(i){var n=t(i.currentTarget);if(!(n.find(".detail-icon").length||n.index()-dv.getDetailViewIndexOffset(e.options)<0)){var o=n.parent(),r=t(i.target).parents(".card-views").children(),a=t(i.target).parents(".card-view"),s=o.data("index"),l=e.data[s],c=e.options.cardView?r.index(a):n[0].cellIndex,h=e.getVisibleFields()[c-dv.getDetailViewIndexOffset(e.options)],u=e.columns[e.fieldsColumnsIndex[h]],d=dv.getItemField(l,h,e.options.escape,u.escape);if(e.trigger("click"===i.type?"click-cell":"dbl-click-cell",h,d,l,n),e.trigger("click"===i.type?"click-row":"dbl-click-row",l,o,h),"click"===i.type&&e.options.clickToSelect&&u.clickToSelect&&!dv.calculateObjectValue(e.options,e.options.ignoreClickToSelectOn,[i.target])){var p=o.find(dv.sprintf('[name="%s"]',e.options.selectItemName));p.length&&p[0].click()}"click"===i.type&&e.options.detailViewByClick&&e.toggleDetailView(s,e.header.detailFormatters[e.fieldsColumnsIndex[h]])}})).off("mousedown").on("mousedown",(function(t){e.multipleSelectRowCtrlKey=t.ctrlKey||t.metaKey,e.multipleSelectRowShiftKey=t.shiftKey})),this.$body.find("> tr[data-index] > td > .detail-icon").off("click").on("click",(function(i){return i.preventDefault(),e.toggleDetailView(t(i.currentTarget).parent().parent().data("index")),!1})),this.$selectItem=this.$body.find(dv.sprintf('[name="%s"]',this.options.selectItemName)),this.$selectItem.off("click").on("click",(function(i){i.stopImmediatePropagation();var n=t(i.currentTarget);e._toggleCheck(n.prop("checked"),n.data("index"))})),this.header.events.forEach((function(i,n){var o=i;if(o){if("string"==typeof o&&(o=dv.calculateObjectValue(null,o)),!o)throw new Error("Unknown event in the scope: ".concat(i));var r=e.header.fields[n],a=e.getVisibleFields().indexOf(r);if(-1!==a){a+=dv.getDetailViewIndexOffset(e.options);var s=function(i){if(!o.hasOwnProperty(i))return"continue";var n=o[i];e.$body.find(">tr:not(.no-records-found)").each((function(o,s){var l=t(s),c=l.find(e.options.cardView?".card-views>.card-view":">td").eq(a),h=i.indexOf(" "),u=i.substring(0,h),d=i.substring(h+1);c.find(d).off(u).on(u,(function(t){var i=l.data("index"),o=e.data[i],a=o[r];n.apply(e,[t,a,o,i])}))}))};for(var l in o)s(l)}}}))}},{key:"initServer",value:function(e,i,n){var o=this,r={},a=this.header.fields.indexOf(this.options.sortName),s={searchText:this.searchText,sortName:this.options.sortName,sortOrder:this.options.sortOrder};if(this.header.sortNames[a]&&(s.sortName=this.header.sortNames[a]),this.options.pagination&&"server"===this.options.sidePagination&&(s.pageSize=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize,s.pageNumber=this.options.pageNumber),n||this.options.url||this.options.ajax){if("limit"===this.options.queryParamsType&&(s={search:s.searchText,sort:s.sortName,order:s.sortOrder},this.options.pagination&&"server"===this.options.sidePagination&&(s.offset=this.options.pageSize===this.options.formatAllRows()?0:this.options.pageSize*(this.options.pageNumber-1),s.limit=this.options.pageSize,0!==s.limit&&this.options.pageSize!==this.options.formatAllRows()||delete s.limit)),this.options.search&&"server"===this.options.sidePagination&&this.options.searchable&&this.columns.filter((function(t){return t.searchable})).length){s.searchable=[];var l,h=c(this.columns);try{for(h.s();!(l=h.n()).done;){var u=l.value;!u.checkbox&&u.searchable&&(this.options.visibleSearch&&u.visible||!this.options.visibleSearch)&&s.searchable.push(u.field)}}catch(t){h.e(t)}finally{h.f()}}if(dv.isEmptyObject(this.filterColumnsPartial)||(s.filter=JSON.stringify(this.filterColumnsPartial,null)),dv.extend(s,i||{}),!1!==(r=dv.calculateObjectValue(this.options,this.options.queryParams,[s],r))){e||this.showLoading();var d=dv.extend({},dv.calculateObjectValue(null,this.options.ajaxOptions),{type:this.options.method,url:n||this.options.url,data:"application/json"===this.options.contentType&&"post"===this.options.method?JSON.stringify(r):r,cache:this.options.cache,contentType:this.options.contentType,dataType:this.options.dataType,success:function(t,i,n){var r=dv.calculateObjectValue(o.options,o.options.responseHandler,[t,n],t);o.load(r),o.trigger("load-success",r,n&&n.status,n),e||o.hideLoading(),"server"===o.options.sidePagination&&o.options.pageNumber>1&&r[o.options.totalField]>0&&!r[o.options.dataField].length&&o.updatePagination()},error:function(t){if(t&&0===t.status&&o._xhrAbort)o._xhrAbort=!1;else{var i=[];"server"===o.options.sidePagination&&((i={})[o.options.totalField]=0,i[o.options.dataField]=[]),o.load(i),o.trigger("load-error",t&&t.status,t),e||o.hideLoading()}}});return this.options.ajax?dv.calculateObjectValue(this,this.options.ajax,[d],null):(this._xhr&&4!==this._xhr.readyState&&(this._xhrAbort=!0,this._xhr.abort()),this._xhr=t.ajax(d)),r}}}},{key:"initSearchText",value:function(){if(this.options.search&&(this.searchText="",""!==this.options.searchText)){var t=dv.getSearchInput(this);t.val(this.options.searchText),this.onSearch({currentTarget:t,firedByInitSearchText:!0})}}},{key:"getCaret",value:function(){var e=this;this.$header.find("th").each((function(i,n){t(n).find(".sortable").removeClass("desc asc").addClass(t(n).data("field")===e.options.sortName?e.options.sortOrder:"both")}))}},{key:"updateSelected",value:function(){var e=this.$selectItem.filter(":enabled").length&&this.$selectItem.filter(":enabled").length===this.$selectItem.filter(":enabled").filter(":checked").length;this.$selectAll.add(this.$selectAll_).prop("checked",e),this.$selectItem.each((function(e,i){t(i).closest("tr")[t(i).prop("checked")?"addClass":"removeClass"]("selected")}))}},{key:"updateRows",value:function(){var e=this;this.$selectItem.each((function(i,n){e.data[t(n).data("index")][e.header.stateField]=t(n).prop("checked")}))}},{key:"resetRows",value:function(){var t,e=c(this.data);try{for(e.s();!(t=e.n()).done;){var i=t.value;this.$selectAll.prop("checked",!1),this.$selectItem.prop("checked",!1),this.header.stateField&&(i[this.header.stateField]=!1)}}catch(t){e.e(t)}finally{e.f()}this.initHiddenRows()}},{key:"trigger",value:function(e){for(var i,o,r="".concat(e,".bs.table"),a=arguments.length,s=new Array(a>1?a-1:0),l=1;l<a;l++)s[l-1]=arguments[l];(i=this.options)[n.EVENTS[r]].apply(i,[].concat(s,[this])),this.$el.trigger(t.Event(r,{sender:this}),s),(o=this.options).onAll.apply(o,[r].concat([].concat(s,[this]))),this.$el.trigger(t.Event("all.bs.table",{sender:this}),[r,s])}},{key:"resetHeader",value:function(){var t=this;clearTimeout(this.timeoutId_),this.timeoutId_=setTimeout((function(){return t.fitHeader()}),this.$el.is(":hidden")?100:0)}},{key:"fitHeader",value:function(){var e=this;if(this.$el.is(":hidden"))this.timeoutId_=setTimeout((function(){return e.fitHeader()}),100);else{var i=this.$tableBody.get(0),n=this.hasScrollBar&&i.scrollHeight>i.clientHeight+this.$header.outerHeight()?dv.getScrollBarWidth():0;this.$el.css("margin-top",-this.$header.outerHeight());var o=this.$tableHeader.find(":focus");if(o.length>0){var r=o.parents("th");if(r.length>0){var a=r.attr("data-field");if(void 0!==a){var s=this.$header.find("[data-field='".concat(a,"']"));s.length>0&&s.find(":input").addClass("focus-temp")}}}this.$header_=this.$header.clone(!0,!0),this.$selectAll_=this.$header_.find('[name="btSelectAll"]'),this.$tableHeader.css("margin-right",n).find("table").css("width",this.$el.outerWidth()).html("").attr("class",this.$el.attr("class")).append(this.$header_),this.$tableLoading.css("width",this.$el.outerWidth());var l=t(".focus-temp:visible:eq(0)");l.length>0&&(l.focus(),this.$header.find(".focus-temp").removeClass("focus-temp")),this.$header.find("th[data-field]").each((function(i,n){e.$header_.find(dv.sprintf('th[data-field="%s"]',t(n).data("field"))).data(t(n).data())}));for(var c=this.getVisibleFields(),h=this.$header_.find("th"),u=this.$body.find(">tr:not(.no-records-found,.virtual-scroll-top)").eq(0);u.length&&u.find('>td[colspan]:not([colspan="1"])').length;)u=u.next();var d=u.find("> *").length;u.find("> *").each((function(i,n){var o=t(n);if(dv.hasDetailViewIcon(e.options)&&(0===i&&"right"!==e.options.detailViewAlign||i===d-1&&"right"===e.options.detailViewAlign)){var r=h.filter(".detail"),a=r.innerWidth()-r.find(".fht-cell").width();r.find(".fht-cell").width(o.innerWidth()-a)}else{var s=i-dv.getDetailViewIndexOffset(e.options),l=e.$header_.find(dv.sprintf('th[data-field="%s"]',c[s]));l.length>1&&(l=t(h[o[0].cellIndex]));var u=l.innerWidth()-l.find(".fht-cell").width();l.find(".fht-cell").width(o.innerWidth()-u)}})),this.horizontalScroll(),this.trigger("post-header")}}},{key:"initFooter",value:function(){if(this.options.showFooter&&!this.options.cardView){var t=this.getData(),e=[],i="";dv.hasDetailViewIcon(this.options)&&(i='<th class="detail"><div class="th-inner"></div><div class="fht-cell"></div></th>'),i&&"right"!==this.options.detailViewAlign&&e.push(i);var n,o=c(this.columns);try{for(o.s();!(n=o.n()).done;){var a,s,l=n.value,h=[],u={},d=dv.sprintf(' class="%s"',l.class);if(!(!l.visible||this.footerData&&this.footerData.length>0&&!(l.field in this.footerData[0]))){if(this.options.cardView&&!l.cardVisible)return;if(a=dv.sprintf("text-align: %s; ",l.falign?l.falign:l.align),s=dv.sprintf("vertical-align: %s; ",l.valign),(u=dv.calculateObjectValue(null,this.options.footerStyle,[l]))&&u.css)for(var p=0,f=Object.entries(u.css);p<f.length;p++){var g=r(f[p],2),v=g[0],b=g[1];h.push("".concat(v,": ").concat(b))}u&&u.classes&&(d=dv.sprintf(' class="%s"',l.class?[l.class,u.classes].join(" "):u.classes)),e.push("<th",d,dv.sprintf(' style="%s"',a+s+h.concat().join("; ")));var m=0;this.footerData&&this.footerData.length>0&&(m=this.footerData[0]["_".concat(l.field,"_colspan")]||0),m&&e.push(' colspan="'.concat(m,'" ')),e.push(">"),e.push('<div class="th-inner">');var y="";this.footerData&&this.footerData.length>0&&(y=this.footerData[0][l.field]||""),e.push(dv.calculateObjectValue(l,l.footerFormatter,[t,y],y)),e.push("</div>"),e.push('<div class="fht-cell"></div>'),e.push("</div>"),e.push("</th>")}}}catch(t){o.e(t)}finally{o.f()}i&&"right"===this.options.detailViewAlign&&e.push(i),this.options.height||this.$tableFooter.length||(this.$el.append("<tfoot><tr></tr></tfoot>"),this.$tableFooter=this.$el.find("tfoot")),this.$tableFooter.find("tr").length||this.$tableFooter.html("<table><thead><tr></tr></thead></table>"),this.$tableFooter.find("tr").html(e.join("")),this.trigger("post-footer",this.$tableFooter)}}},{key:"fitFooter",value:function(){var e=this;if(this.$el.is(":hidden"))setTimeout((function(){return e.fitFooter()}),100);else{var i=this.$tableBody.get(0),n=this.hasScrollBar&&i.scrollHeight>i.clientHeight+this.$header.outerHeight()?dv.getScrollBarWidth():0;this.$tableFooter.css("margin-right",n).find("table").css("width",this.$el.outerWidth()).attr("class",this.$el.attr("class"));var o=this.$tableFooter.find("th"),r=this.$body.find(">tr:first-child:not(.no-records-found)");for(o.find(".fht-cell").width("auto");r.length&&r.find('>td[colspan]:not([colspan="1"])').length;)r=r.next();var a=r.find("> *").length;r.find("> *").each((function(i,n){var r=t(n);if(dv.hasDetailViewIcon(e.options)&&(0===i&&"left"===e.options.detailViewAlign||i===a-1&&"right"===e.options.detailViewAlign)){var s=o.filter(".detail"),l=s.innerWidth()-s.find(".fht-cell").width();s.find(".fht-cell").width(r.innerWidth()-l)}else{var c=o.eq(i),h=c.innerWidth()-c.find(".fht-cell").width();c.find(".fht-cell").width(r.innerWidth()-h)}})),this.horizontalScroll()}}},{key:"horizontalScroll",value:function(){var t=this;this.$tableBody.off("scroll").on("scroll",(function(){var e=t.$tableBody.scrollLeft();t.options.showHeader&&t.options.height&&t.$tableHeader.scrollLeft(e),t.options.showFooter&&!t.options.cardView&&t.$tableFooter.scrollLeft(e),t.trigger("scroll-body",t.$tableBody)}))}},{key:"getVisibleFields",value:function(){var t,e=[],i=c(this.header.fields);try{for(i.s();!(t=i.n()).done;){var n=t.value,o=this.columns[this.fieldsColumnsIndex[n]];o&&o.visible&&(!this.options.cardView||o.cardVisible)&&e.push(n)}}catch(t){i.e(t)}finally{i.f()}return e}},{key:"initHiddenRows",value:function(){this.hiddenRows=[]}},{key:"getOptions",value:function(){var t=dv.extend({},this.options);return delete t.data,dv.extend(!0,{},t)}},{key:"refreshOptions",value:function(t){dv.compareObjects(this.options,t,!0)||(this.options=dv.extend(this.options,t),this.trigger("refresh-options",this.options),this.destroy(),this.init())}},{key:"getData",value:function(t){var e=this,i=this.options.data;if(!(this.searchText||this.options.customSearch||void 0!==this.options.sortName||this.enableCustomSort)&&dv.isEmptyObject(this.filterColumns)&&"function"!=typeof this.options.filterOptions.filterAlgorithm&&dv.isEmptyObject(this.filterColumnsPartial)||t&&t.unfiltered||(i=this.data),t&&!t.includeHiddenRows){var n=this.getHiddenRows();i=i.filter((function(t){return-1===dv.findIndex(n,t)}))}return t&&t.useCurrentPage&&(i=i.slice(this.pageFrom-1,this.pageTo)),t&&t.formatted&&i.forEach((function(t){for(var i=0,n=Object.entries(t);i<n.length;i++){var o=r(n[i],2),a=o[0],s=o[1],l=e.columns[e.fieldsColumnsIndex[a]];if(!l)return;t[a]=dv.calculateObjectValue(l,e.header.formatters[l.fieldIndex],[s,t,t.index,l.field],s)}})),i}},{key:"getSelections",value:function(){var t=this;return(this.options.maintainMetaData?this.options.data:this.data).filter((function(e){return!0===e[t.header.stateField]}))}},{key:"load",value:function(t){var e,i=t;this.options.pagination&&"server"===this.options.sidePagination&&(this.options.totalRows=i[this.options.totalField],this.options.totalNotFiltered=i[this.options.totalNotFilteredField],this.footerData=i[this.options.footerField]?[i[this.options.footerField]]:void 0),e=i.fixedScroll,i=Array.isArray(i)?i:i[this.options.dataField],this.initData(i),this.initSearch(),this.initPagination(),this.initBody(e)}},{key:"append",value:function(t){this.initData(t,"append"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"prepend",value:function(t){this.initData(t,"prepend"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"remove",value:function(t){for(var e=0,i=this.options.data.length-1;i>=0;i--){var n=this.options.data[i],o=dv.getItemField(n,t.field,this.options.escape,n.escape);void 0===o&&"$index"!==t.field||(!n.hasOwnProperty(t.field)&&"$index"===t.field&&t.values.includes(i)||t.values.includes(o))&&(e++,this.options.data.splice(i,1))}e&&("server"===this.options.sidePagination&&(this.options.totalRows-=e,this.data=a(this.options.data)),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))}},{key:"removeAll",value:function(){this.options.data.length>0&&(this.options.data.splice(0,this.options.data.length),this.initSearch(),this.initPagination(),this.initBody(!0))}},{key:"insertRow",value:function(t){t.hasOwnProperty("index")&&t.hasOwnProperty("row")&&(this.options.data.splice(t.index,0,t.row),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))}},{key:"updateRow",value:function(t){var e,i=c(Array.isArray(t)?t:[t]);try{for(i.s();!(e=i.n()).done;){var n=e.value;n.hasOwnProperty("index")&&n.hasOwnProperty("row")&&(n.hasOwnProperty("replace")&&n.replace?this.options.data[n.index]=n.row:dv.extend(this.options.data[n.index],n.row))}}catch(t){i.e(t)}finally{i.f()}this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"getRowByUniqueId",value:function(t){var e,i,n=this.options.uniqueId,o=t,r=null;for(e=this.options.data.length-1;e>=0;e--){i=this.options.data[e];var a=dv.getItemField(i,n,this.options.escape,i.escape);if(void 0!==a&&("string"==typeof a?o=o.toString():"number"==typeof a&&(Number(a)===a&&a%1==0?o=parseInt(o,10):a===Number(a)&&0!==a&&(o=parseFloat(o))),a===o)){r=i;break}}return r}},{key:"updateByUniqueId",value:function(t){var e,i=null,n=c(Array.isArray(t)?t:[t]);try{for(n.s();!(e=n.n()).done;){var o=e.value;if(o.hasOwnProperty("id")&&o.hasOwnProperty("row")){var r=this.options.data.indexOf(this.getRowByUniqueId(o.id));-1!==r&&(o.hasOwnProperty("replace")&&o.replace?this.options.data[r]=o.row:dv.extend(this.options.data[r],o.row),i=o.id)}}}catch(t){n.e(t)}finally{n.f()}this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0,i)}},{key:"removeByUniqueId",value:function(t){var e=this.options.data.length,i=this.getRowByUniqueId(t);i&&this.options.data.splice(this.options.data.indexOf(i),1),e!==this.options.data.length&&("server"===this.options.sidePagination&&(this.options.totalRows-=1,this.data=a(this.options.data)),this.initSearch(),this.initPagination(),this.initBody(!0))}},{key:"_updateCellOnly",value:function(e,i){var n=this.initRow(this.options.data[i],i),o=this.getVisibleFields().indexOf(e);-1!==o&&(o+=dv.getDetailViewIndexOffset(this.options),this.$body.find(">tr[data-index=".concat(i,"]")).find(">td:eq(".concat(o,")")).replaceWith(t(n).find(">td:eq(".concat(o,")"))),this.initBodyEvent(),this.initFooter(),this.resetView(),this.updateSelected())}},{key:"updateCell",value:function(t){t.hasOwnProperty("index")&&t.hasOwnProperty("field")&&t.hasOwnProperty("value")&&(this.options.data[t.index][t.field]=t.value,!1!==t.reinit?(this.initSort(),this.initBody(!0)):this._updateCellOnly(t.field,t.index))}},{key:"updateCellByUniqueId",value:function(t){var e=this;(Array.isArray(t)?t:[t]).forEach((function(t){var i=t.id,n=t.field,o=t.value,r=e.options.data.indexOf(e.getRowByUniqueId(i));-1!==r&&(e.options.data[r][n]=o)})),!1!==t.reinit?(this.initSort(),this.initBody(!0)):this._updateCellOnly(t.field,this.options.data.indexOf(this.getRowByUniqueId(t.id)))}},{key:"showRow",value:function(t){this._toggleRow(t,!0)}},{key:"hideRow",value:function(t){this._toggleRow(t,!1)}},{key:"_toggleRow",value:function(t,e){var i;if(t.hasOwnProperty("index")?i=this.getData()[t.index]:t.hasOwnProperty("uniqueId")&&(i=this.getRowByUniqueId(t.uniqueId)),i){var n=dv.findIndex(this.hiddenRows,i);e||-1!==n?e&&n>-1&&this.hiddenRows.splice(n,1):this.hiddenRows.push(i),this.initBody(!0),this.initPagination()}}},{key:"getHiddenRows",value:function(t){if(t)return this.initHiddenRows(),this.initBody(!0),void this.initPagination();var e,i=[],n=c(this.getData());try{for(n.s();!(e=n.n()).done;){var o=e.value;this.hiddenRows.includes(o)&&i.push(o)}}catch(t){n.e(t)}finally{n.f()}return this.hiddenRows=i,i}},{key:"showColumn",value:function(t){var e=this;(Array.isArray(t)?t:[t]).forEach((function(t){e._toggleColumn(e.fieldsColumnsIndex[t],!0,!0)}))}},{key:"hideColumn",value:function(t){var e=this;(Array.isArray(t)?t:[t]).forEach((function(t){e._toggleColumn(e.fieldsColumnsIndex[t],!1,!0)}))}},{key:"_toggleColumn",value:function(t,e,i){if(-1!==t&&this.columns[t].visible!==e&&(this.columns[t].visible=e,this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns)){var n=this.$toolbar.find('.keep-open input:not(".toggle-all")').prop("disabled",!1);i&&n.filter(dv.sprintf('[value="%s"]',t)).prop("checked",e),n.filter(":checked").length<=this.options.minimumCountColumns&&n.filter(":checked").prop("disabled",!0)}}},{key:"getVisibleColumns",value:function(){var t=this;return this.columns.filter((function(e){return e.visible&&!t.isSelectionColumn(e)}))}},{key:"getHiddenColumns",value:function(){return this.columns.filter((function(t){return!t.visible}))}},{key:"isSelectionColumn",value:function(t){return t.radio||t.checkbox}},{key:"showAllColumns",value:function(){this._toggleAllColumns(!0)}},{key:"hideAllColumns",value:function(){this._toggleAllColumns(!1)}},{key:"_toggleAllColumns",value:function(e){var i,n=this,o=c(this.columns.slice().reverse());try{for(o.s();!(i=o.n()).done;){var r=i.value;if(r.switchable){if(!e&&this.options.showColumns&&this.getVisibleColumns().filter((function(t){return t.switchable})).length===this.options.minimumCountColumns)continue;r.visible=e}}}catch(t){o.e(t)}finally{o.f()}if(this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns){var a=this.$toolbar.find('.keep-open input[type="checkbox"]:not(".toggle-all")').prop("disabled",!1);e?a.prop("checked",e):a.get().reverse().forEach((function(i){a.filter(":checked").length>n.options.minimumCountColumns&&t(i).prop("checked",e)})),a.filter(":checked").length<=this.options.minimumCountColumns&&a.filter(":checked").prop("disabled",!0)}}},{key:"mergeCells",value:function(t){var e,i,n=t.index,o=this.getVisibleFields().indexOf(t.field),r=t.rowspan||1,a=t.colspan||1,s=this.$body.find(">tr[data-index]");o+=dv.getDetailViewIndexOffset(this.options);var l=s.eq(n).find(">td").eq(o);if(!(n<0||o<0||n>=this.data.length)){for(e=n;e<n+r;e++)for(i=o;i<o+a;i++)s.eq(e).find(">td").eq(i).hide();l.attr("rowspan",r).attr("colspan",a).show()}}},{key:"checkAll",value:function(){this._toggleCheckAll(!0)}},{key:"uncheckAll",value:function(){this._toggleCheckAll(!1)}},{key:"_toggleCheckAll",value:function(t){var e=this.getSelections();this.$selectAll.add(this.$selectAll_).prop("checked",t),this.$selectItem.filter(":enabled").prop("checked",t),this.updateRows(),this.updateSelected();var i=this.getSelections();t?this.trigger("check-all",i,e):this.trigger("uncheck-all",i,e)}},{key:"checkInvert",value:function(){var e=this.$selectItem.filter(":enabled"),i=e.filter(":checked");e.each((function(e,i){t(i).prop("checked",!t(i).prop("checked"))})),this.updateRows(),this.updateSelected(),this.trigger("uncheck-some",i),i=this.getSelections(),this.trigger("check-some",i)}},{key:"check",value:function(t){this._toggleCheck(!0,t)}},{key:"uncheck",value:function(t){this._toggleCheck(!1,t)}},{key:"_toggleCheck",value:function(t,e){var i=this.$selectItem.filter('[data-index="'.concat(e,'"]')),n=this.data[e];if(i.is(":radio")||this.options.singleSelect||this.options.multipleSelectRow&&!this.multipleSelectRowCtrlKey&&!this.multipleSelectRowShiftKey){var o,a=c(this.options.data);try{for(a.s();!(o=a.n()).done;){o.value[this.header.stateField]=!1}}catch(t){a.e(t)}finally{a.f()}this.$selectItem.filter(":checked").not(i).prop("checked",!1)}if(n[this.header.stateField]=t,this.options.multipleSelectRow){if(this.multipleSelectRowShiftKey&&this.multipleSelectRowLastSelectedIndex>=0)for(var s=r(this.multipleSelectRowLastSelectedIndex<e?[this.multipleSelectRowLastSelectedIndex,e]:[e,this.multipleSelectRowLastSelectedIndex],2),l=s[0],h=s[1],u=l+1;u<h;u++)this.data[u][this.header.stateField]=!0,this.$selectItem.filter('[data-index="'.concat(u,'"]')).prop("checked",!0);this.multipleSelectRowCtrlKey=!1,this.multipleSelectRowShiftKey=!1,this.multipleSelectRowLastSelectedIndex=t?e:-1}i.prop("checked",t),this.updateSelected(),this.trigger(t?"check":"uncheck",this.data[e],i)}},{key:"checkBy",value:function(t){this._toggleCheckBy(!0,t)}},{key:"uncheckBy",value:function(t){this._toggleCheckBy(!1,t)}},{key:"_toggleCheckBy",value:function(t,e){var i=this;if(e.hasOwnProperty("field")&&e.hasOwnProperty("values")){var n=[];this.data.forEach((function(o,r){if(!o.hasOwnProperty(e.field))return!1;if(e.values.includes(o[e.field])){var a=i.$selectItem.filter(":enabled").filter(dv.sprintf('[data-index="%s"]',r)),s=!!e.hasOwnProperty("onlyCurrentPage")&&e.onlyCurrentPage;if(!(a=t?a.not(":checked"):a.filter(":checked")).length&&s)return;a.prop("checked",t),o[i.header.stateField]=t,n.push(o),i.trigger(t?"check":"uncheck",o,a)}})),this.updateSelected(),this.trigger(t?"check-some":"uncheck-some",n)}}},{key:"refresh",value:function(t){t&&t.url&&(this.options.url=t.url),t&&t.pageNumber&&(this.options.pageNumber=t.pageNumber),t&&t.pageSize&&(this.options.pageSize=t.pageSize),this.trigger("refresh",this.initServer(t&&t.silent,t&&t.query,t&&t.url))}},{key:"destroy",value:function(){this.$el.insertBefore(this.$container),t(this.options.toolbar).insertBefore(this.$el),this.$container.next().remove(),this.$container.remove(),this.$el.html(this.$el_.html()).css("margin-top","0").attr("class",this.$el_.attr("class")||"");var e=dv.getEventName("resize.bootstrap-table",this.$el.attr("id"));t(window).off(e)}},{key:"resetView",value:function(t){var e=0;if(t&&t.height&&(this.options.height=t.height),this.$tableContainer.toggleClass("has-card-view",this.options.cardView),this.options.height){var i=this.$tableBody.get(0);this.hasScrollBar=i.scrollWidth>i.clientWidth}if(!this.options.cardView&&this.options.showHeader&&this.options.height?(this.$tableHeader.show(),this.resetHeader(),e+=this.$header.outerHeight(!0)+1):(this.$tableHeader.hide(),this.trigger("post-header")),!this.options.cardView&&this.options.showFooter&&(this.$tableFooter.show(),this.fitFooter(),this.options.height&&(e+=this.$tableFooter.outerHeight(!0))),this.$container.hasClass("fullscreen"))this.$tableContainer.css("height",""),this.$tableContainer.css("width","");else if(this.options.height){this.$tableBorder&&(this.$tableBorder.css("width",""),this.$tableBorder.css("height",""));var n=this.$toolbar.outerHeight(!0),o=this.$pagination.outerHeight(!0),r=this.options.height-n-o,a=this.$tableBody.find(">table"),s=a.outerHeight();if(this.$tableContainer.css("height","".concat(r,"px")),this.$tableBorder&&a.is(":visible")){var l=r-s-2;this.hasScrollBar&&(l-=dv.getScrollBarWidth()),this.$tableBorder.css("width","".concat(a.outerWidth(),"px")),this.$tableBorder.css("height","".concat(l,"px"))}}this.options.cardView?(this.$el.css("margin-top","0"),this.$tableContainer.css("padding-bottom","0"),this.$tableFooter.hide()):(this.getCaret(),this.$tableContainer.css("padding-bottom","".concat(e,"px"))),this.trigger("reset-view")}},{key:"showLoading",value:function(){this.$tableLoading.toggleClass("open",!0);var t=this.options.loadingFontSize;"auto"===this.options.loadingFontSize&&(t=.04*this.$tableLoading.width(),t=Math.max(12,t),t=Math.min(32,t),t="".concat(t,"px")),this.$tableLoading.find(".loading-text").css("font-size",t)}},{key:"hideLoading",value:function(){this.$tableLoading.toggleClass("open",!1)}},{key:"togglePagination",value:function(){this.options.pagination=!this.options.pagination;var t=this.options.showButtonIcons?this.options.pagination?this.options.icons.paginationSwitchDown:this.options.icons.paginationSwitchUp:"",e=this.options.showButtonText?this.options.pagination?this.options.formatPaginationSwitchUp():this.options.formatPaginationSwitchDown():"";this.$toolbar.find('button[name="paginationSwitch"]').html("".concat(dv.sprintf(this.constants.html.icon,this.options.iconsPrefix,t)," ").concat(e)),this.updatePagination(),this.trigger("toggle-pagination",this.options.pagination)}},{key:"toggleFullscreen",value:function(){this.$el.closest(".bootstrap-table").toggleClass("fullscreen"),this.resetView()}},{key:"toggleView",value:function(){this.options.cardView=!this.options.cardView,this.initHeader();var t=this.options.showButtonIcons?this.options.cardView?this.options.icons.toggleOn:this.options.icons.toggleOff:"",e=this.options.showButtonText?this.options.cardView?this.options.formatToggleOff():this.options.formatToggleOn():"";this.$toolbar.find('button[name="toggle"]').html("".concat(dv.sprintf(this.constants.html.icon,this.options.iconsPrefix,t)," ").concat(e)).attr("aria-label",e).attr("title",e),this.initBody(),this.trigger("toggle",this.options.cardView)}},{key:"resetSearch",value:function(t){var e=dv.getSearchInput(this),i=t||"";e.val(i),this.searchText=i,this.onSearch({currentTarget:e},!1)}},{key:"filterBy",value:function(t,e){this.filterOptions=dv.isEmptyObject(e)?this.options.filterOptions:dv.extend(this.options.filterOptions,e),this.filterColumns=dv.isEmptyObject(t)?{}:t,this.options.pageNumber=1,this.initSearch(),this.updatePagination()}},{key:"scrollTo",value:function(i){var n={unit:"px",value:0};"object"===e(i)?n=Object.assign(n,i):"string"==typeof i&&"bottom"===i?n.value=this.$tableBody[0].scrollHeight:"string"!=typeof i&&"number"!=typeof i||(n.value=i);var o=n.value;"rows"===n.unit&&(o=0,this.$body.find("> tr:lt(".concat(n.value,")")).each((function(e,i){o+=t(i).outerHeight(!0)}))),this.$tableBody.scrollTop(o)}},{key:"getScrollPosition",value:function(){return this.$tableBody.scrollTop()}},{key:"selectPage",value:function(t){t>0&&t<=this.options.totalPages&&(this.options.pageNumber=t,this.updatePagination())}},{key:"prevPage",value:function(){this.options.pageNumber>1&&(this.options.pageNumber--,this.updatePagination())}},{key:"nextPage",value:function(){this.options.pageNumber<this.options.totalPages&&(this.options.pageNumber++,this.updatePagination())}},{key:"toggleDetailView",value:function(t,e){this.$body.find(dv.sprintf('> tr[data-index="%s"]',t)).next().is("tr.detail-view")?this.collapseRow(t):this.expandRow(t,e),this.resetView()}},{key:"expandRow",value:function(t,e){var i=this.data[t],n=this.$body.find(dv.sprintf('> tr[data-index="%s"][data-has-detail-view]',t));if(this.options.detailViewIcon&&n.find("a.detail-icon").html(dv.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailClose)),!n.next().is("tr.detail-view")){n.after(dv.sprintf('<tr class="detail-view"><td colspan="%s"></td></tr>',n.children("td").length));var o=n.next().find("td"),r=e||this.options.detailFormatter,a=dv.calculateObjectValue(this.options,r,[t,i,o],"");1===o.length&&o.append(a),this.trigger("expand-row",t,i,o)}}},{key:"expandRowByUniqueId",value:function(t){var e=this.getRowByUniqueId(t);e&&this.expandRow(this.data.indexOf(e))}},{key:"collapseRow",value:function(t){var e=this.data[t],i=this.$body.find(dv.sprintf('> tr[data-index="%s"][data-has-detail-view]',t));i.next().is("tr.detail-view")&&(this.options.detailViewIcon&&i.find("a.detail-icon").html(dv.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailOpen)),this.trigger("collapse-row",t,e,i.next()),i.next().remove())}},{key:"collapseRowByUniqueId",value:function(t){var e=this.getRowByUniqueId(t);e&&this.collapseRow(this.data.indexOf(e))}},{key:"expandAllRows",value:function(){for(var e=this.$body.find("> tr[data-index][data-has-detail-view]"),i=0;i<e.length;i++)this.expandRow(t(e[i]).data("index"))}},{key:"collapseAllRows",value:function(){for(var e=this.$body.find("> tr[data-index][data-has-detail-view]"),i=0;i<e.length;i++)this.collapseRow(t(e[i]).data("index"))}},{key:"updateColumnTitle",value:function(e){e.hasOwnProperty("field")&&e.hasOwnProperty("title")&&(this.columns[this.fieldsColumnsIndex[e.field]].title=this.options.escape&&this.options.escapeTitle?dv.escapeHTML(e.title):e.title,this.columns[this.fieldsColumnsIndex[e.field]].visible&&(this.$header.find("th[data-field]").each((function(i,n){if(t(n).data("field")===e.field)return t(t(n).find(".th-inner")[0]).text(e.title),!1})),this.resetView()))}},{key:"updateFormatText",value:function(t,e){/^format/.test(t)&&this.options[t]&&("string"==typeof e?this.options[t]=function(){return e}:"function"==typeof e&&(this.options[t]=e),this.initToolbar(),this.initPagination(),this.initBody())}}]),n}();return wv.VERSION=mv.VERSION,wv.DEFAULTS=mv.DEFAULTS,wv.LOCALES=mv.LOCALES,wv.COLUMN_DEFAULTS=mv.COLUMN_DEFAULTS,wv.METHODS=mv.METHODS,wv.EVENTS=mv.EVENTS,t.BootstrapTable=wv,t.fn.bootstrapTable=function(i){for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];var a;return this.each((function(n,r){var s=t(r).data("bootstrap.table");if("string"==typeof i){var l;if(!mv.METHODS.includes(i))throw new Error("Unknown method: ".concat(i));if(!s)return;return a=(l=s)[i].apply(l,o),void("destroy"===i&&t(r).removeData("bootstrap.table"))}if(s)console.warn("You cannot initialize the table more than once!");else{var c=dv.extend(!0,{},wv.DEFAULTS,t(r).data(),"object"===e(i)&&i);s=new t.BootstrapTable(r,c),t(r).data("bootstrap.table",s),s.init()}})),void 0===a?this:a},t.fn.bootstrapTable.Constructor=wv,t.fn.bootstrapTable.theme=mv.THEME,t.fn.bootstrapTable.VERSION=mv.VERSION,t.fn.bootstrapTable.defaults=wv.DEFAULTS,t.fn.bootstrapTable.columnDefaults=wv.COLUMN_DEFAULTS,t.fn.bootstrapTable.events=wv.EVENTS,t.fn.bootstrapTable.locales=wv.LOCALES,t.fn.bootstrapTable.methods=wv.METHODS,t.fn.bootstrapTable.utils=dv,t((function(){t('[data-toggle="table"]').bootstrapTable()})),wv}));
