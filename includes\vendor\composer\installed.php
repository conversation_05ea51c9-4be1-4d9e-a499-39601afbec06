<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '*******',
        'reference' => null,
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '*******',
            'reference' => null,
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cccyun/alipay-sdk' => array(
            'pretty_version' => '1.7',
            'version' => '*******',
            'reference' => '930f85d3f7ff31f53d64e8b39093b0bc24d51ed8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../cccyun/alipay-sdk',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cccyun/qqpay-sdk' => array(
            'pretty_version' => '1.2',
            'version' => '*******',
            'reference' => '873e1d9f06f3cecdbad165fa921096437cc8bcbe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../cccyun/qqpay-sdk',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cccyun/wechatpay-sdk' => array(
            'pretty_version' => '1.7',
            'version' => '*******',
            'reference' => 'c8912fd1af1f57a566d662433de2b23a72f8b2e7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../cccyun/wechatpay-sdk',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fgrosse/phpasn1' => array(
            'pretty_version' => 'v2.5.0',
            'version' => '*******',
            'reference' => '42060ed45344789fb9f21f9f1864fc47b9e3507b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fgrosse/phpasn1',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'lpilp/guomi' => array(
            'pretty_version' => 'v1.0.9',
            'version' => '1.0.9.0',
            'reference' => '9d342416acec45db0d38dd3a8fbc1904463e6b31',
            'type' => 'library',
            'install_path' => __DIR__ . '/../lpilp/guomi',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mdanter/ecc' => array(
            'pretty_version' => 'v1.0.0',
            'version' => '*******',
            'reference' => '34e2eec096bf3dcda814e8f66dd91ae87a2db7cd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mdanter/ecc',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
