<?php
/**
 * SDO支付插件配置文件
 */

if (!defined('IN_CRONLITE')) exit();

// SDO支付配置
return [
    // 支持的游戏列表
    'games' => [
        'GWPAY-791000810' => [
            'name' => '传奇新百区-盟重神兵',
            'rate' => 1.16, // 1传奇币 = 1.16元
            'currency' => '传奇币',
            'servers' => [
                '1' => ['盟重', '比奇', '苍月', '毒蛇', '赤月', '银杏村', '白日门'],
                '2' => ['盟重', '比奇', '苍月', '毒蛇', '赤月', '银杏村', '白日门'],
                '3' => ['盟重', '比奇', '苍月', '毒蛇', '赤月', '银杏村', '白日门'],
            ]
        ],
        // 可以添加更多游戏
        'GWPAY-example' => [
            'name' => '示例游戏',
            'rate' => 1.0,
            'currency' => '游戏币',
            'servers' => [
                '1' => ['服务器1', '服务器2'],
            ]
        ],
    ],

    // 默认配置
    'default' => [
        'game_id' => 'GWPAY-791000810',
        'area_id' => '1',
        'server_name' => '盟重',
        'min_amount' => 1.16,
    ],

    // API配置
    'api' => [
        'base_url' => 'https://pay.sdo.com',
        'login_url' => 'https://w.cas.sdo.com/authen',
        'timeout' => 30,
        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    ],

    // 支付方式映射
    'payment_methods' => [
        'alipay' => '支付宝',
        'wxpay' => '微信支付',
    ],

    // 错误信息
    'errors' => [
        'cookie_empty' => 'Cookie配置不能为空',
        'game_not_found' => '游戏不存在',
        'server_not_found' => '服务器不存在',
        'amount_too_low' => '充值金额过低',
        'order_create_failed' => '创建订单失败',
        'payment_failed' => '支付创建失败',
        'network_error' => '网络请求失败',
    ],
];
?>
