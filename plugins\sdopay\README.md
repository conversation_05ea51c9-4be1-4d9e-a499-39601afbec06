# SDO盛趣游戏支付插件

## 插件简介

SDO支付插件是为盛趣游戏（SDO）平台开发的支付插件，支持支付宝和微信支付两种支付方式。用户只需配置登录Cookie和充值数量即可完成游戏充值。

## 功能特点

- ✅ 支持支付宝和微信支付
- ✅ 真实SDO API集成
- ✅ 自动计算游戏币数量
- ✅ 自定义充值金额界面
- ✅ 支持多个游戏和服务器
- ✅ 智能订单处理
- ✅ 完整的错误处理和日志记录

## 安装方法

1. 将整个 `sdopay` 文件夹复制到 `plugins/` 目录下
2. 在管理后台的支付插件管理中启用 "SDO盛趣游戏支付"
3. 配置插件参数

## 配置说明

### 必需配置

1. **登录Cookie**：从浏览器复制的完整Cookie字符串
2. **游戏ID**：默认为 `GWPAY-791000810`（传奇新百区-盟重神兵）
3. **游戏区服**：格式为 `区号-服务器名`，如：`1-盟重`

### Cookie获取方法

1. 打开浏览器，访问 [https://login.u.sdo.com](https://login.u.sdo.com)
2. 登录您的SDO账户
3. 按F12打开开发者工具
4. 切换到"Network"（网络）标签
5. 刷新页面或进行任何操作
6. 找到任意一个请求，查看Request Headers中的Cookie值
7. 复制完整的Cookie字符串到插件配置中

## 支持的游戏

目前支持的游戏：

- **传奇新百区-盟重神兵** (ID: GWPAY-791000810)
  - 汇率：1传奇币 = 1.16元
  - 支持区服：1区、2区、3区
  - 每区服务器：盟重、比奇、苍月、毒蛇、赤月、银杏村、白日门

## 使用流程

1. 用户访问支付页面
2. 显示自定义充值界面，用户选择或输入充值金额
3. 系统自动计算对应的游戏币数量
4. 创建SDO订单
5. 跳转到支付页面（支付宝或微信）
6. 用户完成支付
7. 系统自动处理支付结果

## 文件结构

```
plugins/sdopay/
├── sdopay_plugin.php    # 主插件文件
├── submit.php           # 自定义充值页面（备份）
├── config.php           # 配置文件
└── README.md            # 说明文档

根目录文件：
├── sdopay_submit.php    # 自定义充值页面（主文件）
└── sdopay_test.php      # 测试页面
```

## API接口

插件使用以下SDO API接口：

- 登录验证：`https://w.cas.sdo.com/authen/`
- 游戏充值：`https://pay.sdo.com/item/{gameId}`
- 创建订单：`https://pay.sdo.com/order`
- 支付处理：`https://pay.sdo.com/cashier/`

## 测试方法

1. 访问 `sdopay_test.php` 进行功能测试
2. 检查插件配置是否正确
3. 测试各个支付方法
4. 验证订单创建流程
5. 访问 `sdopay_submit.php?trade_no=TEST123` 测试充值页面

## 注意事项

1. **Cookie安全**：请妥善保管Cookie信息，定期更新
2. **汇率变化**：游戏币汇率可能会变化，请及时更新配置
3. **网络环境**：确保服务器能够访问SDO的API接口
4. **错误处理**：插件包含完整的错误处理机制，请查看日志排查问题

## 常见问题

### Q: Cookie失效怎么办？
A: 重新登录SDO账户，获取新的Cookie字符串并更新配置。

### Q: 支付失败怎么处理？
A: 检查Cookie是否有效，网络是否正常，游戏服务器是否可用。

### Q: 如何添加新游戏？
A: 在 `config.php` 中的 `games` 数组中添加新游戏的配置信息。

### Q: 充值金额计算错误？
A: 检查 `config.php` 中对应游戏的汇率设置是否正确。

## 技术支持

如有问题，请检查：

1. 插件配置是否正确
2. Cookie是否有效
3. 网络连接是否正常
4. 服务器日志中的错误信息

## 更新日志

### v1.0.0
- 初始版本发布
- 真实SDO API集成
- 支持传奇新百区充值
- 支持支付宝和微信支付
- 智能订单处理机制
- 完整的用户界面和错误处理
- 自动生成支付二维码

## 许可证

本插件遵循项目主体的许可证协议。
