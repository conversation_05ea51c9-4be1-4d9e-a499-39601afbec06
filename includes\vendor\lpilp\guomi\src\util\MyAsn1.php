<?php

namespace Rtgm\util;

use FG\ASN1\ASNObject;
use FG\ASN1\Identifier;


class MyAsn1
{
    const CLASS_UNIVERSAL        = 0;
    const CLASS_APPLICATION      = 1;
    const CLASS_CONTEXT_SPECIFIC = 2;
    const CLASS_PRIVATE          = 3;
    const TYPE_BOOLEAN           = 1;
    const TYPE_INTEGER           = 2;
    const TYPE_BIT_STRING        = 3;
    const TYPE_OCTET_STRING      = 4;
    const TYPE_NULL              = 5;
    const TYPE_OBJECT_IDENTIFIER = 6;
    const TYPE_OBJECT_DESCRIPTOR = 7;
    const TYPE_INSTANCE_OF       = 8; // EXTERNAL
    const TYPE_REAL              = 9;
    const TYPE_ENUMERATED        = 10;
    const TYPE_EMBEDDED          = 11;
    const TYPE_UTF8_STRING       = 12;
    const TYPE_RELATIVE_OID      = 13;
    const TYPE_SEQUENCE          = 16; // SEQUENCE OF
    const TYPE_SET               = 17; // SET OF
    const TYPE_NUMERIC_STRING    = 18;
    const TYPE_PRINTABLE_STRING  = 19;
    const TYPE_TELETEX_STRING    = 20; // T61String
    const TYPE_VIDEOTEX_STRING   = 21;
    const TYPE_IA5_STRING        = 22;
    const TYPE_UTC_TIME          = 23;
    const TYPE_GENERALIZED_TIME  = 24;
    const TYPE_GRAPHIC_STRING    = 25;
    const TYPE_VISIBLE_STRING    = 26; // ISO646String
    const TYPE_GENERAL_STRING    = 27;
    const TYPE_UNIVERSAL_STRING  = 28;
    const TYPE_CHARACTER_STRING  = 29;
    const TYPE_BMP_STRING        = 30;
    const TYPE_CHOICE            = -1;
    const TYPE_ANY               = -2;
    const TYPE_ANY_RAW           = -3;
    const TYPE_ANY_SKIP          = -4;
    const TYPE_ANY_DER           = -5;

    public static function decode_file($pemfile)
    {
        $data = self::pem2der(file_get_contents($pemfile));
        // var_dump($data);
        return self::decode($data);
    }

    public static function decode($data, $format = 'bin')
    {
        if ($format == 'base64') {
            $data = base64_decode($data);
        } else if ($format == 'hex') {
            $data = hex2bin($data);
        }
        $asnObject = ASNObject::fromBinary($data);
        return self::printObject($asnObject);
    }
    public static function printObject(ASNObject $object, $depth = 0)
    {
        $content = $object->getContent();
        if (is_array($content)) {
            $result = array();
            foreach ($object as $child) {
                $rs = self::printObject($child, $depth + 1);
                $result[] = $rs;
            }
            return $result;
        } else {
            $type = $object->getType();
            
            // $strval = $object->__toString(); 
            // 如果是 oid的话，tostring时是取的oidText, PHPasn1没有sm2等相关的就会去调用 http://oid-info.com/get/{$oidString}的接口，然后超时0.5秒
            // 这里相当于直接取get_contents(), 也是可以的
            $strval = $content; 
            if ($type == 6) { //oid
                $rt =  self::OIDtoText($strval);
            } else if ($type == 2) {
                $rt =   self::format_bigint($strval);
            }
            // else if($type==4){
            //     if(substr($strval,0,2)=='30') { //可以再分解
            //         return self::decode($strval, 'hex');
            //     } else {
            //         $rt = $strval;
            //     }
            // }
            else {
                $rt = $strval;
            }
            return $rt;
            // $name = Identifier::getShortName($type);
            // $name = str_replace(" ", "-", $name);
            // return "{$name}($type)_$rt";
        }
    }

    public static function printObject2(ASNObject $object, $depth = 0)
    {
        $treeSymbol = '';
        $depthString = str_repeat('─', $depth);
        if ($depth > 0) {
            $treeSymbol = '├';
        }
        // $type = $object->getType();
        $name = Identifier::getShortName($object->getType());
        echo "{$treeSymbol}{$depthString}{$name}: ";
        $strval = $object->__toString();
        $result[] = $strval;
        echo $object->__toString() . PHP_EOL;

        $content = $object->getContent();
        // print_R($content);
        if (is_array($content)) {
            foreach ($object as $child) {
                self::printObject2($child, $depth + 1);
            }
        }
    }

    protected static function pem2der($pem_data)
    {
        $begin = "-----";
        $end   = "-----END";
        $pem_data = substr($pem_data, strpos($pem_data, $begin, 6) + strlen($begin));
        $pem_data = substr($pem_data, 0, strpos($pem_data, $end));
        $der = base64_decode($pem_data);
        return $der;
    }
    /**
     * 大数都转成16进制
     *
     * @param bigint|string $data
     * @return string
     */
    protected static function format_bigint($data)
    {
        $hex = gmp_strval(gmp_init($data, 10), 16);
        return self::padding_one_zero($hex);
    }
    public static function padding_one_zero($hex)
    {
        if (strlen($hex) % 2 == 1) {
            $hex =  '0' . $hex;
        }
        return $hex;
    }

    public static function padding_zero($hex, $len = 64)
    {
        $left = $len - strlen($hex);
        if ($left > 0) {
            $hex = str_repeat('0', $left) . $hex;
        }
        return $hex;
    }
    /**
     * from  https://github.com/vakata/asn1
     *
     * @var array
     */
    public static $oids = array(
        'sm2' => '1.2.156.10197.1.301',
        'sm3WithSM2Encryption' => '1.2.156.10197.1.501',
        'sha1' =>                 '********.2.26',
        'sha256' =>               '2.16.840.*********.2.1',
        'sha384' =>               '2.16.840.*********.2.2',
        'sha512' =>               '2.16.840.*********.2.3',
        'sha224' =>               '2.16.840.*********.2.4',
        'md5' =>                  '1.2.840.113549.2.5',
        'md2' =>                  '********.2.2.1',
        'ripemd160' =>            '********.2.1',
        'MD4withRSA' =>           '1.2.840.113549.1.1.3',
        'SHA1withECDSA' =>        '1.2.840.10045.4.1',
        'SHA224withECDSA' =>      '1.2.840.10045.4.3.1',
        'SHA256withECDSA' =>      '1.2.840.10045.4.3.2',
        'SHA384withECDSA' =>      '1.2.840.10045.4.3.3',
        'SHA512withECDSA' =>      '1.2.840.10045.4.3.4',
        'dsa' =>                  '1.2.840.10040.4.1',
        'SHA1withDSA' =>          '1.2.840.10040.4.3',
        'SHA224withDSA' =>        '2.16.840.*********.3.1',
        'SHA256withDSA' =>        '2.16.840.*********.3.2',
        'rsaEncryption' =>        '1.2.840.113549.1.1.1',
        'countryName' =>          '*******',
        'organization' =>         '********',
        'organizationalUnit' =>   '********',
        'stateOrProvinceName' =>  '*******',
        'locality' =>             '*******',
        'commonName' =>           '*******',
        'subjectKeyIdentifier' => '*********',
        'keyUsage' =>             '*********',
        'subjectAltName' =>       '*********',
        'basicConstraints' =>     '*********',
        'nameConstraints' =>      '*********',
        'cRLDistributionPoints' => '*********',
        'certificatePolicies' =>  '*********',
        'authorityKeyIdentifier' => '*********',
        'policyConstraints' =>    '*********',
        'extKeyUsage' =>          '*********',
        'authorityInfoAccess' =>  '*******.*******.1',
        'anyExtendedKeyUsage' =>  '*********.0',
        'serverAuth' =>           '*******.*******.1',
        'clientAuth' =>           '*******.*******.2',
        'codeSigning' =>          '*******.*******.3',
        'emailProtection' =>      '*******.*******.4',
        'timeStamping' =>         '*******.*******.8',
        'ocspSigning' =>          '*******.*******.9',
        'ecPublicKey' =>          '1.2.840.10045.2.1',
        'secp256r1' =>            '1.2.840.10045.3.1.7',
        'secp256k1' =>            '1.3.132.0.10',
        'secp384r1' =>            '1.3.132.0.34',
        'pkcs5PBES2' =>           '1.2.840.113549.1.5.13',
        'pkcs5PBKDF2' =>          '1.2.840.113549.1.5.12',
        'des-EDE3-CBC' =>         '1.2.840.113549.3.7',
        'data' =>                 '1.2.840.113549.1.7.1', // CMS data
        'signed-data' =>          '1.2.840.113549.1.7.2', // CMS signed-data
        'enveloped-data' =>       '1.2.840.113549.1.7.3', // CMS enveloped-data
        'digested-data' =>        '1.2.840.113549.1.7.5', // CMS digested-data
        'encrypted-data' =>       '1.2.840.113549.1.7.6', // CMS encrypted-data
        'authenticated-data' =>   '1.2.840.113549.1.9.16.1.2', // CMS authenticated-data
        'tstinfo' =>              '1.2.840.113549.1.9.16.1.4', // RFC3161 TSTInfo,
        'pkix' => '*******.5.5.7',
        'pe' => '*******.*******',
        'qt' => '*******.5.5.7.2',
        'kp' => '*******.*******',
        'ad' => '*******.********',
        'cps' => '*******.5.5.7.2.1',
        'unotice' => '*******.5.5.7.2.2',
        'ocsp' => '*******.********.1',
        'caIssuers' => '*******.********.2',
        'timeStamping' => '*******.********.3',
        'caRepository' => '*******.********.5',
        'at' => '2.5.4',
        'name' => '********',
        'surname' => '*******',
        'givenName' => '*******2',
        'initials' => '*******3',
        'generationQualifier' => '*******4',
        'commonName' => '*******',
        'localityName' => '*******',
        'stateOrProvinceName' => '*******',
        'organizationName' => '********',
        'organizationalUnitName' => '********',
        'title' => '********',
        'description' => '********',
        'dnQualifier' => '*******6',
        'countryName' => '*******',
        'serialNumber' => '*******',
        'pseudonym' => '*******5',
        'postalCode' => '********',
        'streetAddress' => '*******',
        'uniqueIdentifier' => '*******5',
        'role' => '*******2',
        'postalAddress' => '********',
        'domainComponent' => '0.9.2342.1920030**********',
        'pkcs-9' => '1.2.840.113549.1.9',
        'emailAddress' => '1.2.840.113549.1.9.1',
        'ce' => '2.5.29',
        'authorityKeyIdentifier' => '*********',
        'subjectKeyIdentifier' => '*********',
        'keyUsage' => '*********',
        'privateKeyUsagePeriod' => '*********',
        'certificatePolicies' => '*********',
        'anyPolicy' => '*********.0',
        'policyMappings' => '*********',
        'subjectAltName' => '*********',
        'issuerAltName' => '*********',
        'subjectDirectoryAttributes' => '********',
        'basicConstraints' => '*********',
        'nameConstraints' => '*********',
        'policyConstraints' => '*********',
        'cRLDistributionPoints' => '*********',
        'extKeyUsage' => '*********',
        'anyExtendedKeyUsage' => '*********.0',
        'kp-serverAuth' => '*******.*******.1',
        'kp-clientAuth' => '*******.*******.2',
        'kp-codeSigning' => '*******.*******.3',
        'kp-emailProtection' => '*******.*******.4',
        'kp-timeStamping' => '*******.*******.8',
        'kp-OCSPSigning' => '*******.*******.9',
        'inhibitAnyPolicy' => '*********',
        'freshestCRL' => '*********',
        'pe-authorityInfoAccess' => '*******.*******.1',
        'pe-subjectInfoAccess' => '*******.*******.11',
        'cRLNumber' => '*********',
        'issuingDistributionPoint' => '*********',
        'deltaCRLIndicator' => '*********',
        'cRLReasons' => '*********',
        'certificateIssuer' => '*********',
        'holdInstructionCode' => '*********',
        'holdInstruction' => '1.2.840.10040.2',
        'holdinstruction-none' => '1.2.840.10040.2.1',
        'holdinstruction-callissuer' => '1.2.840.10040.2.2',
        'holdinstruction-reject' => '1.2.840.10040.2.3',
        'invalidityDate' => '2.5.29.24',
        'md2' => '1.2.840.113549.2.2',
        'md5' => '1.2.840.113549.2.5',
        'sha1' => '********.2.26',
        'dsa' => '1.2.840.10040.4.1',
        'dsa-with-sha1' => '1.2.840.10040.4.3',
        'pkcs-1' => '1.2.840.113549.1.1',
        'rsaEncryption' => '1.2.840.113549.1.1.1',
        'md2WithRSAEncryption' => '1.2.840.113549.1.1.2',
        'md5WithRSAEncryption' => '1.2.840.113549.1.1.4',
        'sha1WithRSAEncryption' => ['1.2.840.113549.1.1.5', '********.2.29'],
        'dhpublicnumber' => '1.2.840.10046.2.1',
        'keyExchangeAlgorithm' => '2.16.840.1.101.2.1.1.22',
        'ansi-X9-62' => '1.2.840.10045',
        'ecSigType' => '1.2.840.10045.4',
        'ecdsa-with-SHA1' => '1.2.840.10045.4.1',
        'fieldType' => '1.2.840.10045.1',
        'prime-field' => '1.2.840.10045.1.1',
        'characteristic-two-field' => '1.2.840.10045.1.2',
        'characteristic-two-basis' => '1.2.840.10045.1.2.3',
        'gnBasis' => '1.2.840.10045.*******',
        'tpBasis' => '1.2.840.10045.*******',
        'ppBasis' => '1.2.840.10045.*******',
        'publicKeyType' => '1.2.840.10045.2',
        'ecPublicKey' => '1.2.840.10045.2.1',
        'ellipticCurve' => '1.2.840.10045.3',
        'c-TwoCurve' => '1.2.840.10045.3.0',
        'c2pnb163v1' => '1.2.840.10045.3.0.1',
        'c2pnb163v2' => '1.2.840.10045.3.0.2',
        'c2pnb163v3' => '1.2.840.10045.3.0.3',
        'c2pnb176w1' => '1.2.840.10045.3.0.4',
        'c2pnb191v1' => '1.2.840.10045.3.0.5',
        'c2pnb191v2' => '1.2.840.10045.3.0.6',
        'c2pnb191v3' => '1.2.840.10045.3.0.7',
        'c2pnb191v4' => '1.2.840.10045.3.0.8',
        'c2pnb191v5' => '1.2.840.10045.3.0.9',
        'c2pnb208w1' => '1.2.840.10045.3.0.10',
        'c2pnb239v1' => '1.2.840.10045.3.0.11',
        'c2pnb239v2' => '1.2.840.10045.3.0.12',
        'c2pnb239v3' => '1.2.840.10045.3.0.13',
        'c2pnb239v4' => '1.2.840.10045.3.0.14',
        'c2pnb239v5' => '1.2.840.10045.3.0.15',
        'c2pnb272w1' => '1.2.840.10045.3.0.16',
        'c2pnb304w1' => '1.2.840.10045.3.0.17',
        'c2pnb359v1' => '1.2.840.10045.3.0.18',
        'c2pnb368w1' => '1.2.840.10045.3.0.19',
        'c2pnb431r1' => '1.2.840.10045.3.0.20',
        'primeCurve' => '1.2.840.10045.3.1',
        'prime192v1' => '1.2.840.10045.3.1.1',
        'prime192v2' => '1.2.840.10045.3.1.2',
        'prime192v3' => '1.2.840.10045.3.1.3',
        'prime239v1' => '1.2.840.10045.3.1.4',
        'prime239v2' => '1.2.840.10045.3.1.5',
        'prime239v3' => '1.2.840.10045.3.1.6',
        'prime256v1' => '1.2.840.10045.3.1.7',
        'RSAES-OAEP' => '1.2.840.113549.1.1.7',
        'pSpecified' => '1.2.840.113549.1.1.9',
        'RSASSA-PSS' => '1.2.840.113549.1.1.10',
        'mgf1' => '1.2.840.113549.1.1.8',
        'sha224WithRSAEncryption' => '1.2.840.113549.1.1.14',
        'sha256WithRSAEncryption' => '1.2.840.113549.1.1.11',
        'sha384WithRSAEncryption' => '1.2.840.113549.1.1.12',
        'sha512WithRSAEncryption' => '1.2.840.113549.1.1.13',
        'sha224' => '2.16.840.*********.2.4',
        'sha256' => '2.16.840.*********.2.1',
        'sha384' => '2.16.840.*********.2.2',
        'sha512' => '2.16.840.*********.2.3',
        'GostR3411-94-with-GostR3410-94' => '1.2.643.2.2.4',
        'GostR3411-94-with-GostR3410-2001' => '1.2.643.2.2.3',
        'GostR3410-2001' => '1.2.643.2.2.20',
        'GostR3410-94' => '1.2.643.2.2.19',
        'netscape' => '2.16.840.1.113730',
        'netscape-cert-extension' => '2.16.840.1.113730.1',
        'netscape-cert-type' => '2.16.840.1.113730.1.1',
        'netscape-comment' => '2.16.840.1.113730.1.13',
        'netscape-ca-policy-url' => '2.16.840.1.113730.1.8',
        'logotype' => '*******.*******.12',
        'entrustVersInfo' => '1.2.840.113533.7.65.0',
        'verisignPrivate' => '2.16.840.1.113733.1.6.9',
        'unstructuredName' => '1.2.840.113549.1.9.2',
        'challengePassword' => '1.2.840.113549.1.9.7',
        'extensionRequest' => '1.2.840.113549.1.9.14',
        'userid' => '0.9.2342.19200300.100.1.1',
        's/mime' => '1.2.840.113549.1.9.15',
        'unstructuredAddress' => '1.2.840.113549.1.9.8',
        'rc2-cbc' => '1.2.840.113549.3.2',
        'rc4' => '1.2.840.113549.3.4',
        'desCBC' => '********.2.7',
        'qcStatements' => '*******.*******.3',
        'pkixQCSyntax-v1' => '*******.*******1.1',
        'pkixQCSyntax-v2' => '*******.*******1.2',
        'ipsecEndSystem' => '*******.*******.5',
        'ipsecTunnel' => '*******.*******.6',
        'ipsecUser' => '*******.*******.7',
        'OCSP' => '*******.********.1',
        'countryOfCitizenship' => '*******.*******.4',
        'IPSECProtection' => '*******.*******.2',
        'telephoneNumber' => '********',
        'organizationIdentifier' => '********',

    );

    public static $oidTexts = array(
        '1.2.156.10197.1.301' => 'sm2',
        '1.2.156.10197.1.501' => 'sm3WithSM2Encryption',
        '********.2.26' => 'sha1',
        '2.16.840.*********.2.1' => 'sha256',
        '2.16.840.*********.2.2' => 'sha384',
        '2.16.840.*********.2.3' => 'sha512',
        '2.16.840.*********.2.4' => 'sha224',
        '1.2.840.113549.2.5' => 'md5',
        '1.2.840.113549.2.2' => 'md2',
        '********.2.1' => 'ripemd160',
        '1.2.840.113549.1.1.3' => 'MD4withRSA',
        '1.2.840.10045.4.1' => 'SHA1withECDSA',
        '1.2.840.10045.4.3.1' => 'SHA224withECDSA',
        '1.2.840.10045.4.3.2' => 'SHA256withECDSA',
        '1.2.840.10045.4.3.3' => 'SHA384withECDSA',
        '1.2.840.10045.4.3.4' => 'SHA512withECDSA',
        '1.2.840.10040.4.1' => 'dsa',
        '1.2.840.10040.4.3' => 'SHA1withDSA',
        '2.16.840.*********.3.1' => 'SHA224withDSA',
        '2.16.840.*********.3.2' => 'SHA256withDSA',
        '1.2.840.113549.1.1.1' => 'rsaEncryption',
        '*******' => 'countryName',
        '********' => 'organization',
        '********' => 'organizationalUnit',
        '*******' => 'stateOrProvinceName',
        '*******' => 'locality',
        '*******' => 'commonName',
        '*********' => 'subjectKeyIdentifier',
        '*********' => 'keyUsage',
        '*********' => 'subjectAltName',
        '*********' => 'basicConstraints',
        '*********' => 'nameConstraints',
        '*********' => 'cRLDistributionPoints',
        '*********' => 'certificatePolicies',
        '*********' => 'authorityKeyIdentifier',
        '*********' => 'policyConstraints',
        '*********' => 'extKeyUsage',
        '*******.*******.1' => 'authorityInfoAccess',
        '*********.0' => 'anyExtendedKeyUsage',
        '*******.*******.1' => 'serverAuth',
        '*******.*******.2' => 'clientAuth',
        '*******.*******.3' => 'codeSigning',
        '*******.*******.4' => 'emailProtection',
        '*******.********.3' => 'timeStamping',
        '*******.*******.9' => 'ocspSigning',
        '1.2.840.10045.2.1' => 'ecPublicKey',
        '1.2.840.10045.3.1.7' => 'secp256r1',
        '1.3.132.0.10' => 'secp256k1',
        '1.3.132.0.34' => 'secp384r1',
        '1.2.840.113549.1.5.13' => 'pkcs5PBES2',
        '1.2.840.113549.1.5.12' => 'pkcs5PBKDF2',
        '1.2.840.113549.3.7' => 'des-EDE3-CBC',
        '1.2.840.113549.1.7.1' => 'data',
        '1.2.840.113549.1.7.2' => 'signed-data',
        '1.2.840.113549.1.7.3' => 'enveloped-data',
        '1.2.840.113549.1.7.5' => 'digested-data',
        '1.2.840.113549.1.7.6' => 'encrypted-data',
        '1.2.840.113549.1.9.16.1.2' => 'authenticated-data',
        '1.2.840.113549.1.9.16.1.4' => 'tstinfo',
        '*******.5.5.7' => 'pkix',
        '*******.*******' => 'pe',
        '*******.5.5.7.2' => 'qt',
        '*******.*******' => 'kp',
        '*******.********' => 'ad',
        '*******.5.5.7.2.1' => 'cps',
        '*******.5.5.7.2.2' => 'unotice',
        '*******.********.1' => 'ocsp',
        '*******.********.2' => 'caIssuers',
        '*******.********.5' => 'caRepository',
        '2.5.4' => 'at',
        '********' => 'name',
        '*******' => 'surname',
        '*******2' => 'givenName',
        '*******3' => 'initials',
        '*******4' => 'generationQualifier',
        '*******' => 'localityName',
        '********' => 'organizationName',
        '********' => 'organizationalUnitName',
        '********' => 'title',
        '********' => 'description',
        '*******6' => 'dnQualifier',
        '*******' => 'serialNumber',
        '*******5' => 'pseudonym',
        '********' => 'postalCode',
        '*******' => 'streetAddress',
        '*******5' => 'uniqueIdentifier',
        '*******2' => 'role',
        '********' => 'postalAddress',
        '0.9.2342.1920030**********' => 'domainComponent',
        '1.2.840.113549.1.9' => 'pkcs-9',
        '1.2.840.113549.1.9.1' => 'emailAddress',
        '2.5.29' => 'ce',
        '*********' => 'privateKeyUsagePeriod',
        '*********.0' => 'anyPolicy',
        '*********' => 'policyMappings',
        '*********' => 'issuerAltName',
        '********' => 'subjectDirectoryAttributes',
        '*******.*******.1' => 'kp-serverAuth',
        '*******.*******.2' => 'kp-clientAuth',
        '*******.*******.3' => 'kp-codeSigning',
        '*******.*******.4' => 'kp-emailProtection',
        '*******.*******.8' => 'kp-timeStamping',
        '*******.*******.9' => 'kp-OCSPSigning',
        '*********' => 'inhibitAnyPolicy',
        '*********' => 'freshestCRL',
        '*******.*******.1' => 'pe-authorityInfoAccess',
        '*******.*******.11' => 'pe-subjectInfoAccess',
        '*********' => 'cRLNumber',
        '*********' => 'issuingDistributionPoint',
        '*********' => 'deltaCRLIndicator',
        '*********' => 'cRLReasons',
        '*********' => 'certificateIssuer',
        '*********' => 'holdInstructionCode',
        '1.2.840.10040.2' => 'holdInstruction',
        '1.2.840.10040.2.1' => 'holdinstruction-none',
        '1.2.840.10040.2.2' => 'holdinstruction-callissuer',
        '1.2.840.10040.2.3' => 'holdinstruction-reject',
        '2.5.29.24' => 'invalidityDate',
        '1.2.840.10040.4.3' => 'dsa-with-sha1',
        '1.2.840.113549.1.1' => 'pkcs-1',
        '1.2.840.113549.1.1.2' => 'md2WithRSAEncryption',
        '1.2.840.113549.1.1.4' => 'md5WithRSAEncryption',
        '1.2.840.113549.1.1.5' => 'sha1WithRSAEncryption',
        '********.2.29' => 'sha1WithRSAEncryption',
        '1.2.840.10046.2.1' => 'dhpublicnumber',
        '2.16.840.1.101.2.1.1.22' => 'keyExchangeAlgorithm',
        '1.2.840.10045' => 'ansi-X9-62',
        '1.2.840.10045.4' => 'ecSigType',
        '1.2.840.10045.4.1' => 'ecdsa-with-SHA1',
        '1.2.840.10045.1' => 'fieldType',
        '1.2.840.10045.1.1' => 'prime-field',
        '1.2.840.10045.1.2' => 'characteristic-two-field',
        '1.2.840.10045.1.2.3' => 'characteristic-two-basis',
        '1.2.840.10045.*******' => 'gnBasis',
        '1.2.840.10045.*******' => 'tpBasis',
        '1.2.840.10045.*******' => 'ppBasis',
        '1.2.840.10045.2' => 'publicKeyType',
        '1.2.840.10045.3' => 'ellipticCurve',
        '1.2.840.10045.3.0' => 'c-TwoCurve',
        '1.2.840.10045.3.0.1' => 'c2pnb163v1',
        '1.2.840.10045.3.0.2' => 'c2pnb163v2',
        '1.2.840.10045.3.0.3' => 'c2pnb163v3',
        '1.2.840.10045.3.0.4' => 'c2pnb176w1',
        '1.2.840.10045.3.0.5' => 'c2pnb191v1',
        '1.2.840.10045.3.0.6' => 'c2pnb191v2',
        '1.2.840.10045.3.0.7' => 'c2pnb191v3',
        '1.2.840.10045.3.0.8' => 'c2pnb191v4',
        '1.2.840.10045.3.0.9' => 'c2pnb191v5',
        '1.2.840.10045.3.0.10' => 'c2pnb208w1',
        '1.2.840.10045.3.0.11' => 'c2pnb239v1',
        '1.2.840.10045.3.0.12' => 'c2pnb239v2',
        '1.2.840.10045.3.0.13' => 'c2pnb239v3',
        '1.2.840.10045.3.0.14' => 'c2pnb239v4',
        '1.2.840.10045.3.0.15' => 'c2pnb239v5',
        '1.2.840.10045.3.0.16' => 'c2pnb272w1',
        '1.2.840.10045.3.0.17' => 'c2pnb304w1',
        '1.2.840.10045.3.0.18' => 'c2pnb359v1',
        '1.2.840.10045.3.0.19' => 'c2pnb368w1',
        '1.2.840.10045.3.0.20' => 'c2pnb431r1',
        '1.2.840.10045.3.1' => 'primeCurve',
        '1.2.840.10045.3.1.1' => 'prime192v1',
        '1.2.840.10045.3.1.2' => 'prime192v2',
        '1.2.840.10045.3.1.3' => 'prime192v3',
        '1.2.840.10045.3.1.4' => 'prime239v1',
        '1.2.840.10045.3.1.5' => 'prime239v2',
        '1.2.840.10045.3.1.6' => 'prime239v3',
        '1.2.840.10045.3.1.7' => 'prime256v1',
        '1.2.840.113549.1.1.7' => 'RSAES-OAEP',
        '1.2.840.113549.1.1.9' => 'pSpecified',
        '1.2.840.113549.1.1.10' => 'RSASSA-PSS',
        '1.2.840.113549.1.1.8' => 'mgf1',
        '1.2.840.113549.1.1.14' => 'sha224WithRSAEncryption',
        '1.2.840.113549.1.1.11' => 'sha256WithRSAEncryption',
        '1.2.840.113549.1.1.12' => 'sha384WithRSAEncryption',
        '1.2.840.113549.1.1.13' => 'sha512WithRSAEncryption',
        '1.2.643.2.2.4' => 'GostR3411-94-with-GostR3410-94',
        '1.2.643.2.2.3' => 'GostR3411-94-with-GostR3410-2001',
        '1.2.643.2.2.20' => 'GostR3410-2001',
        '1.2.643.2.2.19' => 'GostR3410-94',
        '2.16.840.1.113730' => 'netscape',
        '2.16.840.1.113730.1' => 'netscape-cert-extension',
        '2.16.840.1.113730.1.1' => 'netscape-cert-type',
        '2.16.840.1.113730.1.13' => 'netscape-comment',
        '2.16.840.1.113730.1.8' => 'netscape-ca-policy-url',
        '*******.*******.12' => 'logotype',
        '1.2.840.113533.7.65.0' => 'entrustVersInfo',
        '2.16.840.1.113733.1.6.9' => 'verisignPrivate',
        '1.2.840.113549.1.9.2' => 'unstructuredName',
        '1.2.840.113549.1.9.7' => 'challengePassword',
        '1.2.840.113549.1.9.14' => 'extensionRequest',
        '0.9.2342.19200300.100.1.1' => 'userid',
        '1.2.840.113549.1.9.15' => 's/mime',
        '1.2.840.113549.1.9.8' => 'unstructuredAddress',
        '1.2.840.113549.3.2' => 'rc2-cbc',
        '1.2.840.113549.3.4' => 'rc4',
        '********.2.7' => 'desCBC',
        '*******.*******.3' => 'qcStatements',
        '*******.*******1.1' => 'pkixQCSyntax-v1',
        '*******.*******1.2' => 'pkixQCSyntax-v2',
        '*******.*******.5' => 'ipsecEndSystem',
        '*******.*******.6' => 'ipsecTunnel',
        '*******.*******.7' => 'ipsecUser',
        '*******.********.1' => 'OCSP',
        '*******.*******.4' => 'countryOfCitizenship',
        '*******.*******.2' => 'IPSECProtection',
        '********' => 'telephoneNumber',
        '********' => 'organizationIdentifier',

    );
    /**
     * from  https://github.com/vakata/asn1
     *
     * @param string $id
     * @return string
     */
    public static function OIDtoText($id)
    {
        // echo $id."\n";
        $text = self::$oidTexts[$id] ?? $id;

        return $text;
    }
    /**
     * from  https://github.com/vakata/asn1
     *
     * @param string $text
     * @return string
     */
    public static function TextToOID($text)
    {
        $res = static::$oids[$text] ?? null;
        if (is_array($res)) {
            $res = $res[0];
        }
        return $res ?? $text;
    }
}

function getMillisecond()
{
    list($microsecond, $time) = explode(' ', microtime()); //' '中间是一个空格
    return (float)sprintf('%.0f', (floatval($microsecond) + floatval($time)) * 1000);
}
