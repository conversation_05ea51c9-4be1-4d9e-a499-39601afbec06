<?php

class yunfuyitong_plugin
{
    static public $info = [
        'name'        => 'yunfuyitong',
        'showname'    => '云付易通',
        'author'      => '云付易通',
        'link'        => 'https://www.yunfuyitong.cn/',
        'types'       => ['alipay','wxpay','qqpay','bank'],
        'inputs' => [
            'appid' => [
                'name' => '商户号',
                'type' => 'input',
                'note' => '云付易通商户号(mchNo)',
            ],
            'appmchid' => [
                'name' => '应用ID',
                'type' => 'input',
                'note' => '云付易通应用ID(appId)',
            ],
            'appkey' => [
                'name' => '商户密钥',
                'type' => 'input',
                'note' => '云付易通商户密钥',
            ],
        ],
        'select' => null,
        'note' => '云付易通支付插件，统一使用WEB收银台支付方式',
        'bindwxmp' => false,
        'bindwxa' => false,
    ];

    static public function submit(){
        global $siteurl, $channel, $order, $ordername, $conf;

        if($order['typename']=='alipay'){
            return self::alipay();
        }elseif($order['typename']=='wxpay'){
            return self::wxpay();
        }elseif($order['typename']=='qqpay'){
            return self::qqpay();
        }elseif($order['typename']=='bank'){
            return self::bank();
        }
    }

    static public function mapi(){
        return self::submit();
    }

    //统一下单方法
    static private function unifiedOrder(){
        global $siteurl, $channel, $order, $ordername, $conf, $clientip;

        require_once(PAY_ROOT.'inc/YunfuyitongClient.php');

        $client = new YunfuyitongClient($channel['appid'], $channel['appmchid'], $channel['appkey']);

        $params = [
            'mchOrderNo' => TRADE_NO,
            'wayCode' => 'WEB_CASHIER',
            'amount' => intval($order['realmoney'] * 100),
            'currency' => 'cny',
            'clientIp' => $clientip,
            'subject' => $ordername,
            'body' => $ordername,
            'notifyUrl' => $conf['localurl'].'pay/notify/'.TRADE_NO.'/',
            'returnUrl' => $siteurl.'pay/return/'.TRADE_NO.'/',
            'expiredTime' => 7200,
        ];

        return \lib\Payment::lockPayData(TRADE_NO, function() use($client, $params) {
            $result = $client->unifiedOrder($params);
            
            if($result['code'] == 0 && isset($result['data'])){
                $data = $result['data'];
                \lib\Payment::updateOrder(TRADE_NO, $data['payOrderId']);
                return $data['payData'];
            }else{
                throw new Exception($result['msg'] ?? '创建订单失败');
            }
        });
    }

    //支付宝
    static public function alipay(){
        try{
            $payUrl = self::unifiedOrder();
        }catch(Exception $ex){
            return ['type'=>'error','msg'=>'支付宝下单失败！'.$ex->getMessage()];
        }

        return ['type'=>'jump','url'=>$payUrl];
    }

    //微信支付
    static public function wxpay(){
        try{
            $payUrl = self::unifiedOrder();
        }catch(Exception $ex){
            return ['type'=>'error','msg'=>'微信支付下单失败！'.$ex->getMessage()];
        }

        return ['type'=>'jump','url'=>$payUrl];
    }

    //QQ支付
    static public function qqpay(){
        try{
            $payUrl = self::unifiedOrder();
        }catch(Exception $ex){
            return ['type'=>'error','msg'=>'QQ支付下单失败！'.$ex->getMessage()];
        }

        return ['type'=>'jump','url'=>$payUrl];
    }

    //银行卡/云闪付
    static public function bank(){
        try{
            $payUrl = self::unifiedOrder();
        }catch(Exception $ex){
            return ['type'=>'error','msg'=>'银行卡支付下单失败！'.$ex->getMessage()];
        }

        return ['type'=>'jump','url'=>$payUrl];
    }

    //异步回调
    static public function notify(){
        global $channel, $order;

        require_once(PAY_ROOT.'inc/YunfuyitongClient.php');

        $client = new YunfuyitongClient($channel['appid'], $channel['appmchid'], $channel['appkey']);

        // 获取回调数据
        $data = [];
        if(!empty($_POST)) {
            $data = $_POST;
        } elseif(!empty($_GET)) {
            $data = $_GET;
        } else {
            $rawData = file_get_contents('php://input');
            if(!empty($rawData)){
                $data = json_decode($rawData, true);
                if(!$data) {
                    parse_str($rawData, $data);
                }
            }
        }
        
        if(empty($data) || !isset($data['mchOrderNo']) || !isset($data['state'])) {
            return ['type'=>'html','data'=>'fail'];
        }

        // 验证签名
        if(!$client->verifySign($data)){
            return ['type'=>'html','data'=>'fail'];
        }

        // 验证支付成功状态
        if($data['state'] == 2){
            $mchOrderNo = $data['mchOrderNo'];
            $payOrderId = $data['payOrderId'];
            $amount = $data['amount'];

            // 验证订单号和金额
            if($mchOrderNo == TRADE_NO && $amount == intval($order['realmoney'] * 100)){
                processNotify($order, $payOrderId);
                return ['type'=>'html','data'=>'success'];
            }
        }

        return ['type'=>'html','data'=>'fail'];
    }

    //同步回调
    static public function return(){
        global $channel, $order;

        require_once(PAY_ROOT.'inc/YunfuyitongClient.php');

        $client = new YunfuyitongClient($channel['appid'], $channel['appmchid'], $channel['appkey']);

        // 检查必要的参数
        if(!isset($_GET['state']) || !isset($_GET['mchOrderNo'])) {
            return ['type'=>'error','msg'=>'缺少必要参数'];
        }

        // 验证签名
        if(!$client->verifySign($_GET)){
            return ['type'=>'error','msg'=>'签名验证失败'];
        }
        
        // 验证支付成功状态
        if($_GET['state'] == 2){ 
            $payOrderId = $_GET['payOrderId'] ?? '';
            
            // 处理订单
            if($order['status'] != 1) {
                processReturn($order, $payOrderId);
            }
            
            return ['type'=>'page','page'=>'return'];
        } else {
            return ['type'=>'error','msg'=>'支付未完成或失败'];
        }
    }

    //退款
    static public function refund($order){
        return ['code'=>-1, 'msg'=>'该支付方式暂不支持退款'];
    }
}