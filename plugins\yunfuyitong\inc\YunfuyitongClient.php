<?php

class YunfuyitongClient
{
    private $mchNo;
    private $appId;
    private $key;
    private $apiUrl = 'https://pay.yunfuyitong.cn/api/pay/unifiedOrder';
    private $queryUrl = 'https://pay.yunfuyitong.cn/api/pay/query';

    public function __construct($mchNo, $appId, $key)
    {
        $this->mchNo = $mchNo;
        $this->appId = $appId;
        $this->key = $key;
    }

    /**
     * 获取13位时间戳
     */
    private function getMillisecond()
    {
        return intval(microtime(true) * 1000);
    }

    /**
     * 生成签名
     * 按照云付易通官方签名算法
     */
    private function generateSign($params)
    {
        // 过滤空值参数
        $params = array_filter($params, function($value) {
            return $value !== '' && $value !== null;
        });
        
        // 移除sign参数
        unset($params['sign']);
        
        // 按ASCII码从小到大排序（字典序）
        ksort($params);
        
        // 拼接字符串 - 使用URL键值对的格式
        $stringA = '';
        foreach ($params as $key => $value) {
            $stringA .= $key . '=' . $value . '&';
        }
        
        // 去除最后一个&符号并拼接key
        $stringSignTemp = rtrim($stringA, '&') . '&key=' . $this->key;
        
        // MD5加密并转大写
        return strtoupper(md5($stringSignTemp));
    }

    /**
     * 验证签名
     */
    public function verifySign($params)
    {
        if(!isset($params['sign'])){
            return false;
        }
        
        $sign = $params['sign'];
        $expectedSign = $this->generateSign($params);
        
        return $sign === $expectedSign;
    }

    /**
     * HTTP POST请求
     */
    private function httpPost($url, $data)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Accept: application/json'
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception('CURL错误: ' . $error);
        }

        if ($httpCode !== 200) {
            throw new Exception('HTTP错误: ' . $httpCode);
        }

        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('JSON解析错误: ' . json_last_error_msg());
        }

        return $result;
    }

    /**
     * 统一下单
     */
    public function unifiedOrder($params)
    {
        // 添加必需的系统参数
        $params['mchNo'] = $this->mchNo;
        $params['appId'] = $this->appId;
        $params['version'] = '1.0';
        $params['signType'] = 'MD5';
        $params['reqTime'] = $this->getMillisecond();
        
        // 生成签名
        $params['sign'] = $this->generateSign($params);
        
        return $this->httpPost($this->apiUrl, $params);
    }

    /**
     * 查询订单
     */
    public function queryOrder($mchOrderNo)
    {
        $params = [
            'mchNo' => $this->mchNo,
            'appId' => $this->appId,
            'mchOrderNo' => $mchOrderNo,
            'version' => '1.0',
            'signType' => 'MD5',
            'reqTime' => $this->getMillisecond()
        ];
        
        // 生成签名
        $params['sign'] = $this->generateSign($params);
        
        return $this->httpPost($this->queryUrl, $params);
    }
}