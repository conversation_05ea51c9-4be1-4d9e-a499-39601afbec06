/*
Template Name: Color Admin - Responsive Admin Dashboard Template build with Twitter Bootstrap 3 & 4
Version: 4.0.0
Author: <PERSON>
Website: https://www.seantheme.com/color-admin-v4.0/frontend/one-page-parallax/
*/

.btn.btn-theme {
    background: #ff5b57;
    border-color: #ff5b57;
    color: #fff;
}
.btn.btn-theme:hover,
.btn.btn-theme:focus {
    background: #cc4946;
    border-color: #cc4946;
}
.header.navbar .navbar-nav > li.active > a, 
.header.navbar .navbar-nav > li > a:hover, 
.header.navbar .navbar-nav > li > a:focus,
.header.navbar.navbar-default .navbar-nav > li.active > a, 
.header.navbar.navbar-default .navbar-nav > li > a:hover, 
.header.navbar.navbar-default .navbar-nav > li > a:focus,
.header.navbar.navbar-transparent .navbar-nav > li.active > a, 
.header.navbar.navbar-transparent .navbar-nav > li > a:hover, 
.header.navbar.navbar-transparent .navbar-nav > li > a:focus,
.header.navbar.navbar-inverse .navbar-nav > li.active > a, 
.header.navbar.navbar-inverse .navbar-nav > li > a:hover, 
.header.navbar.navbar-inverse .navbar-nav > li > a:focus,
.header.navbar.navbar-transparent.navbar-small .navbar-nav > li.active > a,
.header.navbar.navbar-transparent.navbar-small .navbar-nav > li > a:hover,
.header.navbar.navbar-transparent.navbar-small .navbar-nav > li > a:focus,
.text-theme,
.navbar-nav .dropdown-menu > li.active > a,
.navbar-nav .dropdown-menu > li > a:hover,
.navbar-nav .dropdown-menu > li > a:focus,
.pricing-table .price .price-number,
a {
    color: #ff5b57;
}
a:hover,
a:focus {
    color: #cc4946;
}
.pricing-table .highlight h3,
.pace-progress {
    background: #cc4946;
}
.pricing-table .highlight .price {
    background: #ff5b57;
}
.pace .pace-activity {
    border-top-color: #ff5b57;
    border-left-color: #ff5b57;
}
.brand-logo,
.footer .footer-brand-logo {
    border-color: #F8504B #cc4946 #993734;
}