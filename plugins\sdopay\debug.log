[2025-07-31 17:10:15] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-07-31 17:10:15] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-07-31 17:10:15] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-07-31 17:10:15] 充值页面访问成功，页面长度: 27735
[2025-07-31 17:10:15] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-07-31 17:10:15] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-07-31 17:10:15] 成功提取token - itemToken: 05859199bcd54454be6121a8b77aaa9b, captchaToken: 0713ee84e161470cad774654dbc70079
[2025-07-31 17:10:15] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-07-31 17:10:15] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"0713ee84e161470cad774654dbc70079","itemToken":"05859199bcd54454be6121a8b77aaa9b","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-07-31 17:10:15] 开始提交充值订单
[2025-07-31 17:10:15] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-07-31 17:10:15] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"0713ee84e161470cad774654dbc70079","itemToken":"05859199bcd54454be6121a8b77aaa9b","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-07-31 17:10:15] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-07-31 17:10:15] 订单提交完成，响应类型: string
[2025-07-31 17:10:15] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"153e83f0b7324b4e9d9f78422b6d3628","orderId":"P1010127026170250731171015000001"}}
[2025-07-31 17:10:15] 开始处理订单响应，支付方式: alipay
[2025-07-31 17:10:15] 开始处理订单响应，响应类型: string
[2025-07-31 17:10:15] 检测到JSON响应，开始解析
[2025-07-31 17:10:15] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"153e83f0b7324b4e9d9f78422b6d3628","orderId":"P1010127026170250731171015000001"}}
[2025-07-31 17:10:15] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=153e83f0b7324b4e9d9f78422b6d3628&orderId=P1010127026170250731171015000001
[2025-07-31 17:10:15] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=153e83f0b7324b4e9d9f78422b6d3628&orderId=P1010127026170250731171015000001
[2025-07-31 17:10:15] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=153e83f0b7324b4e9d9f78422b6d3628&orderId=P1010127026170250731171015000001
[2025-07-31 17:10:15] 提取参数成功 - orderToken: 153e83f0b7324b4e9d9f78422b6d3628, orderId: P1010127026170250731171015000001
[2025-07-31 17:10:15] 步骤1: 访问支付页面获取payToken
[2025-07-31 17:10:15] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=153e83f0b7324b4e9d9f78422b6d3628&orderId=P1010127026170250731171015000001, 方法: GET
[2025-07-31 17:10:15] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-07-31 17:10:15] 成功提取payToken: 1317861aad2641f5a658ca59df32b49a
[2025-07-31 17:10:15] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-07-31 17:10:15] 第一次请求参数: {"orderId":"P1010127026170250731171015000001","orderToken":"153e83f0b7324b4e9d9f78422b6d3628","payChannelId":"3","payToken":"1317861aad2641f5a658ca59df32b49a","routerFlg":"1"}
[2025-07-31 17:10:15] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:10:15] 请求数据: {"orderId":"P1010127026170250731171015000001","orderToken":"153e83f0b7324b4e9d9f78422b6d3628","payChannelId":"3","payToken":"1317861aad2641f5a658ca59df32b49a","routerFlg":"1"}
[2025-07-31 17:10:15] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-07-31 17:10:15] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-07-31 17:10:15] 第一次请求成功，获得最终payChannelId: 10003
[2025-07-31 17:10:15] 步骤3: 完成支付流程，payChannelId: 10003
[2025-07-31 17:10:15] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:10:15] 请求数据: {"orderId":"P1010127026170250731171015000001","orderToken":"153e83f0b7324b4e9d9f78422b6d3628","payChannelId":10003,"payToken":"1317861aad2641f5a658ca59df32b49a"}
[2025-07-31 17:10:16] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-07-31 17:10:16] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=c6c45b2fb3af527de0ef70e5470ceb53&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"c6c45b2fb3af527de0ef70e5470ceb53\"}},\"captchaType\":4}"}}
[2025-07-31 17:10:16] 需要验证码，获取最新验证码参数
[2025-07-31 17:10:16] 最新验证码信息: {"captchaParams":{"gtData":{"gt":"31cc9ac8ae5eb9ef1aeaee9110bfc50c","success":1,"gt_url":"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=c6c45b2fb3af527de0ef70e5470ceb53&offline=false&new_captcha=true&callfunction=","new_captcha":1,"challenge":"c6c45b2fb3af527de0ef70e5470ceb53"}},"captchaType":4}
[2025-07-31 17:10:16] 尝试即时处理支付，跳过验证码验证
[2025-07-31 17:10:16] 尝试验证码格式 1: {"geetest_challenge":"c6c45b2fb3af527de0ef70e5470ceb53","geetest_validate":"e52083a6d2304a911932fd56562c9083","geetest_seccode":"e52083a6d2304a911932fd56562c9083|jordan"}
[2025-07-31 17:10:16] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:10:16] 第三步请求参数: {"orderId":"P1010127026170250731171015000001","orderToken":"153e83f0b7324b4e9d9f78422b6d3628","parentChannelId":"3","payChannelId":10003,"payToken":"1317861aad2641f5a658ca59df32b49a","geetest_challenge":"c6c45b2fb3af527de0ef70e5470ceb53","geetest_validate":"e52083a6d2304a911932fd56562c9083","geetest_seccode":"e52083a6d2304a911932fd56562c9083|jordan"}
[2025-07-31 17:10:16] 验证码结果: {"geetest_challenge":"c6c45b2fb3af527de0ef70e5470ceb53","geetest_validate":"e52083a6d2304a911932fd56562c9083","geetest_seccode":"e52083a6d2304a911932fd56562c9083|jordan"}
[2025-07-31 17:10:16] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:10:16] 请求数据: {"orderId":"P1010127026170250731171015000001","orderToken":"153e83f0b7324b4e9d9f78422b6d3628","parentChannelId":"3","payChannelId":10003,"payToken":"1317861aad2641f5a658ca59df32b49a","geetest_challenge":"c6c45b2fb3af527de0ef70e5470ceb53","geetest_validate":"e52083a6d2304a911932fd56562c9083","geetest_seccode":"e52083a6d2304a911932fd56562c9083|jordan"}
[2025-07-31 17:10:16] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:10:16] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:10:16] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:10:16] 验证码格式 1 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:10:16] 尝试验证码格式 2: {"geetest_challenge":"c6c45b2fb3af527de0ef70e5470ceb53","geetest_validate":"55d7e99cc5e72b278413eddd37d051428bf213de9b5c0c337eb7bf6063c8d56f","geetest_seccode":"55d7e99cc5e72b278413eddd37d051428bf213de9b5c0c337eb7bf6063c8d56f|jordan"}
[2025-07-31 17:10:16] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:10:16] 第三步请求参数: {"orderId":"P1010127026170250731171015000001","orderToken":"153e83f0b7324b4e9d9f78422b6d3628","parentChannelId":"3","payChannelId":10003,"payToken":"1317861aad2641f5a658ca59df32b49a","geetest_challenge":"c6c45b2fb3af527de0ef70e5470ceb53","geetest_validate":"55d7e99cc5e72b278413eddd37d051428bf213de9b5c0c337eb7bf6063c8d56f","geetest_seccode":"55d7e99cc5e72b278413eddd37d051428bf213de9b5c0c337eb7bf6063c8d56f|jordan"}
[2025-07-31 17:10:16] 验证码结果: {"geetest_challenge":"c6c45b2fb3af527de0ef70e5470ceb53","geetest_validate":"55d7e99cc5e72b278413eddd37d051428bf213de9b5c0c337eb7bf6063c8d56f","geetest_seccode":"55d7e99cc5e72b278413eddd37d051428bf213de9b5c0c337eb7bf6063c8d56f|jordan"}
[2025-07-31 17:10:16] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:10:16] 请求数据: {"orderId":"P1010127026170250731171015000001","orderToken":"153e83f0b7324b4e9d9f78422b6d3628","parentChannelId":"3","payChannelId":10003,"payToken":"1317861aad2641f5a658ca59df32b49a","geetest_challenge":"c6c45b2fb3af527de0ef70e5470ceb53","geetest_validate":"55d7e99cc5e72b278413eddd37d051428bf213de9b5c0c337eb7bf6063c8d56f","geetest_seccode":"55d7e99cc5e72b278413eddd37d051428bf213de9b5c0c337eb7bf6063c8d56f|jordan"}
[2025-07-31 17:10:16] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:10:16] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:10:16] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:10:16] 验证码格式 2 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:10:16] 尝试验证码格式 3: []
[2025-07-31 17:10:16] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:10:16] 第三步请求参数: {"orderId":"P1010127026170250731171015000001","orderToken":"153e83f0b7324b4e9d9f78422b6d3628","parentChannelId":"3","payChannelId":10003,"payToken":"1317861aad2641f5a658ca59df32b49a"}
[2025-07-31 17:10:16] 验证码结果: []
[2025-07-31 17:10:16] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:10:16] 请求数据: {"orderId":"P1010127026170250731171015000001","orderToken":"153e83f0b7324b4e9d9f78422b6d3628","parentChannelId":"3","payChannelId":10003,"payToken":"1317861aad2641f5a658ca59df32b49a"}
[2025-07-31 17:10:16] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:10:16] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:10:16] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:10:16] 验证码格式 3 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:10:16] 尝试验证码格式 4: {"picCode":"gtest","gtData":{"challenge":"c6c45b2fb3af527de0ef70e5470ceb53","validate":"978a3da918599c86d96add6d2ab00d08","seccode":"978a3da918599c86d96add6d2ab00d08|jordan"}}
[2025-07-31 17:10:16] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:10:16] 第三步请求参数: {"orderId":"P1010127026170250731171015000001","orderToken":"153e83f0b7324b4e9d9f78422b6d3628","parentChannelId":"3","payChannelId":10003,"payToken":"1317861aad2641f5a658ca59df32b49a","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"c6c45b2fb3af527de0ef70e5470ceb53\",\"validate\":\"978a3da918599c86d96add6d2ab00d08\",\"seccode\":\"978a3da918599c86d96add6d2ab00d08|jordan\"}}"}
[2025-07-31 17:10:16] 验证码结果: {"picCode":"gtest","gtData":{"challenge":"c6c45b2fb3af527de0ef70e5470ceb53","validate":"978a3da918599c86d96add6d2ab00d08","seccode":"978a3da918599c86d96add6d2ab00d08|jordan"}}
[2025-07-31 17:10:16] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:10:16] 请求数据: {"orderId":"P1010127026170250731171015000001","orderToken":"153e83f0b7324b4e9d9f78422b6d3628","parentChannelId":"3","payChannelId":10003,"payToken":"1317861aad2641f5a658ca59df32b49a","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"c6c45b2fb3af527de0ef70e5470ceb53\",\"validate\":\"978a3da918599c86d96add6d2ab00d08\",\"seccode\":\"978a3da918599c86d96add6d2ab00d08|jordan\"}}"}
[2025-07-31 17:10:16] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:10:16] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:10:16] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:10:16] 验证码格式 4 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:10:16] 即时处理失败: 所有验证码格式都已尝试，均未成功
[2025-07-31 17:10:16] 构造验证码页面URL: /sdo_browser_captcha.php?trade_no=2025073117101456513&orderToken=153e83f0b7324b4e9d9f78422b6d3628&orderId=P1010127026170250731171015000001&payToken=1317861aad2641f5a658ca59df32b49a&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT1jNmM0NWIyZmIzYWY1MjdkZTBlZjcwZTU0NzBjZWI1MyZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiJjNmM0NWIyZmIzYWY1MjdkZTBlZjcwZTU0NzBjZWI1MyJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-07-31 17:10:16] SDO支付流程完成，最终支付URL: Array
[2025-07-31 17:10:16] extractQrcodeFromPaymentUrl返回结果类型: array
[2025-07-31 17:10:16] 返回数组内容: {"type":"jump","url":"\/sdo_browser_captcha.php?trade_no=2025073117101456513&orderToken=153e83f0b7324b4e9d9f78422b6d3628&orderId=P1010127026170250731171015000001&payToken=1317861aad2641f5a658ca59df32b49a&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT1jNmM0NWIyZmIzYWY1MjdkZTBlZjcwZTU0NzBjZWI1MyZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiJjNmM0NWIyZmIzYWY1MjdkZTBlZjcwZTU0NzBjZWI1MyJ9fSwiY2FwdGNoYVR5cGUiOjR9","message":"需要完成验证码验证"}
[2025-07-31 17:10:16] 返回验证码页面，URL: /sdo_browser_captcha.php?trade_no=2025073117101456513&orderToken=153e83f0b7324b4e9d9f78422b6d3628&orderId=P1010127026170250731171015000001&payToken=1317861aad2641f5a658ca59df32b49a&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT1jNmM0NWIyZmIzYWY1MjdkZTBlZjcwZTU0NzBjZWI1MyZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiJjNmM0NWIyZmIzYWY1MjdkZTBlZjcwZTU0NzBjZWI1MyJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-07-31 17:22:01] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-07-31 17:22:01] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-07-31 17:22:02] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-07-31 17:22:02] 充值页面访问成功，页面长度: 27735
[2025-07-31 17:22:02] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-07-31 17:22:02] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-07-31 17:22:02] 成功提取token - itemToken: 484b76b48b8b4edb926d3803c81699fd, captchaToken: fe4b327162c24878af1744574e1d22f0
[2025-07-31 17:22:02] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-07-31 17:22:02] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"fe4b327162c24878af1744574e1d22f0","itemToken":"484b76b48b8b4edb926d3803c81699fd","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-07-31 17:22:02] 开始提交充值订单
[2025-07-31 17:22:02] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-07-31 17:22:02] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"fe4b327162c24878af1744574e1d22f0","itemToken":"484b76b48b8b4edb926d3803c81699fd","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-07-31 17:22:02] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-07-31 17:22:02] 订单提交完成，响应类型: string
[2025-07-31 17:22:02] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"fe1279c813154d8083b116b89391618d","orderId":"P1010129058031250731172202000001"}}
[2025-07-31 17:22:02] 开始处理订单响应，支付方式: alipay
[2025-07-31 17:22:02] 开始处理订单响应，响应类型: string
[2025-07-31 17:22:02] 检测到JSON响应，开始解析
[2025-07-31 17:22:02] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"fe1279c813154d8083b116b89391618d","orderId":"P1010129058031250731172202000001"}}
[2025-07-31 17:22:02] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=fe1279c813154d8083b116b89391618d&orderId=P1010129058031250731172202000001
[2025-07-31 17:22:02] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=fe1279c813154d8083b116b89391618d&orderId=P1010129058031250731172202000001
[2025-07-31 17:22:02] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=fe1279c813154d8083b116b89391618d&orderId=P1010129058031250731172202000001
[2025-07-31 17:22:02] 提取参数成功 - orderToken: fe1279c813154d8083b116b89391618d, orderId: P1010129058031250731172202000001
[2025-07-31 17:22:02] 步骤1: 访问支付页面获取payToken
[2025-07-31 17:22:02] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=fe1279c813154d8083b116b89391618d&orderId=P1010129058031250731172202000001, 方法: GET
[2025-07-31 17:22:02] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-07-31 17:22:02] 成功提取payToken: f174b8ce673d42aa82a6c316053183e2
[2025-07-31 17:22:02] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-07-31 17:22:02] 第一次请求参数: {"orderId":"P1010129058031250731172202000001","orderToken":"fe1279c813154d8083b116b89391618d","payChannelId":"3","payToken":"f174b8ce673d42aa82a6c316053183e2","routerFlg":"1"}
[2025-07-31 17:22:02] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:22:02] 请求数据: {"orderId":"P1010129058031250731172202000001","orderToken":"fe1279c813154d8083b116b89391618d","payChannelId":"3","payToken":"f174b8ce673d42aa82a6c316053183e2","routerFlg":"1"}
[2025-07-31 17:22:02] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-07-31 17:22:02] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-07-31 17:22:02] 第一次请求成功，获得最终payChannelId: 10003
[2025-07-31 17:22:02] 步骤3: 完成支付流程，payChannelId: 10003
[2025-07-31 17:22:02] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:22:02] 请求数据: {"orderId":"P1010129058031250731172202000001","orderToken":"fe1279c813154d8083b116b89391618d","payChannelId":10003,"payToken":"f174b8ce673d42aa82a6c316053183e2"}
[2025-07-31 17:22:03] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-07-31 17:22:03] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=f7802b0b5af19a94822029b3d26acff2&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"f7802b0b5af19a94822029b3d26acff2\"}},\"captchaType\":4}"}}
[2025-07-31 17:22:03] 需要验证码，获取最新验证码参数
[2025-07-31 17:22:03] 最新验证码信息: {"captchaParams":{"gtData":{"gt":"31cc9ac8ae5eb9ef1aeaee9110bfc50c","success":1,"gt_url":"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=f7802b0b5af19a94822029b3d26acff2&offline=false&new_captcha=true&callfunction=","new_captcha":1,"challenge":"f7802b0b5af19a94822029b3d26acff2"}},"captchaType":4}
[2025-07-31 17:22:03] 尝试即时处理支付，跳过验证码验证
[2025-07-31 17:22:03] 尝试验证码格式 1: {"geetest_challenge":"f7802b0b5af19a94822029b3d26acff2","geetest_validate":"ba643bcd7f69bc371f53986e60ff357a","geetest_seccode":"ba643bcd7f69bc371f53986e60ff357a|jordan"}
[2025-07-31 17:22:03] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:22:03] 第三步请求参数: {"orderId":"P1010129058031250731172202000001","orderToken":"fe1279c813154d8083b116b89391618d","parentChannelId":"3","payChannelId":10003,"payToken":"f174b8ce673d42aa82a6c316053183e2","geetest_challenge":"f7802b0b5af19a94822029b3d26acff2","geetest_validate":"ba643bcd7f69bc371f53986e60ff357a","geetest_seccode":"ba643bcd7f69bc371f53986e60ff357a|jordan"}
[2025-07-31 17:22:03] 验证码结果: {"geetest_challenge":"f7802b0b5af19a94822029b3d26acff2","geetest_validate":"ba643bcd7f69bc371f53986e60ff357a","geetest_seccode":"ba643bcd7f69bc371f53986e60ff357a|jordan"}
[2025-07-31 17:22:03] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:22:03] 请求数据: {"orderId":"P1010129058031250731172202000001","orderToken":"fe1279c813154d8083b116b89391618d","parentChannelId":"3","payChannelId":10003,"payToken":"f174b8ce673d42aa82a6c316053183e2","geetest_challenge":"f7802b0b5af19a94822029b3d26acff2","geetest_validate":"ba643bcd7f69bc371f53986e60ff357a","geetest_seccode":"ba643bcd7f69bc371f53986e60ff357a|jordan"}
[2025-07-31 17:22:03] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:22:03] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:22:03] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:22:03] 验证码格式 1 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:22:03] 尝试验证码格式 2: {"geetest_challenge":"f7802b0b5af19a94822029b3d26acff2","geetest_validate":"4e00bef72eec5b37cdb362d778192a910e0d772e37a3fc6c8f81d8ea80f7f304","geetest_seccode":"4e00bef72eec5b37cdb362d778192a910e0d772e37a3fc6c8f81d8ea80f7f304|jordan"}
[2025-07-31 17:22:03] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:22:03] 第三步请求参数: {"orderId":"P1010129058031250731172202000001","orderToken":"fe1279c813154d8083b116b89391618d","parentChannelId":"3","payChannelId":10003,"payToken":"f174b8ce673d42aa82a6c316053183e2","geetest_challenge":"f7802b0b5af19a94822029b3d26acff2","geetest_validate":"4e00bef72eec5b37cdb362d778192a910e0d772e37a3fc6c8f81d8ea80f7f304","geetest_seccode":"4e00bef72eec5b37cdb362d778192a910e0d772e37a3fc6c8f81d8ea80f7f304|jordan"}
[2025-07-31 17:22:03] 验证码结果: {"geetest_challenge":"f7802b0b5af19a94822029b3d26acff2","geetest_validate":"4e00bef72eec5b37cdb362d778192a910e0d772e37a3fc6c8f81d8ea80f7f304","geetest_seccode":"4e00bef72eec5b37cdb362d778192a910e0d772e37a3fc6c8f81d8ea80f7f304|jordan"}
[2025-07-31 17:22:03] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:22:03] 请求数据: {"orderId":"P1010129058031250731172202000001","orderToken":"fe1279c813154d8083b116b89391618d","parentChannelId":"3","payChannelId":10003,"payToken":"f174b8ce673d42aa82a6c316053183e2","geetest_challenge":"f7802b0b5af19a94822029b3d26acff2","geetest_validate":"4e00bef72eec5b37cdb362d778192a910e0d772e37a3fc6c8f81d8ea80f7f304","geetest_seccode":"4e00bef72eec5b37cdb362d778192a910e0d772e37a3fc6c8f81d8ea80f7f304|jordan"}
[2025-07-31 17:22:03] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:22:03] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:22:03] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:22:03] 验证码格式 2 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:22:03] 尝试验证码格式 3: []
[2025-07-31 17:22:03] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:22:03] 第三步请求参数: {"orderId":"P1010129058031250731172202000001","orderToken":"fe1279c813154d8083b116b89391618d","parentChannelId":"3","payChannelId":10003,"payToken":"f174b8ce673d42aa82a6c316053183e2"}
[2025-07-31 17:22:03] 验证码结果: []
[2025-07-31 17:22:03] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:22:03] 请求数据: {"orderId":"P1010129058031250731172202000001","orderToken":"fe1279c813154d8083b116b89391618d","parentChannelId":"3","payChannelId":10003,"payToken":"f174b8ce673d42aa82a6c316053183e2"}
[2025-07-31 17:22:03] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:22:03] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:22:03] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:22:03] 验证码格式 3 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:22:03] 尝试验证码格式 4: {"picCode":"gtest","gtData":{"challenge":"f7802b0b5af19a94822029b3d26acff2","validate":"cf65da69b8fefb699060f746b76707d0","seccode":"cf65da69b8fefb699060f746b76707d0|jordan"}}
[2025-07-31 17:22:03] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:22:03] 第三步请求参数: {"orderId":"P1010129058031250731172202000001","orderToken":"fe1279c813154d8083b116b89391618d","parentChannelId":"3","payChannelId":10003,"payToken":"f174b8ce673d42aa82a6c316053183e2","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"f7802b0b5af19a94822029b3d26acff2\",\"validate\":\"cf65da69b8fefb699060f746b76707d0\",\"seccode\":\"cf65da69b8fefb699060f746b76707d0|jordan\"}}"}
[2025-07-31 17:22:03] 验证码结果: {"picCode":"gtest","gtData":{"challenge":"f7802b0b5af19a94822029b3d26acff2","validate":"cf65da69b8fefb699060f746b76707d0","seccode":"cf65da69b8fefb699060f746b76707d0|jordan"}}
[2025-07-31 17:22:03] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:22:03] 请求数据: {"orderId":"P1010129058031250731172202000001","orderToken":"fe1279c813154d8083b116b89391618d","parentChannelId":"3","payChannelId":10003,"payToken":"f174b8ce673d42aa82a6c316053183e2","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"f7802b0b5af19a94822029b3d26acff2\",\"validate\":\"cf65da69b8fefb699060f746b76707d0\",\"seccode\":\"cf65da69b8fefb699060f746b76707d0|jordan\"}}"}
[2025-07-31 17:22:03] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:22:03] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:22:03] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:22:03] 验证码格式 4 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:22:03] 即时处理失败: 所有验证码格式都已尝试，均未成功
[2025-07-31 17:22:03] 构造验证码页面URL: /sdo_browser_captcha.php?trade_no=2025073117220113884&orderToken=fe1279c813154d8083b116b89391618d&orderId=P1010129058031250731172202000001&payToken=f174b8ce673d42aa82a6c316053183e2&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT1mNzgwMmIwYjVhZjE5YTk0ODIyMDI5YjNkMjZhY2ZmMiZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiJmNzgwMmIwYjVhZjE5YTk0ODIyMDI5YjNkMjZhY2ZmMiJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-07-31 17:22:03] SDO支付流程完成，最终支付URL: Array
[2025-07-31 17:22:03] extractQrcodeFromPaymentUrl返回结果类型: array
[2025-07-31 17:22:03] 返回数组内容: {"type":"jump","url":"\/sdo_browser_captcha.php?trade_no=2025073117220113884&orderToken=fe1279c813154d8083b116b89391618d&orderId=P1010129058031250731172202000001&payToken=f174b8ce673d42aa82a6c316053183e2&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT1mNzgwMmIwYjVhZjE5YTk0ODIyMDI5YjNkMjZhY2ZmMiZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiJmNzgwMmIwYjVhZjE5YTk0ODIyMDI5YjNkMjZhY2ZmMiJ9fSwiY2FwdGNoYVR5cGUiOjR9","message":"需要完成验证码验证"}
[2025-07-31 17:22:03] 返回验证码页面，URL: /sdo_browser_captcha.php?trade_no=2025073117220113884&orderToken=fe1279c813154d8083b116b89391618d&orderId=P1010129058031250731172202000001&payToken=f174b8ce673d42aa82a6c316053183e2&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT1mNzgwMmIwYjVhZjE5YTk0ODIyMDI5YjNkMjZhY2ZmMiZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiJmNzgwMmIwYjVhZjE5YTk0ODIyMDI5YjNkMjZhY2ZmMiJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-07-31 17:26:15] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-07-31 17:26:15] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-07-31 17:26:16] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-07-31 17:26:16] 充值页面访问成功，页面长度: 27735
[2025-07-31 17:26:16] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-07-31 17:26:16] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-07-31 17:26:16] 成功提取token - itemToken: 7b26b772fbf244ae9e173708be640c8f, captchaToken: 4fa17e22ca5141f2a2654bb1641d2d6f
[2025-07-31 17:26:16] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-07-31 17:26:16] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"4fa17e22ca5141f2a2654bb1641d2d6f","itemToken":"7b26b772fbf244ae9e173708be640c8f","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-07-31 17:26:16] 开始提交充值订单
[2025-07-31 17:26:16] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-07-31 17:26:16] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"4fa17e22ca5141f2a2654bb1641d2d6f","itemToken":"7b26b772fbf244ae9e173708be640c8f","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-07-31 17:26:16] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-07-31 17:26:16] 订单提交完成，响应类型: string
[2025-07-31 17:26:16] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"eb09951140f34cafaa8e3173c08bc743","orderId":"P1010127026169250731172616000001"}}
[2025-07-31 17:26:16] 开始处理订单响应，支付方式: alipay
[2025-07-31 17:26:16] 开始处理订单响应，响应类型: string
[2025-07-31 17:26:16] 检测到JSON响应，开始解析
[2025-07-31 17:26:16] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"eb09951140f34cafaa8e3173c08bc743","orderId":"P1010127026169250731172616000001"}}
[2025-07-31 17:26:16] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=eb09951140f34cafaa8e3173c08bc743&orderId=P1010127026169250731172616000001
[2025-07-31 17:26:16] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=eb09951140f34cafaa8e3173c08bc743&orderId=P1010127026169250731172616000001
[2025-07-31 17:26:16] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=eb09951140f34cafaa8e3173c08bc743&orderId=P1010127026169250731172616000001
[2025-07-31 17:26:16] 提取参数成功 - orderToken: eb09951140f34cafaa8e3173c08bc743, orderId: P1010127026169250731172616000001
[2025-07-31 17:26:16] 步骤1: 访问支付页面获取payToken
[2025-07-31 17:26:16] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=eb09951140f34cafaa8e3173c08bc743&orderId=P1010127026169250731172616000001, 方法: GET
[2025-07-31 17:26:16] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-07-31 17:26:16] 成功提取payToken: 5d4251511e6246468793879a0bee470e
[2025-07-31 17:26:16] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-07-31 17:26:16] 第一次请求参数: {"orderId":"P1010127026169250731172616000001","orderToken":"eb09951140f34cafaa8e3173c08bc743","payChannelId":"3","payToken":"5d4251511e6246468793879a0bee470e","routerFlg":"1"}
[2025-07-31 17:26:16] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:26:16] 请求数据: {"orderId":"P1010127026169250731172616000001","orderToken":"eb09951140f34cafaa8e3173c08bc743","payChannelId":"3","payToken":"5d4251511e6246468793879a0bee470e","routerFlg":"1"}
[2025-07-31 17:26:16] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-07-31 17:26:16] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-07-31 17:26:16] 第一次请求成功，获得最终payChannelId: 10003
[2025-07-31 17:26:16] 步骤3: 完成支付流程，payChannelId: 10003
[2025-07-31 17:26:16] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:26:16] 请求数据: {"orderId":"P1010127026169250731172616000001","orderToken":"eb09951140f34cafaa8e3173c08bc743","payChannelId":10003,"payToken":"5d4251511e6246468793879a0bee470e"}
[2025-07-31 17:26:16] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-07-31 17:26:17] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=45625346ba9f0352f7862cc525386968&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"45625346ba9f0352f7862cc525386968\"}},\"captchaType\":4}"}}
[2025-07-31 17:26:17] 需要验证码，获取最新验证码参数
[2025-07-31 17:26:17] 最新验证码信息: {"captchaParams":{"gtData":{"gt":"31cc9ac8ae5eb9ef1aeaee9110bfc50c","success":1,"gt_url":"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=45625346ba9f0352f7862cc525386968&offline=false&new_captcha=true&callfunction=","new_captcha":1,"challenge":"45625346ba9f0352f7862cc525386968"}},"captchaType":4}
[2025-07-31 17:26:17] 尝试即时处理支付，跳过验证码验证
[2025-07-31 17:26:17] 尝试验证码格式 1: {"geetest_challenge":"45625346ba9f0352f7862cc525386968","geetest_validate":"948830768aacf22a46e91218032fd456","geetest_seccode":"948830768aacf22a46e91218032fd456|jordan"}
[2025-07-31 17:26:17] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:26:17] 第三步请求参数: {"orderId":"P1010127026169250731172616000001","orderToken":"eb09951140f34cafaa8e3173c08bc743","parentChannelId":"3","payChannelId":10003,"payToken":"5d4251511e6246468793879a0bee470e","geetest_challenge":"45625346ba9f0352f7862cc525386968","geetest_validate":"948830768aacf22a46e91218032fd456","geetest_seccode":"948830768aacf22a46e91218032fd456|jordan"}
[2025-07-31 17:26:17] 验证码结果: {"geetest_challenge":"45625346ba9f0352f7862cc525386968","geetest_validate":"948830768aacf22a46e91218032fd456","geetest_seccode":"948830768aacf22a46e91218032fd456|jordan"}
[2025-07-31 17:26:17] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:26:17] 请求数据: {"orderId":"P1010127026169250731172616000001","orderToken":"eb09951140f34cafaa8e3173c08bc743","parentChannelId":"3","payChannelId":10003,"payToken":"5d4251511e6246468793879a0bee470e","geetest_challenge":"45625346ba9f0352f7862cc525386968","geetest_validate":"948830768aacf22a46e91218032fd456","geetest_seccode":"948830768aacf22a46e91218032fd456|jordan"}
[2025-07-31 17:26:17] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:26:17] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:26:17] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:26:17] 验证码格式 1 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:26:17] 尝试验证码格式 2: {"geetest_challenge":"45625346ba9f0352f7862cc525386968","geetest_validate":"bfa0d417956069bfad2d519b79bbc39861fd35a513734a70fa6392f16babb97c","geetest_seccode":"bfa0d417956069bfad2d519b79bbc39861fd35a513734a70fa6392f16babb97c|jordan"}
[2025-07-31 17:26:17] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:26:17] 第三步请求参数: {"orderId":"P1010127026169250731172616000001","orderToken":"eb09951140f34cafaa8e3173c08bc743","parentChannelId":"3","payChannelId":10003,"payToken":"5d4251511e6246468793879a0bee470e","geetest_challenge":"45625346ba9f0352f7862cc525386968","geetest_validate":"bfa0d417956069bfad2d519b79bbc39861fd35a513734a70fa6392f16babb97c","geetest_seccode":"bfa0d417956069bfad2d519b79bbc39861fd35a513734a70fa6392f16babb97c|jordan"}
[2025-07-31 17:26:17] 验证码结果: {"geetest_challenge":"45625346ba9f0352f7862cc525386968","geetest_validate":"bfa0d417956069bfad2d519b79bbc39861fd35a513734a70fa6392f16babb97c","geetest_seccode":"bfa0d417956069bfad2d519b79bbc39861fd35a513734a70fa6392f16babb97c|jordan"}
[2025-07-31 17:26:17] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:26:17] 请求数据: {"orderId":"P1010127026169250731172616000001","orderToken":"eb09951140f34cafaa8e3173c08bc743","parentChannelId":"3","payChannelId":10003,"payToken":"5d4251511e6246468793879a0bee470e","geetest_challenge":"45625346ba9f0352f7862cc525386968","geetest_validate":"bfa0d417956069bfad2d519b79bbc39861fd35a513734a70fa6392f16babb97c","geetest_seccode":"bfa0d417956069bfad2d519b79bbc39861fd35a513734a70fa6392f16babb97c|jordan"}
[2025-07-31 17:26:17] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:26:17] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:26:17] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:26:17] 验证码格式 2 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:26:17] 尝试验证码格式 3: []
[2025-07-31 17:26:17] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:26:17] 第三步请求参数: {"orderId":"P1010127026169250731172616000001","orderToken":"eb09951140f34cafaa8e3173c08bc743","parentChannelId":"3","payChannelId":10003,"payToken":"5d4251511e6246468793879a0bee470e"}
[2025-07-31 17:26:17] 验证码结果: []
[2025-07-31 17:26:17] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:26:17] 请求数据: {"orderId":"P1010127026169250731172616000001","orderToken":"eb09951140f34cafaa8e3173c08bc743","parentChannelId":"3","payChannelId":10003,"payToken":"5d4251511e6246468793879a0bee470e"}
[2025-07-31 17:26:17] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:26:17] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:26:17] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:26:17] 验证码格式 3 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:26:17] 尝试验证码格式 4: {"picCode":"gtest","gtData":{"challenge":"45625346ba9f0352f7862cc525386968","validate":"408a807edb6da7f01a049e34e73666b0","seccode":"408a807edb6da7f01a049e34e73666b0|jordan"}}
[2025-07-31 17:26:17] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:26:17] 第三步请求参数: {"orderId":"P1010127026169250731172616000001","orderToken":"eb09951140f34cafaa8e3173c08bc743","parentChannelId":"3","payChannelId":10003,"payToken":"5d4251511e6246468793879a0bee470e","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"45625346ba9f0352f7862cc525386968\",\"validate\":\"408a807edb6da7f01a049e34e73666b0\",\"seccode\":\"408a807edb6da7f01a049e34e73666b0|jordan\"}}"}
[2025-07-31 17:26:17] 验证码结果: {"picCode":"gtest","gtData":{"challenge":"45625346ba9f0352f7862cc525386968","validate":"408a807edb6da7f01a049e34e73666b0","seccode":"408a807edb6da7f01a049e34e73666b0|jordan"}}
[2025-07-31 17:26:17] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:26:17] 请求数据: {"orderId":"P1010127026169250731172616000001","orderToken":"eb09951140f34cafaa8e3173c08bc743","parentChannelId":"3","payChannelId":10003,"payToken":"5d4251511e6246468793879a0bee470e","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"45625346ba9f0352f7862cc525386968\",\"validate\":\"408a807edb6da7f01a049e34e73666b0\",\"seccode\":\"408a807edb6da7f01a049e34e73666b0|jordan\"}}"}
[2025-07-31 17:26:18] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:26:18] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:26:18] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:26:18] 验证码格式 4 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:26:18] 即时处理失败: 所有验证码格式都已尝试，均未成功
[2025-07-31 17:26:18] 构造验证码页面URL: /sdo_browser_captcha.php?trade_no=2025073117261573227&orderToken=eb09951140f34cafaa8e3173c08bc743&orderId=P1010127026169250731172616000001&payToken=5d4251511e6246468793879a0bee470e&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT00NTYyNTM0NmJhOWYwMzUyZjc4NjJjYzUyNTM4Njk2OCZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiI0NTYyNTM0NmJhOWYwMzUyZjc4NjJjYzUyNTM4Njk2OCJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-07-31 17:26:18] SDO支付流程完成，最终支付URL: Array
[2025-07-31 17:26:18] extractQrcodeFromPaymentUrl返回结果类型: array
[2025-07-31 17:26:18] 返回数组内容: {"type":"jump","url":"\/sdo_browser_captcha.php?trade_no=2025073117261573227&orderToken=eb09951140f34cafaa8e3173c08bc743&orderId=P1010127026169250731172616000001&payToken=5d4251511e6246468793879a0bee470e&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT00NTYyNTM0NmJhOWYwMzUyZjc4NjJjYzUyNTM4Njk2OCZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiI0NTYyNTM0NmJhOWYwMzUyZjc4NjJjYzUyNTM4Njk2OCJ9fSwiY2FwdGNoYVR5cGUiOjR9","message":"需要完成验证码验证"}
[2025-07-31 17:26:18] 返回验证码页面，URL: /sdo_browser_captcha.php?trade_no=2025073117261573227&orderToken=eb09951140f34cafaa8e3173c08bc743&orderId=P1010127026169250731172616000001&payToken=5d4251511e6246468793879a0bee470e&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT00NTYyNTM0NmJhOWYwMzUyZjc4NjJjYzUyNTM4Njk2OCZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiI0NTYyNTM0NmJhOWYwMzUyZjc4NjJjYzUyNTM4Njk2OCJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-07-31 17:43:53] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-07-31 17:43:53] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-07-31 17:43:53] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-07-31 17:43:53] 充值页面访问成功，页面长度: 27735
[2025-07-31 17:43:53] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-07-31 17:43:53] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-07-31 17:43:53] 成功提取token - itemToken: b9a01e3a5504438bbbe80f1a772ac637, captchaToken: 23d149ebf31f4251bfc1972cc2709387
[2025-07-31 17:43:53] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-07-31 17:43:53] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"23d149ebf31f4251bfc1972cc2709387","itemToken":"b9a01e3a5504438bbbe80f1a772ac637","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-07-31 17:43:53] 开始提交充值订单
[2025-07-31 17:43:53] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-07-31 17:43:53] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"23d149ebf31f4251bfc1972cc2709387","itemToken":"b9a01e3a5504438bbbe80f1a772ac637","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-07-31 17:43:53] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-07-31 17:43:53] 订单提交完成，响应类型: string
[2025-07-31 17:43:53] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"c24ceda198a844f88f7f840102f6efc8","orderId":"P1010129058031250731174354000001"}}
[2025-07-31 17:43:53] 开始处理订单响应，支付方式: alipay
[2025-07-31 17:43:53] 开始处理订单响应，响应类型: string
[2025-07-31 17:43:53] 检测到JSON响应，开始解析
[2025-07-31 17:43:53] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"c24ceda198a844f88f7f840102f6efc8","orderId":"P1010129058031250731174354000001"}}
[2025-07-31 17:43:53] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=c24ceda198a844f88f7f840102f6efc8&orderId=P1010129058031250731174354000001
[2025-07-31 17:43:53] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=c24ceda198a844f88f7f840102f6efc8&orderId=P1010129058031250731174354000001
[2025-07-31 17:43:53] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=c24ceda198a844f88f7f840102f6efc8&orderId=P1010129058031250731174354000001
[2025-07-31 17:43:53] 提取参数成功 - orderToken: c24ceda198a844f88f7f840102f6efc8, orderId: P1010129058031250731174354000001
[2025-07-31 17:43:53] 步骤1: 访问支付页面获取payToken
[2025-07-31 17:43:53] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=c24ceda198a844f88f7f840102f6efc8&orderId=P1010129058031250731174354000001, 方法: GET
[2025-07-31 17:43:54] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-07-31 17:43:54] 成功提取payToken: f0d3ceb7dbac4cd483f3b766d98865d0
[2025-07-31 17:43:54] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-07-31 17:43:54] 第一次请求参数: {"orderId":"P1010129058031250731174354000001","orderToken":"c24ceda198a844f88f7f840102f6efc8","payChannelId":"3","payToken":"f0d3ceb7dbac4cd483f3b766d98865d0","routerFlg":"1"}
[2025-07-31 17:43:54] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:43:54] 请求数据: {"orderId":"P1010129058031250731174354000001","orderToken":"c24ceda198a844f88f7f840102f6efc8","payChannelId":"3","payToken":"f0d3ceb7dbac4cd483f3b766d98865d0","routerFlg":"1"}
[2025-07-31 17:43:54] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-07-31 17:43:54] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-07-31 17:43:54] 第一次请求成功，获得最终payChannelId: 10003
[2025-07-31 17:43:54] 步骤3: 完成支付流程，payChannelId: 10003
[2025-07-31 17:43:54] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:43:54] 请求数据: {"orderId":"P1010129058031250731174354000001","orderToken":"c24ceda198a844f88f7f840102f6efc8","payChannelId":10003,"payToken":"f0d3ceb7dbac4cd483f3b766d98865d0"}
[2025-07-31 17:43:54] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-07-31 17:43:54] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=71374a735059de8db8d9899e00bf7bb0&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"71374a735059de8db8d9899e00bf7bb0\"}},\"captchaType\":4}"}}
[2025-07-31 17:43:54] 需要验证码，获取最新验证码参数
[2025-07-31 17:43:54] 最新验证码信息: {"captchaParams":{"gtData":{"gt":"31cc9ac8ae5eb9ef1aeaee9110bfc50c","success":1,"gt_url":"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=71374a735059de8db8d9899e00bf7bb0&offline=false&new_captcha=true&callfunction=","new_captcha":1,"challenge":"71374a735059de8db8d9899e00bf7bb0"}},"captchaType":4}
[2025-07-31 17:43:54] 尝试即时处理支付，跳过验证码验证
[2025-07-31 17:43:54] 尝试验证码格式 1: {"geetest_challenge":"71374a735059de8db8d9899e00bf7bb0","geetest_validate":"7b7e3b34a2ade816e23a1730c78d016c","geetest_seccode":"7b7e3b34a2ade816e23a1730c78d016c|jordan"}
[2025-07-31 17:43:54] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:43:54] 第三步请求参数: {"orderId":"P1010129058031250731174354000001","orderToken":"c24ceda198a844f88f7f840102f6efc8","parentChannelId":"3","payChannelId":10003,"payToken":"f0d3ceb7dbac4cd483f3b766d98865d0","geetest_challenge":"71374a735059de8db8d9899e00bf7bb0","geetest_validate":"7b7e3b34a2ade816e23a1730c78d016c","geetest_seccode":"7b7e3b34a2ade816e23a1730c78d016c|jordan"}
[2025-07-31 17:43:54] 验证码结果: {"geetest_challenge":"71374a735059de8db8d9899e00bf7bb0","geetest_validate":"7b7e3b34a2ade816e23a1730c78d016c","geetest_seccode":"7b7e3b34a2ade816e23a1730c78d016c|jordan"}
[2025-07-31 17:43:54] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:43:54] 请求数据: {"orderId":"P1010129058031250731174354000001","orderToken":"c24ceda198a844f88f7f840102f6efc8","parentChannelId":"3","payChannelId":10003,"payToken":"f0d3ceb7dbac4cd483f3b766d98865d0","geetest_challenge":"71374a735059de8db8d9899e00bf7bb0","geetest_validate":"7b7e3b34a2ade816e23a1730c78d016c","geetest_seccode":"7b7e3b34a2ade816e23a1730c78d016c|jordan"}
[2025-07-31 17:43:54] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:43:54] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:43:54] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:43:54] 验证码格式 1 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:43:54] 尝试验证码格式 2: {"geetest_challenge":"71374a735059de8db8d9899e00bf7bb0","geetest_validate":"7c0ac431429d23d479f43fa73b9963e548f49e2f7ea496651c74f85e13f691fe","geetest_seccode":"7c0ac431429d23d479f43fa73b9963e548f49e2f7ea496651c74f85e13f691fe|jordan"}
[2025-07-31 17:43:54] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:43:54] 第三步请求参数: {"orderId":"P1010129058031250731174354000001","orderToken":"c24ceda198a844f88f7f840102f6efc8","parentChannelId":"3","payChannelId":10003,"payToken":"f0d3ceb7dbac4cd483f3b766d98865d0","geetest_challenge":"71374a735059de8db8d9899e00bf7bb0","geetest_validate":"7c0ac431429d23d479f43fa73b9963e548f49e2f7ea496651c74f85e13f691fe","geetest_seccode":"7c0ac431429d23d479f43fa73b9963e548f49e2f7ea496651c74f85e13f691fe|jordan"}
[2025-07-31 17:43:54] 验证码结果: {"geetest_challenge":"71374a735059de8db8d9899e00bf7bb0","geetest_validate":"7c0ac431429d23d479f43fa73b9963e548f49e2f7ea496651c74f85e13f691fe","geetest_seccode":"7c0ac431429d23d479f43fa73b9963e548f49e2f7ea496651c74f85e13f691fe|jordan"}
[2025-07-31 17:43:54] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:43:54] 请求数据: {"orderId":"P1010129058031250731174354000001","orderToken":"c24ceda198a844f88f7f840102f6efc8","parentChannelId":"3","payChannelId":10003,"payToken":"f0d3ceb7dbac4cd483f3b766d98865d0","geetest_challenge":"71374a735059de8db8d9899e00bf7bb0","geetest_validate":"7c0ac431429d23d479f43fa73b9963e548f49e2f7ea496651c74f85e13f691fe","geetest_seccode":"7c0ac431429d23d479f43fa73b9963e548f49e2f7ea496651c74f85e13f691fe|jordan"}
[2025-07-31 17:43:54] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:43:54] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:43:54] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:43:54] 验证码格式 2 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:43:54] 尝试验证码格式 3: []
[2025-07-31 17:43:54] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:43:54] 第三步请求参数: {"orderId":"P1010129058031250731174354000001","orderToken":"c24ceda198a844f88f7f840102f6efc8","parentChannelId":"3","payChannelId":10003,"payToken":"f0d3ceb7dbac4cd483f3b766d98865d0"}
[2025-07-31 17:43:54] 验证码结果: []
[2025-07-31 17:43:54] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:43:54] 请求数据: {"orderId":"P1010129058031250731174354000001","orderToken":"c24ceda198a844f88f7f840102f6efc8","parentChannelId":"3","payChannelId":10003,"payToken":"f0d3ceb7dbac4cd483f3b766d98865d0"}
[2025-07-31 17:43:55] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:43:55] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:43:55] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:43:55] 验证码格式 3 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:43:55] 尝试验证码格式 4: {"picCode":"gtest","gtData":{"challenge":"71374a735059de8db8d9899e00bf7bb0","validate":"889753f2af34dc3506059c779f60fc3d","seccode":"889753f2af34dc3506059c779f60fc3d|jordan"}}
[2025-07-31 17:43:55] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:43:55] 第三步请求参数: {"orderId":"P1010129058031250731174354000001","orderToken":"c24ceda198a844f88f7f840102f6efc8","parentChannelId":"3","payChannelId":10003,"payToken":"f0d3ceb7dbac4cd483f3b766d98865d0","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"71374a735059de8db8d9899e00bf7bb0\",\"validate\":\"889753f2af34dc3506059c779f60fc3d\",\"seccode\":\"889753f2af34dc3506059c779f60fc3d|jordan\"}}"}
[2025-07-31 17:43:55] 验证码结果: {"picCode":"gtest","gtData":{"challenge":"71374a735059de8db8d9899e00bf7bb0","validate":"889753f2af34dc3506059c779f60fc3d","seccode":"889753f2af34dc3506059c779f60fc3d|jordan"}}
[2025-07-31 17:43:55] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:43:55] 请求数据: {"orderId":"P1010129058031250731174354000001","orderToken":"c24ceda198a844f88f7f840102f6efc8","parentChannelId":"3","payChannelId":10003,"payToken":"f0d3ceb7dbac4cd483f3b766d98865d0","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"71374a735059de8db8d9899e00bf7bb0\",\"validate\":\"889753f2af34dc3506059c779f60fc3d\",\"seccode\":\"889753f2af34dc3506059c779f60fc3d|jordan\"}}"}
[2025-07-31 17:43:55] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:43:55] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:43:55] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:43:55] 验证码格式 4 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:43:55] 即时处理失败: 所有验证码格式都已尝试，均未成功
[2025-07-31 17:43:55] 构造验证码页面URL: /sdo_browser_captcha.php?trade_no=2025073117435233102&orderToken=c24ceda198a844f88f7f840102f6efc8&orderId=P1010129058031250731174354000001&payToken=f0d3ceb7dbac4cd483f3b766d98865d0&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT03MTM3NGE3MzUwNTlkZThkYjhkOTg5OWUwMGJmN2JiMCZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiI3MTM3NGE3MzUwNTlkZThkYjhkOTg5OWUwMGJmN2JiMCJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-07-31 17:43:55] SDO支付流程完成，最终支付URL: Array
[2025-07-31 17:43:55] extractQrcodeFromPaymentUrl返回结果类型: array
[2025-07-31 17:43:55] 返回数组内容: {"type":"jump","url":"\/sdo_browser_captcha.php?trade_no=2025073117435233102&orderToken=c24ceda198a844f88f7f840102f6efc8&orderId=P1010129058031250731174354000001&payToken=f0d3ceb7dbac4cd483f3b766d98865d0&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT03MTM3NGE3MzUwNTlkZThkYjhkOTg5OWUwMGJmN2JiMCZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiI3MTM3NGE3MzUwNTlkZThkYjhkOTg5OWUwMGJmN2JiMCJ9fSwiY2FwdGNoYVR5cGUiOjR9","message":"需要完成验证码验证"}
[2025-07-31 17:43:55] 返回验证码页面，URL: /sdo_browser_captcha.php?trade_no=2025073117435233102&orderToken=c24ceda198a844f88f7f840102f6efc8&orderId=P1010129058031250731174354000001&payToken=f0d3ceb7dbac4cd483f3b766d98865d0&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT03MTM3NGE3MzUwNTlkZThkYjhkOTg5OWUwMGJmN2JiMCZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiI3MTM3NGE3MzUwNTlkZThkYjhkOTg5OWUwMGJmN2JiMCJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-07-31 17:48:30] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-07-31 17:48:30] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-07-31 17:48:30] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-07-31 17:48:30] 充值页面访问成功，页面长度: 27735
[2025-07-31 17:48:30] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-07-31 17:48:30] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-07-31 17:48:30] 成功提取token - itemToken: 7a8807ac084c40ec8dfa83a6c0ffb3b2, captchaToken: 7e4070e839d048be800134e276897a4f
[2025-07-31 17:48:30] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-07-31 17:48:30] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"7e4070e839d048be800134e276897a4f","itemToken":"7a8807ac084c40ec8dfa83a6c0ffb3b2","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-07-31 17:48:30] 开始提交充值订单
[2025-07-31 17:48:30] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-07-31 17:48:30] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"7e4070e839d048be800134e276897a4f","itemToken":"7a8807ac084c40ec8dfa83a6c0ffb3b2","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-07-31 17:48:31] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-07-31 17:48:31] 订单提交完成，响应类型: string
[2025-07-31 17:48:31] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"20c8da74e430439a9bad3cae9ee074bd","orderId":"P1010129058031250731174831000001"}}
[2025-07-31 17:48:31] 开始处理订单响应，支付方式: alipay
[2025-07-31 17:48:31] 开始处理订单响应，响应类型: string
[2025-07-31 17:48:31] 检测到JSON响应，开始解析
[2025-07-31 17:48:31] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"20c8da74e430439a9bad3cae9ee074bd","orderId":"P1010129058031250731174831000001"}}
[2025-07-31 17:48:31] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=20c8da74e430439a9bad3cae9ee074bd&orderId=P1010129058031250731174831000001
[2025-07-31 17:48:31] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=20c8da74e430439a9bad3cae9ee074bd&orderId=P1010129058031250731174831000001
[2025-07-31 17:48:31] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=20c8da74e430439a9bad3cae9ee074bd&orderId=P1010129058031250731174831000001
[2025-07-31 17:48:31] 提取参数成功 - orderToken: 20c8da74e430439a9bad3cae9ee074bd, orderId: P1010129058031250731174831000001
[2025-07-31 17:48:31] 步骤1: 访问支付页面获取payToken
[2025-07-31 17:48:31] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=20c8da74e430439a9bad3cae9ee074bd&orderId=P1010129058031250731174831000001, 方法: GET
[2025-07-31 17:48:31] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-07-31 17:48:31] 成功提取payToken: 0baddb2859e54e48abefb273396f4252
[2025-07-31 17:48:31] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-07-31 17:48:31] 第一次请求参数: {"orderId":"P1010129058031250731174831000001","orderToken":"20c8da74e430439a9bad3cae9ee074bd","payChannelId":"3","payToken":"0baddb2859e54e48abefb273396f4252","routerFlg":"1"}
[2025-07-31 17:48:31] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:48:31] 请求数据: {"orderId":"P1010129058031250731174831000001","orderToken":"20c8da74e430439a9bad3cae9ee074bd","payChannelId":"3","payToken":"0baddb2859e54e48abefb273396f4252","routerFlg":"1"}
[2025-07-31 17:48:31] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-07-31 17:48:31] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-07-31 17:48:31] 第一次请求成功，获得最终payChannelId: 10003
[2025-07-31 17:48:31] 步骤3: 完成支付流程，payChannelId: 10003
[2025-07-31 17:48:31] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:48:31] 请求数据: {"orderId":"P1010129058031250731174831000001","orderToken":"20c8da74e430439a9bad3cae9ee074bd","payChannelId":10003,"payToken":"0baddb2859e54e48abefb273396f4252"}
[2025-07-31 17:48:31] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-07-31 17:48:31] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=b2c802cd1b9c2de71c81f2f791dd461f&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"b2c802cd1b9c2de71c81f2f791dd461f\"}},\"captchaType\":4}"}}
[2025-07-31 17:48:31] 需要验证码，获取最新验证码参数
[2025-07-31 17:48:31] 最新验证码信息: {"captchaParams":{"gtData":{"gt":"31cc9ac8ae5eb9ef1aeaee9110bfc50c","success":1,"gt_url":"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=b2c802cd1b9c2de71c81f2f791dd461f&offline=false&new_captcha=true&callfunction=","new_captcha":1,"challenge":"b2c802cd1b9c2de71c81f2f791dd461f"}},"captchaType":4}
[2025-07-31 17:48:31] 尝试即时处理支付，跳过验证码验证
[2025-07-31 17:48:31] 使用captchaInfo中的challenge: b2c802cd1b9c2de71c81f2f791dd461f
[2025-07-31 17:48:31] 尝试验证码格式 1: {"geetest_challenge":"b2c802cd1b9c2de71c81f2f791dd461f","geetest_validate":"37b1102f07d0198624b0e8b020548908","geetest_seccode":"37b1102f07d0198624b0e8b020548908|jordan"}
[2025-07-31 17:48:31] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:48:31] 第三步请求参数: {"orderId":"P1010129058031250731174831000001","orderToken":"20c8da74e430439a9bad3cae9ee074bd","parentChannelId":"3","payChannelId":10003,"payToken":"0baddb2859e54e48abefb273396f4252","geetest_challenge":"b2c802cd1b9c2de71c81f2f791dd461f","geetest_validate":"37b1102f07d0198624b0e8b020548908","geetest_seccode":"37b1102f07d0198624b0e8b020548908|jordan"}
[2025-07-31 17:48:31] 验证码结果: {"geetest_challenge":"b2c802cd1b9c2de71c81f2f791dd461f","geetest_validate":"37b1102f07d0198624b0e8b020548908","geetest_seccode":"37b1102f07d0198624b0e8b020548908|jordan"}
[2025-07-31 17:48:31] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:48:31] 请求数据: {"orderId":"P1010129058031250731174831000001","orderToken":"20c8da74e430439a9bad3cae9ee074bd","parentChannelId":"3","payChannelId":10003,"payToken":"0baddb2859e54e48abefb273396f4252","geetest_challenge":"b2c802cd1b9c2de71c81f2f791dd461f","geetest_validate":"37b1102f07d0198624b0e8b020548908","geetest_seccode":"37b1102f07d0198624b0e8b020548908|jordan"}
[2025-07-31 17:48:31] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:48:31] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:48:31] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:48:31] 验证码格式 1 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:48:31] 尝试验证码格式 2: {"geetest_challenge":"b2c802cd1b9c2de71c81f2f791dd461f","geetest_validate":"d5d9c6120357b7fd9b968e1529f6960c6c31445ef4677e2ff95403dbc920046a","geetest_seccode":"d5d9c6120357b7fd9b968e1529f6960c6c31445ef4677e2ff95403dbc920046a|jordan"}
[2025-07-31 17:48:31] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:48:31] 第三步请求参数: {"orderId":"P1010129058031250731174831000001","orderToken":"20c8da74e430439a9bad3cae9ee074bd","parentChannelId":"3","payChannelId":10003,"payToken":"0baddb2859e54e48abefb273396f4252","geetest_challenge":"b2c802cd1b9c2de71c81f2f791dd461f","geetest_validate":"d5d9c6120357b7fd9b968e1529f6960c6c31445ef4677e2ff95403dbc920046a","geetest_seccode":"d5d9c6120357b7fd9b968e1529f6960c6c31445ef4677e2ff95403dbc920046a|jordan"}
[2025-07-31 17:48:31] 验证码结果: {"geetest_challenge":"b2c802cd1b9c2de71c81f2f791dd461f","geetest_validate":"d5d9c6120357b7fd9b968e1529f6960c6c31445ef4677e2ff95403dbc920046a","geetest_seccode":"d5d9c6120357b7fd9b968e1529f6960c6c31445ef4677e2ff95403dbc920046a|jordan"}
[2025-07-31 17:48:32] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:48:32] 请求数据: {"orderId":"P1010129058031250731174831000001","orderToken":"20c8da74e430439a9bad3cae9ee074bd","parentChannelId":"3","payChannelId":10003,"payToken":"0baddb2859e54e48abefb273396f4252","geetest_challenge":"b2c802cd1b9c2de71c81f2f791dd461f","geetest_validate":"d5d9c6120357b7fd9b968e1529f6960c6c31445ef4677e2ff95403dbc920046a","geetest_seccode":"d5d9c6120357b7fd9b968e1529f6960c6c31445ef4677e2ff95403dbc920046a|jordan"}
[2025-07-31 17:48:32] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:48:32] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:48:32] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:48:32] 验证码格式 2 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:48:32] 尝试验证码格式 3: []
[2025-07-31 17:48:32] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:48:32] 第三步请求参数: {"orderId":"P1010129058031250731174831000001","orderToken":"20c8da74e430439a9bad3cae9ee074bd","parentChannelId":"3","payChannelId":10003,"payToken":"0baddb2859e54e48abefb273396f4252"}
[2025-07-31 17:48:32] 验证码结果: []
[2025-07-31 17:48:32] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:48:32] 请求数据: {"orderId":"P1010129058031250731174831000001","orderToken":"20c8da74e430439a9bad3cae9ee074bd","parentChannelId":"3","payChannelId":10003,"payToken":"0baddb2859e54e48abefb273396f4252"}
[2025-07-31 17:48:32] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:48:32] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:48:32] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:48:32] 验证码格式 3 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:48:32] 尝试验证码格式 4: {"picCode":"gtest","gtData":{"challenge":"b2c802cd1b9c2de71c81f2f791dd461f","validate":"0d62b1242cd6c0c8b5c22c3b2c8c1569","seccode":"0d62b1242cd6c0c8b5c22c3b2c8c1569|jordan"}}
[2025-07-31 17:48:32] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:48:32] 第三步请求参数: {"orderId":"P1010129058031250731174831000001","orderToken":"20c8da74e430439a9bad3cae9ee074bd","parentChannelId":"3","payChannelId":10003,"payToken":"0baddb2859e54e48abefb273396f4252","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"b2c802cd1b9c2de71c81f2f791dd461f\",\"validate\":\"0d62b1242cd6c0c8b5c22c3b2c8c1569\",\"seccode\":\"0d62b1242cd6c0c8b5c22c3b2c8c1569|jordan\"}}"}
[2025-07-31 17:48:32] 验证码结果: {"picCode":"gtest","gtData":{"challenge":"b2c802cd1b9c2de71c81f2f791dd461f","validate":"0d62b1242cd6c0c8b5c22c3b2c8c1569","seccode":"0d62b1242cd6c0c8b5c22c3b2c8c1569|jordan"}}
[2025-07-31 17:48:32] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:48:32] 请求数据: {"orderId":"P1010129058031250731174831000001","orderToken":"20c8da74e430439a9bad3cae9ee074bd","parentChannelId":"3","payChannelId":10003,"payToken":"0baddb2859e54e48abefb273396f4252","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"b2c802cd1b9c2de71c81f2f791dd461f\",\"validate\":\"0d62b1242cd6c0c8b5c22c3b2c8c1569\",\"seccode\":\"0d62b1242cd6c0c8b5c22c3b2c8c1569|jordan\"}}"}
[2025-07-31 17:48:32] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:48:32] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:48:32] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:48:32] 验证码格式 4 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:48:32] 即时处理失败: 所有验证码格式都已尝试，均未成功
[2025-07-31 17:48:32] 构造验证码页面URL: /sdo_browser_captcha.php?trade_no=2025073117483079890&orderToken=20c8da74e430439a9bad3cae9ee074bd&orderId=P1010129058031250731174831000001&payToken=0baddb2859e54e48abefb273396f4252&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT1iMmM4MDJjZDFiOWMyZGU3MWM4MWYyZjc5MWRkNDYxZiZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiJiMmM4MDJjZDFiOWMyZGU3MWM4MWYyZjc5MWRkNDYxZiJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-07-31 17:48:32] SDO支付流程完成，最终支付URL: Array
[2025-07-31 17:48:32] extractQrcodeFromPaymentUrl返回结果类型: array
[2025-07-31 17:48:32] 返回数组内容: {"type":"jump","url":"\/sdo_browser_captcha.php?trade_no=2025073117483079890&orderToken=20c8da74e430439a9bad3cae9ee074bd&orderId=P1010129058031250731174831000001&payToken=0baddb2859e54e48abefb273396f4252&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT1iMmM4MDJjZDFiOWMyZGU3MWM4MWYyZjc5MWRkNDYxZiZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiJiMmM4MDJjZDFiOWMyZGU3MWM4MWYyZjc5MWRkNDYxZiJ9fSwiY2FwdGNoYVR5cGUiOjR9","message":"需要完成验证码验证"}
[2025-07-31 17:48:32] 返回验证码页面，URL: /sdo_browser_captcha.php?trade_no=2025073117483079890&orderToken=20c8da74e430439a9bad3cae9ee074bd&orderId=P1010129058031250731174831000001&payToken=0baddb2859e54e48abefb273396f4252&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT1iMmM4MDJjZDFiOWMyZGU3MWM4MWYyZjc5MWRkNDYxZiZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiJiMmM4MDJjZDFiOWMyZGU3MWM4MWYyZjc5MWRkNDYxZiJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-07-31 17:48:40] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-07-31 17:48:40] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-07-31 17:48:40] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-07-31 17:48:40] 充值页面访问成功，页面长度: 27735
[2025-07-31 17:48:40] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-07-31 17:48:40] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-07-31 17:48:40] 成功提取token - itemToken: fe8b7a093a814ffbbd24b6906a9597f7, captchaToken: 010c774d79e6418081af826be19c8389
[2025-07-31 17:48:40] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-07-31 17:48:40] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"010c774d79e6418081af826be19c8389","itemToken":"fe8b7a093a814ffbbd24b6906a9597f7","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-07-31 17:48:40] 开始提交充值订单
[2025-07-31 17:48:40] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-07-31 17:48:40] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"010c774d79e6418081af826be19c8389","itemToken":"fe8b7a093a814ffbbd24b6906a9597f7","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-07-31 17:48:40] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-07-31 17:48:40] 订单提交完成，响应类型: string
[2025-07-31 17:48:40] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"057bf7edf03d409d8c44f9f222afbc52","orderId":"P1010129058031250731174841000001"}}
[2025-07-31 17:48:40] 开始处理订单响应，支付方式: alipay
[2025-07-31 17:48:40] 开始处理订单响应，响应类型: string
[2025-07-31 17:48:40] 检测到JSON响应，开始解析
[2025-07-31 17:48:40] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"057bf7edf03d409d8c44f9f222afbc52","orderId":"P1010129058031250731174841000001"}}
[2025-07-31 17:48:40] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=057bf7edf03d409d8c44f9f222afbc52&orderId=P1010129058031250731174841000001
[2025-07-31 17:48:40] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=057bf7edf03d409d8c44f9f222afbc52&orderId=P1010129058031250731174841000001
[2025-07-31 17:48:40] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=057bf7edf03d409d8c44f9f222afbc52&orderId=P1010129058031250731174841000001
[2025-07-31 17:48:40] 提取参数成功 - orderToken: 057bf7edf03d409d8c44f9f222afbc52, orderId: P1010129058031250731174841000001
[2025-07-31 17:48:40] 步骤1: 访问支付页面获取payToken
[2025-07-31 17:48:40] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=057bf7edf03d409d8c44f9f222afbc52&orderId=P1010129058031250731174841000001, 方法: GET
[2025-07-31 17:48:40] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-07-31 17:48:40] 成功提取payToken: e052f4c1a53c49dc99dc4f98ab690c2e
[2025-07-31 17:48:40] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-07-31 17:48:40] 第一次请求参数: {"orderId":"P1010129058031250731174841000001","orderToken":"057bf7edf03d409d8c44f9f222afbc52","payChannelId":"3","payToken":"e052f4c1a53c49dc99dc4f98ab690c2e","routerFlg":"1"}
[2025-07-31 17:48:40] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:48:40] 请求数据: {"orderId":"P1010129058031250731174841000001","orderToken":"057bf7edf03d409d8c44f9f222afbc52","payChannelId":"3","payToken":"e052f4c1a53c49dc99dc4f98ab690c2e","routerFlg":"1"}
[2025-07-31 17:48:40] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-07-31 17:48:40] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-07-31 17:48:40] 第一次请求成功，获得最终payChannelId: 10003
[2025-07-31 17:48:40] 步骤3: 完成支付流程，payChannelId: 10003
[2025-07-31 17:48:40] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:48:40] 请求数据: {"orderId":"P1010129058031250731174841000001","orderToken":"057bf7edf03d409d8c44f9f222afbc52","payChannelId":10003,"payToken":"e052f4c1a53c49dc99dc4f98ab690c2e"}
[2025-07-31 17:48:40] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-07-31 17:48:40] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=b0794a9937fdf0c25195d00830ea6790&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"b0794a9937fdf0c25195d00830ea6790\"}},\"captchaType\":4}"}}
[2025-07-31 17:48:40] 需要验证码，获取最新验证码参数
[2025-07-31 17:48:40] 最新验证码信息: {"captchaParams":{"gtData":{"gt":"31cc9ac8ae5eb9ef1aeaee9110bfc50c","success":1,"gt_url":"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=b0794a9937fdf0c25195d00830ea6790&offline=false&new_captcha=true&callfunction=","new_captcha":1,"challenge":"b0794a9937fdf0c25195d00830ea6790"}},"captchaType":4}
[2025-07-31 17:48:40] 尝试即时处理支付，跳过验证码验证
[2025-07-31 17:48:40] 使用captchaInfo中的challenge: b0794a9937fdf0c25195d00830ea6790
[2025-07-31 17:48:40] 尝试验证码格式 1: {"geetest_challenge":"b0794a9937fdf0c25195d00830ea6790","geetest_validate":"11f2f628ae1cad4024471ad2076437ec","geetest_seccode":"11f2f628ae1cad4024471ad2076437ec|jordan"}
[2025-07-31 17:48:40] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:48:40] 第三步请求参数: {"orderId":"P1010129058031250731174841000001","orderToken":"057bf7edf03d409d8c44f9f222afbc52","parentChannelId":"3","payChannelId":10003,"payToken":"e052f4c1a53c49dc99dc4f98ab690c2e","geetest_challenge":"b0794a9937fdf0c25195d00830ea6790","geetest_validate":"11f2f628ae1cad4024471ad2076437ec","geetest_seccode":"11f2f628ae1cad4024471ad2076437ec|jordan"}
[2025-07-31 17:48:40] 验证码结果: {"geetest_challenge":"b0794a9937fdf0c25195d00830ea6790","geetest_validate":"11f2f628ae1cad4024471ad2076437ec","geetest_seccode":"11f2f628ae1cad4024471ad2076437ec|jordan"}
[2025-07-31 17:48:40] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:48:40] 请求数据: {"orderId":"P1010129058031250731174841000001","orderToken":"057bf7edf03d409d8c44f9f222afbc52","parentChannelId":"3","payChannelId":10003,"payToken":"e052f4c1a53c49dc99dc4f98ab690c2e","geetest_challenge":"b0794a9937fdf0c25195d00830ea6790","geetest_validate":"11f2f628ae1cad4024471ad2076437ec","geetest_seccode":"11f2f628ae1cad4024471ad2076437ec|jordan"}
[2025-07-31 17:48:41] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:48:41] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:48:41] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:48:41] 验证码格式 1 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:48:41] 尝试验证码格式 2: {"geetest_challenge":"b0794a9937fdf0c25195d00830ea6790","geetest_validate":"edfb76c97c689dadab07f437aa893f7867d328e31d7daeb2947ecc587e6a12d4","geetest_seccode":"edfb76c97c689dadab07f437aa893f7867d328e31d7daeb2947ecc587e6a12d4|jordan"}
[2025-07-31 17:48:41] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:48:41] 第三步请求参数: {"orderId":"P1010129058031250731174841000001","orderToken":"057bf7edf03d409d8c44f9f222afbc52","parentChannelId":"3","payChannelId":10003,"payToken":"e052f4c1a53c49dc99dc4f98ab690c2e","geetest_challenge":"b0794a9937fdf0c25195d00830ea6790","geetest_validate":"edfb76c97c689dadab07f437aa893f7867d328e31d7daeb2947ecc587e6a12d4","geetest_seccode":"edfb76c97c689dadab07f437aa893f7867d328e31d7daeb2947ecc587e6a12d4|jordan"}
[2025-07-31 17:48:41] 验证码结果: {"geetest_challenge":"b0794a9937fdf0c25195d00830ea6790","geetest_validate":"edfb76c97c689dadab07f437aa893f7867d328e31d7daeb2947ecc587e6a12d4","geetest_seccode":"edfb76c97c689dadab07f437aa893f7867d328e31d7daeb2947ecc587e6a12d4|jordan"}
[2025-07-31 17:48:41] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:48:41] 请求数据: {"orderId":"P1010129058031250731174841000001","orderToken":"057bf7edf03d409d8c44f9f222afbc52","parentChannelId":"3","payChannelId":10003,"payToken":"e052f4c1a53c49dc99dc4f98ab690c2e","geetest_challenge":"b0794a9937fdf0c25195d00830ea6790","geetest_validate":"edfb76c97c689dadab07f437aa893f7867d328e31d7daeb2947ecc587e6a12d4","geetest_seccode":"edfb76c97c689dadab07f437aa893f7867d328e31d7daeb2947ecc587e6a12d4|jordan"}
[2025-07-31 17:48:41] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:48:41] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:48:41] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:48:41] 验证码格式 2 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:48:41] 尝试验证码格式 3: []
[2025-07-31 17:48:41] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:48:41] 第三步请求参数: {"orderId":"P1010129058031250731174841000001","orderToken":"057bf7edf03d409d8c44f9f222afbc52","parentChannelId":"3","payChannelId":10003,"payToken":"e052f4c1a53c49dc99dc4f98ab690c2e"}
[2025-07-31 17:48:41] 验证码结果: []
[2025-07-31 17:48:41] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:48:41] 请求数据: {"orderId":"P1010129058031250731174841000001","orderToken":"057bf7edf03d409d8c44f9f222afbc52","parentChannelId":"3","payChannelId":10003,"payToken":"e052f4c1a53c49dc99dc4f98ab690c2e"}
[2025-07-31 17:48:41] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:48:41] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:48:41] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:48:41] 验证码格式 3 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:48:41] 尝试验证码格式 4: {"picCode":"gtest","gtData":{"challenge":"b0794a9937fdf0c25195d00830ea6790","validate":"9a443f2906e6d3acf4f371cfc143c325","seccode":"9a443f2906e6d3acf4f371cfc143c325|jordan"}}
[2025-07-31 17:48:41] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:48:41] 第三步请求参数: {"orderId":"P1010129058031250731174841000001","orderToken":"057bf7edf03d409d8c44f9f222afbc52","parentChannelId":"3","payChannelId":10003,"payToken":"e052f4c1a53c49dc99dc4f98ab690c2e","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"b0794a9937fdf0c25195d00830ea6790\",\"validate\":\"9a443f2906e6d3acf4f371cfc143c325\",\"seccode\":\"9a443f2906e6d3acf4f371cfc143c325|jordan\"}}"}
[2025-07-31 17:48:41] 验证码结果: {"picCode":"gtest","gtData":{"challenge":"b0794a9937fdf0c25195d00830ea6790","validate":"9a443f2906e6d3acf4f371cfc143c325","seccode":"9a443f2906e6d3acf4f371cfc143c325|jordan"}}
[2025-07-31 17:48:41] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:48:41] 请求数据: {"orderId":"P1010129058031250731174841000001","orderToken":"057bf7edf03d409d8c44f9f222afbc52","parentChannelId":"3","payChannelId":10003,"payToken":"e052f4c1a53c49dc99dc4f98ab690c2e","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"b0794a9937fdf0c25195d00830ea6790\",\"validate\":\"9a443f2906e6d3acf4f371cfc143c325\",\"seccode\":\"9a443f2906e6d3acf4f371cfc143c325|jordan\"}}"}
[2025-07-31 17:48:41] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:48:41] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:48:41] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:48:41] 验证码格式 4 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:48:41] 即时处理失败: 所有验证码格式都已尝试，均未成功
[2025-07-31 17:48:41] 构造验证码页面URL: /sdo_browser_captcha.php?trade_no=2025073117483839176&orderToken=057bf7edf03d409d8c44f9f222afbc52&orderId=P1010129058031250731174841000001&payToken=e052f4c1a53c49dc99dc4f98ab690c2e&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT1iMDc5NGE5OTM3ZmRmMGMyNTE5NWQwMDgzMGVhNjc5MCZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiJiMDc5NGE5OTM3ZmRmMGMyNTE5NWQwMDgzMGVhNjc5MCJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-07-31 17:48:41] SDO支付流程完成，最终支付URL: Array
[2025-07-31 17:48:41] extractQrcodeFromPaymentUrl返回结果类型: array
[2025-07-31 17:48:41] 返回数组内容: {"type":"jump","url":"\/sdo_browser_captcha.php?trade_no=2025073117483839176&orderToken=057bf7edf03d409d8c44f9f222afbc52&orderId=P1010129058031250731174841000001&payToken=e052f4c1a53c49dc99dc4f98ab690c2e&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT1iMDc5NGE5OTM3ZmRmMGMyNTE5NWQwMDgzMGVhNjc5MCZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiJiMDc5NGE5OTM3ZmRmMGMyNTE5NWQwMDgzMGVhNjc5MCJ9fSwiY2FwdGNoYVR5cGUiOjR9","message":"需要完成验证码验证"}
[2025-07-31 17:48:41] 返回验证码页面，URL: /sdo_browser_captcha.php?trade_no=2025073117483839176&orderToken=057bf7edf03d409d8c44f9f222afbc52&orderId=P1010129058031250731174841000001&payToken=e052f4c1a53c49dc99dc4f98ab690c2e&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT1iMDc5NGE5OTM3ZmRmMGMyNTE5NWQwMDgzMGVhNjc5MCZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiJiMDc5NGE5OTM3ZmRmMGMyNTE5NWQwMDgzMGVhNjc5MCJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-07-31 17:49:19] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-07-31 17:49:19] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-07-31 17:49:19] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-07-31 17:49:19] 充值页面访问成功，页面长度: 27735
[2025-07-31 17:49:19] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-07-31 17:49:19] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-07-31 17:49:19] 成功提取token - itemToken: d8016bc4c6bf4fc989f80009d41513ae, captchaToken: f0395f23c3244cc888a4ce60d26367b4
[2025-07-31 17:49:19] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-07-31 17:49:19] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"f0395f23c3244cc888a4ce60d26367b4","itemToken":"d8016bc4c6bf4fc989f80009d41513ae","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-07-31 17:49:19] 开始提交充值订单
[2025-07-31 17:49:19] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-07-31 17:49:19] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"f0395f23c3244cc888a4ce60d26367b4","itemToken":"d8016bc4c6bf4fc989f80009d41513ae","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-07-31 17:49:19] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-07-31 17:49:19] 订单提交完成，响应类型: string
[2025-07-31 17:49:19] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"4c838f58f5f748ec9ece436574bb7cdc","orderId":"P1010127026168250731174920000001"}}
[2025-07-31 17:49:19] 开始处理订单响应，支付方式: alipay
[2025-07-31 17:49:19] 开始处理订单响应，响应类型: string
[2025-07-31 17:49:19] 检测到JSON响应，开始解析
[2025-07-31 17:49:19] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"4c838f58f5f748ec9ece436574bb7cdc","orderId":"P1010127026168250731174920000001"}}
[2025-07-31 17:49:19] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=4c838f58f5f748ec9ece436574bb7cdc&orderId=P1010127026168250731174920000001
[2025-07-31 17:49:19] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=4c838f58f5f748ec9ece436574bb7cdc&orderId=P1010127026168250731174920000001
[2025-07-31 17:49:19] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=4c838f58f5f748ec9ece436574bb7cdc&orderId=P1010127026168250731174920000001
[2025-07-31 17:49:19] 提取参数成功 - orderToken: 4c838f58f5f748ec9ece436574bb7cdc, orderId: P1010127026168250731174920000001
[2025-07-31 17:49:19] 步骤1: 访问支付页面获取payToken
[2025-07-31 17:49:19] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=4c838f58f5f748ec9ece436574bb7cdc&orderId=P1010127026168250731174920000001, 方法: GET
[2025-07-31 17:49:20] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-07-31 17:49:20] 成功提取payToken: 13ae4698029345e3ac0a682cfdf12049
[2025-07-31 17:49:20] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-07-31 17:49:20] 第一次请求参数: {"orderId":"P1010127026168250731174920000001","orderToken":"4c838f58f5f748ec9ece436574bb7cdc","payChannelId":"3","payToken":"13ae4698029345e3ac0a682cfdf12049","routerFlg":"1"}
[2025-07-31 17:49:20] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:49:20] 请求数据: {"orderId":"P1010127026168250731174920000001","orderToken":"4c838f58f5f748ec9ece436574bb7cdc","payChannelId":"3","payToken":"13ae4698029345e3ac0a682cfdf12049","routerFlg":"1"}
[2025-07-31 17:49:20] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-07-31 17:49:20] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-07-31 17:49:20] 第一次请求成功，获得最终payChannelId: 10003
[2025-07-31 17:49:20] 步骤3: 完成支付流程，payChannelId: 10003
[2025-07-31 17:49:20] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:49:20] 请求数据: {"orderId":"P1010127026168250731174920000001","orderToken":"4c838f58f5f748ec9ece436574bb7cdc","payChannelId":10003,"payToken":"13ae4698029345e3ac0a682cfdf12049"}
[2025-07-31 17:49:20] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-07-31 17:49:20] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=2b28279b4fb2dec5a82f75f0c6c93ba8&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"2b28279b4fb2dec5a82f75f0c6c93ba8\"}},\"captchaType\":4}"}}
[2025-07-31 17:49:20] 需要验证码，获取最新验证码参数
[2025-07-31 17:49:20] 最新验证码信息: {"captchaParams":{"gtData":{"gt":"31cc9ac8ae5eb9ef1aeaee9110bfc50c","success":1,"gt_url":"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=2b28279b4fb2dec5a82f75f0c6c93ba8&offline=false&new_captcha=true&callfunction=","new_captcha":1,"challenge":"2b28279b4fb2dec5a82f75f0c6c93ba8"}},"captchaType":4}
[2025-07-31 17:49:20] 尝试即时处理支付，跳过验证码验证
[2025-07-31 17:49:20] 使用captchaInfo中的challenge: 2b28279b4fb2dec5a82f75f0c6c93ba8
[2025-07-31 17:49:20] 尝试验证码格式 1: {"geetest_challenge":"2b28279b4fb2dec5a82f75f0c6c93ba8","geetest_validate":"cde1beaf1ad62ab45f4f7c1ad33c646b","geetest_seccode":"cde1beaf1ad62ab45f4f7c1ad33c646b|jordan"}
[2025-07-31 17:49:20] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:49:20] 第三步请求参数: {"orderId":"P1010127026168250731174920000001","orderToken":"4c838f58f5f748ec9ece436574bb7cdc","parentChannelId":"3","payChannelId":10003,"payToken":"13ae4698029345e3ac0a682cfdf12049","geetest_challenge":"2b28279b4fb2dec5a82f75f0c6c93ba8","geetest_validate":"cde1beaf1ad62ab45f4f7c1ad33c646b","geetest_seccode":"cde1beaf1ad62ab45f4f7c1ad33c646b|jordan"}
[2025-07-31 17:49:20] 验证码结果: {"geetest_challenge":"2b28279b4fb2dec5a82f75f0c6c93ba8","geetest_validate":"cde1beaf1ad62ab45f4f7c1ad33c646b","geetest_seccode":"cde1beaf1ad62ab45f4f7c1ad33c646b|jordan"}
[2025-07-31 17:49:20] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:49:20] 请求数据: {"orderId":"P1010127026168250731174920000001","orderToken":"4c838f58f5f748ec9ece436574bb7cdc","parentChannelId":"3","payChannelId":10003,"payToken":"13ae4698029345e3ac0a682cfdf12049","geetest_challenge":"2b28279b4fb2dec5a82f75f0c6c93ba8","geetest_validate":"cde1beaf1ad62ab45f4f7c1ad33c646b","geetest_seccode":"cde1beaf1ad62ab45f4f7c1ad33c646b|jordan"}
[2025-07-31 17:49:20] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:49:20] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:49:20] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:49:20] 验证码格式 1 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:49:20] 尝试验证码格式 2: {"geetest_challenge":"2b28279b4fb2dec5a82f75f0c6c93ba8","geetest_validate":"ac4c80db8d61c13f5fef7d1c82d393fa286f3026457b19c97218901db2bda5d2","geetest_seccode":"ac4c80db8d61c13f5fef7d1c82d393fa286f3026457b19c97218901db2bda5d2|jordan"}
[2025-07-31 17:49:20] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:49:20] 第三步请求参数: {"orderId":"P1010127026168250731174920000001","orderToken":"4c838f58f5f748ec9ece436574bb7cdc","parentChannelId":"3","payChannelId":10003,"payToken":"13ae4698029345e3ac0a682cfdf12049","geetest_challenge":"2b28279b4fb2dec5a82f75f0c6c93ba8","geetest_validate":"ac4c80db8d61c13f5fef7d1c82d393fa286f3026457b19c97218901db2bda5d2","geetest_seccode":"ac4c80db8d61c13f5fef7d1c82d393fa286f3026457b19c97218901db2bda5d2|jordan"}
[2025-07-31 17:49:20] 验证码结果: {"geetest_challenge":"2b28279b4fb2dec5a82f75f0c6c93ba8","geetest_validate":"ac4c80db8d61c13f5fef7d1c82d393fa286f3026457b19c97218901db2bda5d2","geetest_seccode":"ac4c80db8d61c13f5fef7d1c82d393fa286f3026457b19c97218901db2bda5d2|jordan"}
[2025-07-31 17:49:20] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:49:20] 请求数据: {"orderId":"P1010127026168250731174920000001","orderToken":"4c838f58f5f748ec9ece436574bb7cdc","parentChannelId":"3","payChannelId":10003,"payToken":"13ae4698029345e3ac0a682cfdf12049","geetest_challenge":"2b28279b4fb2dec5a82f75f0c6c93ba8","geetest_validate":"ac4c80db8d61c13f5fef7d1c82d393fa286f3026457b19c97218901db2bda5d2","geetest_seccode":"ac4c80db8d61c13f5fef7d1c82d393fa286f3026457b19c97218901db2bda5d2|jordan"}
[2025-07-31 17:49:20] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:49:20] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:49:20] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:49:20] 验证码格式 2 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:49:20] 尝试验证码格式 3: []
[2025-07-31 17:49:20] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:49:20] 第三步请求参数: {"orderId":"P1010127026168250731174920000001","orderToken":"4c838f58f5f748ec9ece436574bb7cdc","parentChannelId":"3","payChannelId":10003,"payToken":"13ae4698029345e3ac0a682cfdf12049"}
[2025-07-31 17:49:20] 验证码结果: []
[2025-07-31 17:49:20] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:49:20] 请求数据: {"orderId":"P1010127026168250731174920000001","orderToken":"4c838f58f5f748ec9ece436574bb7cdc","parentChannelId":"3","payChannelId":10003,"payToken":"13ae4698029345e3ac0a682cfdf12049"}
[2025-07-31 17:49:21] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:49:21] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:49:21] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:49:21] 验证码格式 3 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:49:21] 尝试验证码格式 4: {"picCode":"gtest","gtData":{"challenge":"2b28279b4fb2dec5a82f75f0c6c93ba8","validate":"a0145ff6c514c94c6181e87925584155","seccode":"a0145ff6c514c94c6181e87925584155|jordan"}}
[2025-07-31 17:49:21] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:49:21] 第三步请求参数: {"orderId":"P1010127026168250731174920000001","orderToken":"4c838f58f5f748ec9ece436574bb7cdc","parentChannelId":"3","payChannelId":10003,"payToken":"13ae4698029345e3ac0a682cfdf12049","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"2b28279b4fb2dec5a82f75f0c6c93ba8\",\"validate\":\"a0145ff6c514c94c6181e87925584155\",\"seccode\":\"a0145ff6c514c94c6181e87925584155|jordan\"}}"}
[2025-07-31 17:49:21] 验证码结果: {"picCode":"gtest","gtData":{"challenge":"2b28279b4fb2dec5a82f75f0c6c93ba8","validate":"a0145ff6c514c94c6181e87925584155","seccode":"a0145ff6c514c94c6181e87925584155|jordan"}}
[2025-07-31 17:49:21] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:49:21] 请求数据: {"orderId":"P1010127026168250731174920000001","orderToken":"4c838f58f5f748ec9ece436574bb7cdc","parentChannelId":"3","payChannelId":10003,"payToken":"13ae4698029345e3ac0a682cfdf12049","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"2b28279b4fb2dec5a82f75f0c6c93ba8\",\"validate\":\"a0145ff6c514c94c6181e87925584155\",\"seccode\":\"a0145ff6c514c94c6181e87925584155|jordan\"}}"}
[2025-07-31 17:49:21] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:49:21] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:49:21] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:49:21] 验证码格式 4 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:49:21] 即时处理失败: 所有验证码格式都已尝试，均未成功
[2025-07-31 17:49:21] 构造验证码页面URL: /sdo_browser_captcha.php?trade_no=2025073117483839176&orderToken=4c838f58f5f748ec9ece436574bb7cdc&orderId=P1010127026168250731174920000001&payToken=13ae4698029345e3ac0a682cfdf12049&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT0yYjI4Mjc5YjRmYjJkZWM1YTgyZjc1ZjBjNmM5M2JhOCZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiIyYjI4Mjc5YjRmYjJkZWM1YTgyZjc1ZjBjNmM5M2JhOCJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-07-31 17:49:21] SDO支付流程完成，最终支付URL: Array
[2025-07-31 17:49:21] extractQrcodeFromPaymentUrl返回结果类型: array
[2025-07-31 17:49:21] 返回数组内容: {"type":"jump","url":"\/sdo_browser_captcha.php?trade_no=2025073117483839176&orderToken=4c838f58f5f748ec9ece436574bb7cdc&orderId=P1010127026168250731174920000001&payToken=13ae4698029345e3ac0a682cfdf12049&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT0yYjI4Mjc5YjRmYjJkZWM1YTgyZjc1ZjBjNmM5M2JhOCZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiIyYjI4Mjc5YjRmYjJkZWM1YTgyZjc1ZjBjNmM5M2JhOCJ9fSwiY2FwdGNoYVR5cGUiOjR9","message":"需要完成验证码验证"}
[2025-07-31 17:49:21] 返回验证码页面，URL: /sdo_browser_captcha.php?trade_no=2025073117483839176&orderToken=4c838f58f5f748ec9ece436574bb7cdc&orderId=P1010127026168250731174920000001&payToken=13ae4698029345e3ac0a682cfdf12049&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT0yYjI4Mjc5YjRmYjJkZWM1YTgyZjc1ZjBjNmM5M2JhOCZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiIyYjI4Mjc5YjRmYjJkZWM1YTgyZjc1ZjBjNmM5M2JhOCJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-07-31 17:56:10] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-07-31 17:56:10] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-07-31 17:56:10] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-07-31 17:56:10] 充值页面访问成功，页面长度: 27735
[2025-07-31 17:56:10] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-07-31 17:56:10] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-07-31 17:56:10] 成功提取token - itemToken: dba075c4966a44658ff5a92c7d201c21, captchaToken: 387bd020517d47c9953ce2ad54fe42c5
[2025-07-31 17:56:10] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-07-31 17:56:10] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"387bd020517d47c9953ce2ad54fe42c5","itemToken":"dba075c4966a44658ff5a92c7d201c21","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-07-31 17:56:10] 开始提交充值订单
[2025-07-31 17:56:10] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-07-31 17:56:10] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"387bd020517d47c9953ce2ad54fe42c5","itemToken":"dba075c4966a44658ff5a92c7d201c21","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-07-31 17:56:10] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-07-31 17:56:10] 订单提交完成，响应类型: string
[2025-07-31 17:56:10] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"47ddfac7cf3948b0b2f0b8bd99fa7864","orderId":"P1010127026170250731175611000001"}}
[2025-07-31 17:56:10] 开始处理订单响应，支付方式: alipay
[2025-07-31 17:56:10] 开始处理订单响应，响应类型: string
[2025-07-31 17:56:10] 检测到JSON响应，开始解析
[2025-07-31 17:56:10] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"47ddfac7cf3948b0b2f0b8bd99fa7864","orderId":"P1010127026170250731175611000001"}}
[2025-07-31 17:56:10] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=47ddfac7cf3948b0b2f0b8bd99fa7864&orderId=P1010127026170250731175611000001
[2025-07-31 17:56:10] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=47ddfac7cf3948b0b2f0b8bd99fa7864&orderId=P1010127026170250731175611000001
[2025-07-31 17:56:10] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=47ddfac7cf3948b0b2f0b8bd99fa7864&orderId=P1010127026170250731175611000001
[2025-07-31 17:56:10] 提取参数成功 - orderToken: 47ddfac7cf3948b0b2f0b8bd99fa7864, orderId: P1010127026170250731175611000001
[2025-07-31 17:56:10] 步骤1: 访问支付页面获取payToken
[2025-07-31 17:56:10] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=47ddfac7cf3948b0b2f0b8bd99fa7864&orderId=P1010127026170250731175611000001, 方法: GET
[2025-07-31 17:56:10] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-07-31 17:56:10] 成功提取payToken: f71761f1a271436982cbaf85d5a426df
[2025-07-31 17:56:10] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-07-31 17:56:10] 第一次请求参数: {"orderId":"P1010127026170250731175611000001","orderToken":"47ddfac7cf3948b0b2f0b8bd99fa7864","payChannelId":"3","payToken":"f71761f1a271436982cbaf85d5a426df","routerFlg":"1"}
[2025-07-31 17:56:10] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:56:10] 请求数据: {"orderId":"P1010127026170250731175611000001","orderToken":"47ddfac7cf3948b0b2f0b8bd99fa7864","payChannelId":"3","payToken":"f71761f1a271436982cbaf85d5a426df","routerFlg":"1"}
[2025-07-31 17:56:10] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-07-31 17:56:10] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-07-31 17:56:10] 第一次请求成功，获得最终payChannelId: 10003
[2025-07-31 17:56:10] 步骤3: 完成支付流程，payChannelId: 10003
[2025-07-31 17:56:10] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:56:10] 请求数据: {"orderId":"P1010127026170250731175611000001","orderToken":"47ddfac7cf3948b0b2f0b8bd99fa7864","payChannelId":10003,"payToken":"f71761f1a271436982cbaf85d5a426df"}
[2025-07-31 17:56:11] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-07-31 17:56:11] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=77ff09fa1585466e87683b728daccdf3&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"77ff09fa1585466e87683b728daccdf3\"}},\"captchaType\":4}"}}
[2025-07-31 17:56:11] 需要验证码，获取最新验证码参数
[2025-07-31 17:56:11] 最新验证码信息: {"captchaParams":{"gtData":{"gt":"31cc9ac8ae5eb9ef1aeaee9110bfc50c","success":1,"gt_url":"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=77ff09fa1585466e87683b728daccdf3&offline=false&new_captcha=true&callfunction=","new_captcha":1,"challenge":"77ff09fa1585466e87683b728daccdf3"}},"captchaType":4}
[2025-07-31 17:56:11] 尝试即时处理支付，跳过验证码验证
[2025-07-31 17:56:11] 使用captchaInfo中的challenge: 77ff09fa1585466e87683b728daccdf3
[2025-07-31 17:56:11] 尝试验证码格式 1: {"geetest_challenge":"77ff09fa1585466e87683b728daccdf3","geetest_validate":"e570d9343e96c68da64e4ca9a471a353","geetest_seccode":"e570d9343e96c68da64e4ca9a471a353|jordan"}
[2025-07-31 17:56:11] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:56:11] 第三步请求参数: {"orderId":"P1010127026170250731175611000001","orderToken":"47ddfac7cf3948b0b2f0b8bd99fa7864","parentChannelId":"3","payChannelId":10003,"payToken":"f71761f1a271436982cbaf85d5a426df","geetest_challenge":"77ff09fa1585466e87683b728daccdf3","geetest_validate":"e570d9343e96c68da64e4ca9a471a353","geetest_seccode":"e570d9343e96c68da64e4ca9a471a353|jordan"}
[2025-07-31 17:56:11] 验证码结果: {"geetest_challenge":"77ff09fa1585466e87683b728daccdf3","geetest_validate":"e570d9343e96c68da64e4ca9a471a353","geetest_seccode":"e570d9343e96c68da64e4ca9a471a353|jordan"}
[2025-07-31 17:56:11] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:56:11] 请求数据: {"orderId":"P1010127026170250731175611000001","orderToken":"47ddfac7cf3948b0b2f0b8bd99fa7864","parentChannelId":"3","payChannelId":10003,"payToken":"f71761f1a271436982cbaf85d5a426df","geetest_challenge":"77ff09fa1585466e87683b728daccdf3","geetest_validate":"e570d9343e96c68da64e4ca9a471a353","geetest_seccode":"e570d9343e96c68da64e4ca9a471a353|jordan"}
[2025-07-31 17:56:11] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:56:11] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:56:11] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:56:11] 验证码格式 1 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:56:11] 尝试验证码格式 2: {"geetest_challenge":"77ff09fa1585466e87683b728daccdf3","geetest_validate":"3cbf3250289dc4417326ca9949cc268286c53e685f87337a2f112bb84778a394","geetest_seccode":"3cbf3250289dc4417326ca9949cc268286c53e685f87337a2f112bb84778a394|jordan"}
[2025-07-31 17:56:11] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:56:11] 第三步请求参数: {"orderId":"P1010127026170250731175611000001","orderToken":"47ddfac7cf3948b0b2f0b8bd99fa7864","parentChannelId":"3","payChannelId":10003,"payToken":"f71761f1a271436982cbaf85d5a426df","geetest_challenge":"77ff09fa1585466e87683b728daccdf3","geetest_validate":"3cbf3250289dc4417326ca9949cc268286c53e685f87337a2f112bb84778a394","geetest_seccode":"3cbf3250289dc4417326ca9949cc268286c53e685f87337a2f112bb84778a394|jordan"}
[2025-07-31 17:56:11] 验证码结果: {"geetest_challenge":"77ff09fa1585466e87683b728daccdf3","geetest_validate":"3cbf3250289dc4417326ca9949cc268286c53e685f87337a2f112bb84778a394","geetest_seccode":"3cbf3250289dc4417326ca9949cc268286c53e685f87337a2f112bb84778a394|jordan"}
[2025-07-31 17:56:11] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:56:11] 请求数据: {"orderId":"P1010127026170250731175611000001","orderToken":"47ddfac7cf3948b0b2f0b8bd99fa7864","parentChannelId":"3","payChannelId":10003,"payToken":"f71761f1a271436982cbaf85d5a426df","geetest_challenge":"77ff09fa1585466e87683b728daccdf3","geetest_validate":"3cbf3250289dc4417326ca9949cc268286c53e685f87337a2f112bb84778a394","geetest_seccode":"3cbf3250289dc4417326ca9949cc268286c53e685f87337a2f112bb84778a394|jordan"}
[2025-07-31 17:56:11] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:56:11] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:56:11] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:56:11] 验证码格式 2 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:56:11] 尝试验证码格式 3: []
[2025-07-31 17:56:11] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:56:11] 第三步请求参数: {"orderId":"P1010127026170250731175611000001","orderToken":"47ddfac7cf3948b0b2f0b8bd99fa7864","parentChannelId":"3","payChannelId":10003,"payToken":"f71761f1a271436982cbaf85d5a426df"}
[2025-07-31 17:56:11] 验证码结果: []
[2025-07-31 17:56:11] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:56:11] 请求数据: {"orderId":"P1010127026170250731175611000001","orderToken":"47ddfac7cf3948b0b2f0b8bd99fa7864","parentChannelId":"3","payChannelId":10003,"payToken":"f71761f1a271436982cbaf85d5a426df"}
[2025-07-31 17:56:11] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:56:11] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:56:11] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:56:11] 验证码格式 3 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:56:11] 尝试验证码格式 4: {"picCode":"gtest","gtData":{"challenge":"77ff09fa1585466e87683b728daccdf3","validate":"b6b1a4cfcf0b5c72c9a6c65e97d98b9a","seccode":"b6b1a4cfcf0b5c72c9a6c65e97d98b9a|jordan"}}
[2025-07-31 17:56:11] 步骤3: 第三步请求 - 提交验证码结果
[2025-07-31 17:56:11] 第三步请求参数: {"orderId":"P1010127026170250731175611000001","orderToken":"47ddfac7cf3948b0b2f0b8bd99fa7864","parentChannelId":"3","payChannelId":10003,"payToken":"f71761f1a271436982cbaf85d5a426df","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"77ff09fa1585466e87683b728daccdf3\",\"validate\":\"b6b1a4cfcf0b5c72c9a6c65e97d98b9a\",\"seccode\":\"b6b1a4cfcf0b5c72c9a6c65e97d98b9a|jordan\"}}"}
[2025-07-31 17:56:11] 验证码结果: {"picCode":"gtest","gtData":{"challenge":"77ff09fa1585466e87683b728daccdf3","validate":"b6b1a4cfcf0b5c72c9a6c65e97d98b9a","seccode":"b6b1a4cfcf0b5c72c9a6c65e97d98b9a|jordan"}}
[2025-07-31 17:56:11] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-07-31 17:56:11] 请求数据: {"orderId":"P1010127026170250731175611000001","orderToken":"47ddfac7cf3948b0b2f0b8bd99fa7864","parentChannelId":"3","payChannelId":10003,"payToken":"f71761f1a271436982cbaf85d5a426df","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"77ff09fa1585466e87683b728daccdf3\",\"validate\":\"b6b1a4cfcf0b5c72c9a6c65e97d98b9a\",\"seccode\":\"b6b1a4cfcf0b5c72c9a6c65e97d98b9a|jordan\"}}"}
[2025-07-31 17:56:11] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-07-31 17:56:11] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-07-31 17:56:11] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:56:11] 验证码格式 4 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-07-31 17:56:11] 即时处理失败: 所有验证码格式都已尝试，均未成功
[2025-07-31 17:56:11] 构造验证码页面URL: /sdo_browser_captcha.php?trade_no=2025073117560940999&orderToken=47ddfac7cf3948b0b2f0b8bd99fa7864&orderId=P1010127026170250731175611000001&payToken=f71761f1a271436982cbaf85d5a426df&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT03N2ZmMDlmYTE1ODU0NjZlODc2ODNiNzI4ZGFjY2RmMyZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiI3N2ZmMDlmYTE1ODU0NjZlODc2ODNiNzI4ZGFjY2RmMyJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-07-31 17:56:11] SDO支付流程完成，最终支付URL: Array
[2025-07-31 17:56:11] extractQrcodeFromPaymentUrl返回结果类型: array
[2025-07-31 17:56:11] 返回数组内容: {"type":"jump","url":"\/sdo_browser_captcha.php?trade_no=2025073117560940999&orderToken=47ddfac7cf3948b0b2f0b8bd99fa7864&orderId=P1010127026170250731175611000001&payToken=f71761f1a271436982cbaf85d5a426df&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT03N2ZmMDlmYTE1ODU0NjZlODc2ODNiNzI4ZGFjY2RmMyZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiI3N2ZmMDlmYTE1ODU0NjZlODc2ODNiNzI4ZGFjY2RmMyJ9fSwiY2FwdGNoYVR5cGUiOjR9","message":"需要完成验证码验证"}
[2025-07-31 17:56:11] 返回验证码页面，URL: /sdo_browser_captcha.php?trade_no=2025073117560940999&orderToken=47ddfac7cf3948b0b2f0b8bd99fa7864&orderId=P1010127026170250731175611000001&payToken=f71761f1a271436982cbaf85d5a426df&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT03N2ZmMDlmYTE1ODU0NjZlODc2ODNiNzI4ZGFjY2RmMyZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiI3N2ZmMDlmYTE1ODU0NjZlODc2ODNiNzI4ZGFjY2RmMyJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-08-01 09:19:26] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-08-01 09:19:26] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-08-01 09:19:26] Cookie验证失败，页面包含登录提示
[2025-08-01 09:19:26] SDO支付插件错误: Cookie已过期或无效，请重新登录SDO账户
[2025-08-01 09:19:26] SDO支付插件错误堆栈: #0 D:\wwwroot\epay.com\plugins\sdopay\sdopay_plugin.php(66): sdopay_plugin::qrcode()
#1 D:\wwwroot\epay.com\includes\lib\Plugin.php(103): sdopay_plugin::alipay()
#2 D:\wwwroot\epay.com\includes\lib\Plugin.php(65): lib\Plugin::loadClass('sdopay', 'alipay', '202508010919262...')
#3 D:\wwwroot\epay.com\pay.php(20): lib\Plugin::loadForPay('alipay/20250801...')
#4 {main}
[2025-08-01 09:20:48] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-08-01 09:20:48] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-08-01 09:20:48] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-08-01 09:20:48] 充值页面访问成功，页面长度: 27735
[2025-08-01 09:20:48] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-08-01 09:20:48] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-08-01 09:20:48] 成功提取token - itemToken: 805a6388c1554e1aaabb7f7de0e07a9c, captchaToken: ce2346188ad3414498de2978fbc9c4e6
[2025-08-01 09:20:48] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-08-01 09:20:48] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"ce2346188ad3414498de2978fbc9c4e6","itemToken":"805a6388c1554e1aaabb7f7de0e07a9c","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 09:20:48] 开始提交充值订单
[2025-08-01 09:20:48] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-08-01 09:20:48] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"ce2346188ad3414498de2978fbc9c4e6","itemToken":"805a6388c1554e1aaabb7f7de0e07a9c","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 09:20:48] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-08-01 09:20:48] 订单提交完成，响应类型: string
[2025-08-01 09:20:48] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"c7c71588ce864c4bad4401953bfc21b5","orderId":"P1010127026169250801092048000001"}}
[2025-08-01 09:20:48] 开始处理订单响应，支付方式: alipay
[2025-08-01 09:20:48] 开始处理订单响应，响应类型: string
[2025-08-01 09:20:48] 检测到JSON响应，开始解析
[2025-08-01 09:20:48] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"c7c71588ce864c4bad4401953bfc21b5","orderId":"P1010127026169250801092048000001"}}
[2025-08-01 09:20:48] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=c7c71588ce864c4bad4401953bfc21b5&orderId=P1010127026169250801092048000001
[2025-08-01 09:20:48] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=c7c71588ce864c4bad4401953bfc21b5&orderId=P1010127026169250801092048000001
[2025-08-01 09:20:48] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=c7c71588ce864c4bad4401953bfc21b5&orderId=P1010127026169250801092048000001
[2025-08-01 09:20:48] 提取参数成功 - orderToken: c7c71588ce864c4bad4401953bfc21b5, orderId: P1010127026169250801092048000001
[2025-08-01 09:20:48] 步骤1: 访问支付页面获取payToken
[2025-08-01 09:20:48] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=c7c71588ce864c4bad4401953bfc21b5&orderId=P1010127026169250801092048000001, 方法: GET
[2025-08-01 09:20:49] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-08-01 09:20:49] 成功提取payToken: bef3db7a11334ce68ae098ce2dfb1988
[2025-08-01 09:20:49] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-08-01 09:20:49] 第一次请求参数: {"orderId":"P1010127026169250801092048000001","orderToken":"c7c71588ce864c4bad4401953bfc21b5","payChannelId":"3","payToken":"bef3db7a11334ce68ae098ce2dfb1988","routerFlg":"1"}
[2025-08-01 09:20:49] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 09:20:49] 请求数据: {"orderId":"P1010127026169250801092048000001","orderToken":"c7c71588ce864c4bad4401953bfc21b5","payChannelId":"3","payToken":"bef3db7a11334ce68ae098ce2dfb1988","routerFlg":"1"}
[2025-08-01 09:20:49] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-08-01 09:20:49] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-08-01 09:20:49] 第一次请求成功，获得最终payChannelId: 10003
[2025-08-01 09:20:49] 步骤3: 完成支付流程，payChannelId: 10003
[2025-08-01 09:20:49] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 09:20:49] 请求数据: {"orderId":"P1010127026169250801092048000001","orderToken":"c7c71588ce864c4bad4401953bfc21b5","payChannelId":10003,"payToken":"bef3db7a11334ce68ae098ce2dfb1988"}
[2025-08-01 09:20:49] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-08-01 09:20:49] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=c99e74ab43bd24049e5cc6981905cde2&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"c99e74ab43bd24049e5cc6981905cde2\"}},\"captchaType\":4}"}}
[2025-08-01 09:20:49] 需要验证码，获取最新验证码参数
[2025-08-01 09:20:49] 最新验证码信息: {"captchaParams":{"gtData":{"gt":"31cc9ac8ae5eb9ef1aeaee9110bfc50c","success":1,"gt_url":"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=c99e74ab43bd24049e5cc6981905cde2&offline=false&new_captcha=true&callfunction=","new_captcha":1,"challenge":"c99e74ab43bd24049e5cc6981905cde2"}},"captchaType":4}
[2025-08-01 09:20:49] 尝试即时处理支付，跳过验证码验证
[2025-08-01 09:20:49] 使用captchaInfo中的challenge: c99e74ab43bd24049e5cc6981905cde2
[2025-08-01 09:20:49] 尝试验证码格式 1: {"geetest_challenge":"c99e74ab43bd24049e5cc6981905cde2","geetest_validate":"89b6738a28bed7755f83f6edb060d1fa","geetest_seccode":"89b6738a28bed7755f83f6edb060d1fa|jordan"}
[2025-08-01 09:20:49] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 09:20:49] 第三步请求参数: {"orderId":"P1010127026169250801092048000001","orderToken":"c7c71588ce864c4bad4401953bfc21b5","parentChannelId":"3","payChannelId":10003,"payToken":"bef3db7a11334ce68ae098ce2dfb1988","geetest_challenge":"c99e74ab43bd24049e5cc6981905cde2","geetest_validate":"89b6738a28bed7755f83f6edb060d1fa","geetest_seccode":"89b6738a28bed7755f83f6edb060d1fa|jordan"}
[2025-08-01 09:20:49] 验证码结果: {"geetest_challenge":"c99e74ab43bd24049e5cc6981905cde2","geetest_validate":"89b6738a28bed7755f83f6edb060d1fa","geetest_seccode":"89b6738a28bed7755f83f6edb060d1fa|jordan"}
[2025-08-01 09:20:49] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 09:20:49] 请求数据: {"orderId":"P1010127026169250801092048000001","orderToken":"c7c71588ce864c4bad4401953bfc21b5","parentChannelId":"3","payChannelId":10003,"payToken":"bef3db7a11334ce68ae098ce2dfb1988","geetest_challenge":"c99e74ab43bd24049e5cc6981905cde2","geetest_validate":"89b6738a28bed7755f83f6edb060d1fa","geetest_seccode":"89b6738a28bed7755f83f6edb060d1fa|jordan"}
[2025-08-01 09:20:49] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 09:20:49] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 09:20:49] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:20:49] 验证码格式 1 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:20:49] 尝试验证码格式 2: {"geetest_challenge":"c99e74ab43bd24049e5cc6981905cde2","geetest_validate":"9bd1c495fef3854fcce3765bf7675b61b82fb886ab68ff0264b69fa2755480d9","geetest_seccode":"9bd1c495fef3854fcce3765bf7675b61b82fb886ab68ff0264b69fa2755480d9|jordan"}
[2025-08-01 09:20:49] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 09:20:49] 第三步请求参数: {"orderId":"P1010127026169250801092048000001","orderToken":"c7c71588ce864c4bad4401953bfc21b5","parentChannelId":"3","payChannelId":10003,"payToken":"bef3db7a11334ce68ae098ce2dfb1988","geetest_challenge":"c99e74ab43bd24049e5cc6981905cde2","geetest_validate":"9bd1c495fef3854fcce3765bf7675b61b82fb886ab68ff0264b69fa2755480d9","geetest_seccode":"9bd1c495fef3854fcce3765bf7675b61b82fb886ab68ff0264b69fa2755480d9|jordan"}
[2025-08-01 09:20:49] 验证码结果: {"geetest_challenge":"c99e74ab43bd24049e5cc6981905cde2","geetest_validate":"9bd1c495fef3854fcce3765bf7675b61b82fb886ab68ff0264b69fa2755480d9","geetest_seccode":"9bd1c495fef3854fcce3765bf7675b61b82fb886ab68ff0264b69fa2755480d9|jordan"}
[2025-08-01 09:20:49] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 09:20:49] 请求数据: {"orderId":"P1010127026169250801092048000001","orderToken":"c7c71588ce864c4bad4401953bfc21b5","parentChannelId":"3","payChannelId":10003,"payToken":"bef3db7a11334ce68ae098ce2dfb1988","geetest_challenge":"c99e74ab43bd24049e5cc6981905cde2","geetest_validate":"9bd1c495fef3854fcce3765bf7675b61b82fb886ab68ff0264b69fa2755480d9","geetest_seccode":"9bd1c495fef3854fcce3765bf7675b61b82fb886ab68ff0264b69fa2755480d9|jordan"}
[2025-08-01 09:20:49] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 09:20:49] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 09:20:49] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:20:49] 验证码格式 2 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:20:49] 尝试验证码格式 3: []
[2025-08-01 09:20:49] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 09:20:49] 第三步请求参数: {"orderId":"P1010127026169250801092048000001","orderToken":"c7c71588ce864c4bad4401953bfc21b5","parentChannelId":"3","payChannelId":10003,"payToken":"bef3db7a11334ce68ae098ce2dfb1988"}
[2025-08-01 09:20:49] 验证码结果: []
[2025-08-01 09:20:49] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 09:20:49] 请求数据: {"orderId":"P1010127026169250801092048000001","orderToken":"c7c71588ce864c4bad4401953bfc21b5","parentChannelId":"3","payChannelId":10003,"payToken":"bef3db7a11334ce68ae098ce2dfb1988"}
[2025-08-01 09:20:50] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 09:20:50] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 09:20:50] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:20:50] 验证码格式 3 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:20:50] 尝试验证码格式 4: {"picCode":"gtest","gtData":{"challenge":"c99e74ab43bd24049e5cc6981905cde2","validate":"5d749e79cd8c42a846e2e7064b85d41e","seccode":"5d749e79cd8c42a846e2e7064b85d41e|jordan"}}
[2025-08-01 09:20:50] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 09:20:50] 第三步请求参数: {"orderId":"P1010127026169250801092048000001","orderToken":"c7c71588ce864c4bad4401953bfc21b5","parentChannelId":"3","payChannelId":10003,"payToken":"bef3db7a11334ce68ae098ce2dfb1988","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"c99e74ab43bd24049e5cc6981905cde2\",\"validate\":\"5d749e79cd8c42a846e2e7064b85d41e\",\"seccode\":\"5d749e79cd8c42a846e2e7064b85d41e|jordan\"}}"}
[2025-08-01 09:20:50] 验证码结果: {"picCode":"gtest","gtData":{"challenge":"c99e74ab43bd24049e5cc6981905cde2","validate":"5d749e79cd8c42a846e2e7064b85d41e","seccode":"5d749e79cd8c42a846e2e7064b85d41e|jordan"}}
[2025-08-01 09:20:50] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 09:20:50] 请求数据: {"orderId":"P1010127026169250801092048000001","orderToken":"c7c71588ce864c4bad4401953bfc21b5","parentChannelId":"3","payChannelId":10003,"payToken":"bef3db7a11334ce68ae098ce2dfb1988","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"c99e74ab43bd24049e5cc6981905cde2\",\"validate\":\"5d749e79cd8c42a846e2e7064b85d41e\",\"seccode\":\"5d749e79cd8c42a846e2e7064b85d41e|jordan\"}}"}
[2025-08-01 09:20:50] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 09:20:50] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 09:20:50] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:20:50] 验证码格式 4 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:20:50] 即时处理失败: 所有验证码格式都已尝试，均未成功
[2025-08-01 09:20:50] 构造验证码页面URL: /sdo_browser_captcha.php?trade_no=2025080109204744347&orderToken=c7c71588ce864c4bad4401953bfc21b5&orderId=P1010127026169250801092048000001&payToken=bef3db7a11334ce68ae098ce2dfb1988&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT1jOTllNzRhYjQzYmQyNDA0OWU1Y2M2OTgxOTA1Y2RlMiZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiJjOTllNzRhYjQzYmQyNDA0OWU1Y2M2OTgxOTA1Y2RlMiJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-08-01 09:20:50] SDO支付流程完成，最终支付URL: Array
[2025-08-01 09:20:50] extractQrcodeFromPaymentUrl返回结果类型: array
[2025-08-01 09:20:50] 返回数组内容: {"type":"jump","url":"\/sdo_browser_captcha.php?trade_no=2025080109204744347&orderToken=c7c71588ce864c4bad4401953bfc21b5&orderId=P1010127026169250801092048000001&payToken=bef3db7a11334ce68ae098ce2dfb1988&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT1jOTllNzRhYjQzYmQyNDA0OWU1Y2M2OTgxOTA1Y2RlMiZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiJjOTllNzRhYjQzYmQyNDA0OWU1Y2M2OTgxOTA1Y2RlMiJ9fSwiY2FwdGNoYVR5cGUiOjR9","message":"需要完成验证码验证"}
[2025-08-01 09:20:50] 返回验证码页面，URL: /sdo_browser_captcha.php?trade_no=2025080109204744347&orderToken=c7c71588ce864c4bad4401953bfc21b5&orderId=P1010127026169250801092048000001&payToken=bef3db7a11334ce68ae098ce2dfb1988&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT1jOTllNzRhYjQzYmQyNDA0OWU1Y2M2OTgxOTA1Y2RlMiZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiJjOTllNzRhYjQzYmQyNDA0OWU1Y2M2OTgxOTA1Y2RlMiJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-08-01 09:31:59] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-08-01 09:31:59] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-08-01 09:31:59] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-08-01 09:31:59] 充值页面访问成功，页面长度: 27735
[2025-08-01 09:31:59] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-08-01 09:31:59] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-08-01 09:31:59] 成功提取token - itemToken: 0c009c613e914d6c96bb5049dcc88ad3, captchaToken: aeb4ab80e17743479212b96c45d59b81
[2025-08-01 09:31:59] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-08-01 09:31:59] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"aeb4ab80e17743479212b96c45d59b81","itemToken":"0c009c613e914d6c96bb5049dcc88ad3","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 09:31:59] 开始提交充值订单
[2025-08-01 09:31:59] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-08-01 09:31:59] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"aeb4ab80e17743479212b96c45d59b81","itemToken":"0c009c613e914d6c96bb5049dcc88ad3","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 09:31:59] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-08-01 09:31:59] 订单提交完成，响应类型: string
[2025-08-01 09:31:59] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"6b5d0cd94192470dbac6807028d3ab50","orderId":"P1010127026169250801093159000001"}}
[2025-08-01 09:31:59] 开始处理订单响应，支付方式: alipay
[2025-08-01 09:31:59] 开始处理订单响应，响应类型: string
[2025-08-01 09:31:59] 检测到JSON响应，开始解析
[2025-08-01 09:31:59] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"6b5d0cd94192470dbac6807028d3ab50","orderId":"P1010127026169250801093159000001"}}
[2025-08-01 09:31:59] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=6b5d0cd94192470dbac6807028d3ab50&orderId=P1010127026169250801093159000001
[2025-08-01 09:31:59] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=6b5d0cd94192470dbac6807028d3ab50&orderId=P1010127026169250801093159000001
[2025-08-01 09:31:59] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=6b5d0cd94192470dbac6807028d3ab50&orderId=P1010127026169250801093159000001
[2025-08-01 09:31:59] 提取参数成功 - orderToken: 6b5d0cd94192470dbac6807028d3ab50, orderId: P1010127026169250801093159000001
[2025-08-01 09:31:59] 步骤1: 访问支付页面获取payToken
[2025-08-01 09:31:59] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=6b5d0cd94192470dbac6807028d3ab50&orderId=P1010127026169250801093159000001, 方法: GET
[2025-08-01 09:32:00] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-08-01 09:32:00] 成功提取payToken: 79676ca2d8d74e148dd41c82e168a0cd
[2025-08-01 09:32:00] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-08-01 09:32:00] 第一次请求参数: {"orderId":"P1010127026169250801093159000001","orderToken":"6b5d0cd94192470dbac6807028d3ab50","payChannelId":"3","payToken":"79676ca2d8d74e148dd41c82e168a0cd","routerFlg":"1"}
[2025-08-01 09:32:00] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 09:32:00] 请求数据: {"orderId":"P1010127026169250801093159000001","orderToken":"6b5d0cd94192470dbac6807028d3ab50","payChannelId":"3","payToken":"79676ca2d8d74e148dd41c82e168a0cd","routerFlg":"1"}
[2025-08-01 09:32:00] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-08-01 09:32:00] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-08-01 09:32:00] 第一次请求成功，获得最终payChannelId: 10003
[2025-08-01 09:32:00] 步骤3: 完成支付流程，payChannelId: 10003
[2025-08-01 09:32:00] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 09:32:00] 请求数据: {"orderId":"P1010127026169250801093159000001","orderToken":"6b5d0cd94192470dbac6807028d3ab50","payChannelId":10003,"payToken":"79676ca2d8d74e148dd41c82e168a0cd"}
[2025-08-01 09:32:00] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-08-01 09:32:00] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=939fdbb7f89fb33a014118d4444369c1&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"939fdbb7f89fb33a014118d4444369c1\"}},\"captchaType\":4}"}}
[2025-08-01 09:32:00] 需要验证码，获取最新验证码参数
[2025-08-01 09:32:00] 最新验证码信息: {"captchaParams":{"gtData":{"gt":"31cc9ac8ae5eb9ef1aeaee9110bfc50c","success":1,"gt_url":"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=939fdbb7f89fb33a014118d4444369c1&offline=false&new_captcha=true&callfunction=","new_captcha":1,"challenge":"939fdbb7f89fb33a014118d4444369c1"}},"captchaType":4}
[2025-08-01 09:32:00] 尝试即时处理支付，跳过验证码验证
[2025-08-01 09:32:00] 使用captchaInfo中的challenge: 939fdbb7f89fb33a014118d4444369c1
[2025-08-01 09:32:00] 尝试验证码格式 1: {"geetest_challenge":"939fdbb7f89fb33a014118d4444369c1","geetest_validate":"72ca1a2ce794ca79699ae2b005ab060c","geetest_seccode":"72ca1a2ce794ca79699ae2b005ab060c|jordan"}
[2025-08-01 09:32:00] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 09:32:00] 第三步请求参数: {"orderId":"P1010127026169250801093159000001","orderToken":"6b5d0cd94192470dbac6807028d3ab50","parentChannelId":"3","payChannelId":10003,"payToken":"79676ca2d8d74e148dd41c82e168a0cd","geetest_challenge":"939fdbb7f89fb33a014118d4444369c1","geetest_validate":"72ca1a2ce794ca79699ae2b005ab060c","geetest_seccode":"72ca1a2ce794ca79699ae2b005ab060c|jordan"}
[2025-08-01 09:32:00] 验证码结果: {"geetest_challenge":"939fdbb7f89fb33a014118d4444369c1","geetest_validate":"72ca1a2ce794ca79699ae2b005ab060c","geetest_seccode":"72ca1a2ce794ca79699ae2b005ab060c|jordan"}
[2025-08-01 09:32:00] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 09:32:00] 请求数据: {"orderId":"P1010127026169250801093159000001","orderToken":"6b5d0cd94192470dbac6807028d3ab50","parentChannelId":"3","payChannelId":10003,"payToken":"79676ca2d8d74e148dd41c82e168a0cd","geetest_challenge":"939fdbb7f89fb33a014118d4444369c1","geetest_validate":"72ca1a2ce794ca79699ae2b005ab060c","geetest_seccode":"72ca1a2ce794ca79699ae2b005ab060c|jordan"}
[2025-08-01 09:32:00] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 09:32:00] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 09:32:00] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:32:00] 验证码格式 1 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:32:00] 尝试验证码格式 2: {"geetest_challenge":"939fdbb7f89fb33a014118d4444369c1","geetest_validate":"b8d02d6209af220d010e6feb94f90fe33b744e550387bd3f2608fd784179a8ac","geetest_seccode":"b8d02d6209af220d010e6feb94f90fe33b744e550387bd3f2608fd784179a8ac|jordan"}
[2025-08-01 09:32:00] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 09:32:00] 第三步请求参数: {"orderId":"P1010127026169250801093159000001","orderToken":"6b5d0cd94192470dbac6807028d3ab50","parentChannelId":"3","payChannelId":10003,"payToken":"79676ca2d8d74e148dd41c82e168a0cd","geetest_challenge":"939fdbb7f89fb33a014118d4444369c1","geetest_validate":"b8d02d6209af220d010e6feb94f90fe33b744e550387bd3f2608fd784179a8ac","geetest_seccode":"b8d02d6209af220d010e6feb94f90fe33b744e550387bd3f2608fd784179a8ac|jordan"}
[2025-08-01 09:32:00] 验证码结果: {"geetest_challenge":"939fdbb7f89fb33a014118d4444369c1","geetest_validate":"b8d02d6209af220d010e6feb94f90fe33b744e550387bd3f2608fd784179a8ac","geetest_seccode":"b8d02d6209af220d010e6feb94f90fe33b744e550387bd3f2608fd784179a8ac|jordan"}
[2025-08-01 09:32:00] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 09:32:00] 请求数据: {"orderId":"P1010127026169250801093159000001","orderToken":"6b5d0cd94192470dbac6807028d3ab50","parentChannelId":"3","payChannelId":10003,"payToken":"79676ca2d8d74e148dd41c82e168a0cd","geetest_challenge":"939fdbb7f89fb33a014118d4444369c1","geetest_validate":"b8d02d6209af220d010e6feb94f90fe33b744e550387bd3f2608fd784179a8ac","geetest_seccode":"b8d02d6209af220d010e6feb94f90fe33b744e550387bd3f2608fd784179a8ac|jordan"}
[2025-08-01 09:32:01] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 09:32:01] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 09:32:01] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:32:01] 验证码格式 2 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:32:01] 尝试验证码格式 3: []
[2025-08-01 09:32:01] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 09:32:01] 第三步请求参数: {"orderId":"P1010127026169250801093159000001","orderToken":"6b5d0cd94192470dbac6807028d3ab50","parentChannelId":"3","payChannelId":10003,"payToken":"79676ca2d8d74e148dd41c82e168a0cd"}
[2025-08-01 09:32:01] 验证码结果: []
[2025-08-01 09:32:01] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 09:32:01] 请求数据: {"orderId":"P1010127026169250801093159000001","orderToken":"6b5d0cd94192470dbac6807028d3ab50","parentChannelId":"3","payChannelId":10003,"payToken":"79676ca2d8d74e148dd41c82e168a0cd"}
[2025-08-01 09:32:01] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 09:32:01] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 09:32:01] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:32:01] 验证码格式 3 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:32:01] 尝试验证码格式 4: {"picCode":"gtest","gtData":{"challenge":"939fdbb7f89fb33a014118d4444369c1","validate":"23d3bb287e14821b3c0fd75f8673934d","seccode":"23d3bb287e14821b3c0fd75f8673934d|jordan"}}
[2025-08-01 09:32:01] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 09:32:01] 第三步请求参数: {"orderId":"P1010127026169250801093159000001","orderToken":"6b5d0cd94192470dbac6807028d3ab50","parentChannelId":"3","payChannelId":10003,"payToken":"79676ca2d8d74e148dd41c82e168a0cd","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"939fdbb7f89fb33a014118d4444369c1\",\"validate\":\"23d3bb287e14821b3c0fd75f8673934d\",\"seccode\":\"23d3bb287e14821b3c0fd75f8673934d|jordan\"}}"}
[2025-08-01 09:32:01] 验证码结果: {"picCode":"gtest","gtData":{"challenge":"939fdbb7f89fb33a014118d4444369c1","validate":"23d3bb287e14821b3c0fd75f8673934d","seccode":"23d3bb287e14821b3c0fd75f8673934d|jordan"}}
[2025-08-01 09:32:01] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 09:32:01] 请求数据: {"orderId":"P1010127026169250801093159000001","orderToken":"6b5d0cd94192470dbac6807028d3ab50","parentChannelId":"3","payChannelId":10003,"payToken":"79676ca2d8d74e148dd41c82e168a0cd","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"939fdbb7f89fb33a014118d4444369c1\",\"validate\":\"23d3bb287e14821b3c0fd75f8673934d\",\"seccode\":\"23d3bb287e14821b3c0fd75f8673934d|jordan\"}}"}
[2025-08-01 09:32:01] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 09:32:01] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 09:32:01] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:32:01] 验证码格式 4 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:32:01] 即时处理失败: 所有验证码格式都已尝试，均未成功
[2025-08-01 09:32:01] 构造验证码页面URL: /sdo_browser_captcha.php?trade_no=2025080109315821891&orderToken=6b5d0cd94192470dbac6807028d3ab50&orderId=P1010127026169250801093159000001&payToken=79676ca2d8d74e148dd41c82e168a0cd&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT05MzlmZGJiN2Y4OWZiMzNhMDE0MTE4ZDQ0NDQzNjljMSZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiI5MzlmZGJiN2Y4OWZiMzNhMDE0MTE4ZDQ0NDQzNjljMSJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-08-01 09:32:01] SDO支付流程完成，最终支付URL: Array
[2025-08-01 09:32:01] extractQrcodeFromPaymentUrl返回结果类型: array
[2025-08-01 09:32:01] 返回数组内容: {"type":"jump","url":"\/sdo_browser_captcha.php?trade_no=2025080109315821891&orderToken=6b5d0cd94192470dbac6807028d3ab50&orderId=P1010127026169250801093159000001&payToken=79676ca2d8d74e148dd41c82e168a0cd&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT05MzlmZGJiN2Y4OWZiMzNhMDE0MTE4ZDQ0NDQzNjljMSZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiI5MzlmZGJiN2Y4OWZiMzNhMDE0MTE4ZDQ0NDQzNjljMSJ9fSwiY2FwdGNoYVR5cGUiOjR9","message":"需要完成验证码验证"}
[2025-08-01 09:32:01] 返回验证码页面，URL: /sdo_browser_captcha.php?trade_no=2025080109315821891&orderToken=6b5d0cd94192470dbac6807028d3ab50&orderId=P1010127026169250801093159000001&payToken=79676ca2d8d74e148dd41c82e168a0cd&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT05MzlmZGJiN2Y4OWZiMzNhMDE0MTE4ZDQ0NDQzNjljMSZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiI5MzlmZGJiN2Y4OWZiMzNhMDE0MTE4ZDQ0NDQzNjljMSJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-08-01 09:36:13] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-08-01 09:36:13] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-08-01 09:36:13] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-08-01 09:36:13] 充值页面访问成功，页面长度: 27735
[2025-08-01 09:36:13] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-08-01 09:36:13] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-08-01 09:36:13] 成功提取token - itemToken: 346bfcd794ce48f6b448e0ad168a665b, captchaToken: 9d13e66d633f4b3c85d900ad7caf8f71
[2025-08-01 09:36:13] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-08-01 09:36:13] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"9d13e66d633f4b3c85d900ad7caf8f71","itemToken":"346bfcd794ce48f6b448e0ad168a665b","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 09:36:13] 开始提交充值订单
[2025-08-01 09:36:13] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-08-01 09:36:13] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"9d13e66d633f4b3c85d900ad7caf8f71","itemToken":"346bfcd794ce48f6b448e0ad168a665b","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 09:36:14] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-08-01 09:36:14] 订单提交完成，响应类型: string
[2025-08-01 09:36:14] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"a4932dde7c8d40a8bd81fe7bb5cbb704","orderId":"P1010129058032250801093614000001"}}
[2025-08-01 09:36:14] 开始处理订单响应，支付方式: alipay
[2025-08-01 09:36:14] 开始处理订单响应，响应类型: string
[2025-08-01 09:36:14] 检测到JSON响应，开始解析
[2025-08-01 09:36:14] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"a4932dde7c8d40a8bd81fe7bb5cbb704","orderId":"P1010129058032250801093614000001"}}
[2025-08-01 09:36:14] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=a4932dde7c8d40a8bd81fe7bb5cbb704&orderId=P1010129058032250801093614000001
[2025-08-01 09:36:14] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=a4932dde7c8d40a8bd81fe7bb5cbb704&orderId=P1010129058032250801093614000001
[2025-08-01 09:36:14] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=a4932dde7c8d40a8bd81fe7bb5cbb704&orderId=P1010129058032250801093614000001
[2025-08-01 09:36:14] 提取参数成功 - orderToken: a4932dde7c8d40a8bd81fe7bb5cbb704, orderId: P1010129058032250801093614000001
[2025-08-01 09:36:14] 步骤1: 访问支付页面获取payToken
[2025-08-01 09:36:14] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=a4932dde7c8d40a8bd81fe7bb5cbb704&orderId=P1010129058032250801093614000001, 方法: GET
[2025-08-01 09:36:14] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-08-01 09:36:14] 成功提取payToken: 2e917939cdd044cb8a904f7bbd216617
[2025-08-01 09:36:14] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-08-01 09:36:14] 第一次请求参数: {"orderId":"P1010129058032250801093614000001","orderToken":"a4932dde7c8d40a8bd81fe7bb5cbb704","payChannelId":"3","payToken":"2e917939cdd044cb8a904f7bbd216617","routerFlg":"1"}
[2025-08-01 09:36:14] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 09:36:14] 请求数据: {"orderId":"P1010129058032250801093614000001","orderToken":"a4932dde7c8d40a8bd81fe7bb5cbb704","payChannelId":"3","payToken":"2e917939cdd044cb8a904f7bbd216617","routerFlg":"1"}
[2025-08-01 09:36:14] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-08-01 09:36:14] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-08-01 09:36:14] 第一次请求成功，获得最终payChannelId: 10003
[2025-08-01 09:36:14] 步骤3: 完成支付流程，payChannelId: 10003
[2025-08-01 09:36:14] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 09:36:14] 请求数据: {"orderId":"P1010129058032250801093614000001","orderToken":"a4932dde7c8d40a8bd81fe7bb5cbb704","payChannelId":10003,"payToken":"2e917939cdd044cb8a904f7bbd216617"}
[2025-08-01 09:36:14] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-08-01 09:36:14] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=5f747015cd388bdf3b41475a3163fbc2&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"5f747015cd388bdf3b41475a3163fbc2\"}},\"captchaType\":4}"}}
[2025-08-01 09:36:14] 需要验证码，获取最新验证码参数
[2025-08-01 09:36:14] 最新验证码信息: {"captchaParams":{"gtData":{"gt":"31cc9ac8ae5eb9ef1aeaee9110bfc50c","success":1,"gt_url":"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=5f747015cd388bdf3b41475a3163fbc2&offline=false&new_captcha=true&callfunction=","new_captcha":1,"challenge":"5f747015cd388bdf3b41475a3163fbc2"}},"captchaType":4}
[2025-08-01 09:36:14] 尝试即时处理支付，跳过验证码验证
[2025-08-01 09:36:14] 使用captchaInfo中的challenge: 5f747015cd388bdf3b41475a3163fbc2
[2025-08-01 09:36:14] 尝试验证码格式 1: {"geetest_challenge":"5f747015cd388bdf3b41475a3163fbc2","geetest_validate":"6712b4675460530a2a74d138f7c71472","geetest_seccode":"6712b4675460530a2a74d138f7c71472|jordan"}
[2025-08-01 09:36:14] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 09:36:14] 第三步请求参数: {"orderId":"P1010129058032250801093614000001","orderToken":"a4932dde7c8d40a8bd81fe7bb5cbb704","parentChannelId":"3","payChannelId":10003,"payToken":"2e917939cdd044cb8a904f7bbd216617","geetest_challenge":"5f747015cd388bdf3b41475a3163fbc2","geetest_validate":"6712b4675460530a2a74d138f7c71472","geetest_seccode":"6712b4675460530a2a74d138f7c71472|jordan"}
[2025-08-01 09:36:14] 验证码结果: {"geetest_challenge":"5f747015cd388bdf3b41475a3163fbc2","geetest_validate":"6712b4675460530a2a74d138f7c71472","geetest_seccode":"6712b4675460530a2a74d138f7c71472|jordan"}
[2025-08-01 09:36:14] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 09:36:14] 请求数据: {"orderId":"P1010129058032250801093614000001","orderToken":"a4932dde7c8d40a8bd81fe7bb5cbb704","parentChannelId":"3","payChannelId":10003,"payToken":"2e917939cdd044cb8a904f7bbd216617","geetest_challenge":"5f747015cd388bdf3b41475a3163fbc2","geetest_validate":"6712b4675460530a2a74d138f7c71472","geetest_seccode":"6712b4675460530a2a74d138f7c71472|jordan"}
[2025-08-01 09:36:15] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 09:36:15] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 09:36:15] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:36:15] 验证码格式 1 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:36:15] 尝试验证码格式 2: {"geetest_challenge":"5f747015cd388bdf3b41475a3163fbc2","geetest_validate":"ab5f986941889caaa96c776c6ec8589755c37b4b56e419089ded9c6dfc9992d3","geetest_seccode":"ab5f986941889caaa96c776c6ec8589755c37b4b56e419089ded9c6dfc9992d3|jordan"}
[2025-08-01 09:36:15] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 09:36:15] 第三步请求参数: {"orderId":"P1010129058032250801093614000001","orderToken":"a4932dde7c8d40a8bd81fe7bb5cbb704","parentChannelId":"3","payChannelId":10003,"payToken":"2e917939cdd044cb8a904f7bbd216617","geetest_challenge":"5f747015cd388bdf3b41475a3163fbc2","geetest_validate":"ab5f986941889caaa96c776c6ec8589755c37b4b56e419089ded9c6dfc9992d3","geetest_seccode":"ab5f986941889caaa96c776c6ec8589755c37b4b56e419089ded9c6dfc9992d3|jordan"}
[2025-08-01 09:36:15] 验证码结果: {"geetest_challenge":"5f747015cd388bdf3b41475a3163fbc2","geetest_validate":"ab5f986941889caaa96c776c6ec8589755c37b4b56e419089ded9c6dfc9992d3","geetest_seccode":"ab5f986941889caaa96c776c6ec8589755c37b4b56e419089ded9c6dfc9992d3|jordan"}
[2025-08-01 09:36:15] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 09:36:15] 请求数据: {"orderId":"P1010129058032250801093614000001","orderToken":"a4932dde7c8d40a8bd81fe7bb5cbb704","parentChannelId":"3","payChannelId":10003,"payToken":"2e917939cdd044cb8a904f7bbd216617","geetest_challenge":"5f747015cd388bdf3b41475a3163fbc2","geetest_validate":"ab5f986941889caaa96c776c6ec8589755c37b4b56e419089ded9c6dfc9992d3","geetest_seccode":"ab5f986941889caaa96c776c6ec8589755c37b4b56e419089ded9c6dfc9992d3|jordan"}
[2025-08-01 09:36:15] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 09:36:15] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 09:36:15] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:36:15] 验证码格式 2 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:36:15] 尝试验证码格式 3: []
[2025-08-01 09:36:15] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 09:36:15] 第三步请求参数: {"orderId":"P1010129058032250801093614000001","orderToken":"a4932dde7c8d40a8bd81fe7bb5cbb704","parentChannelId":"3","payChannelId":10003,"payToken":"2e917939cdd044cb8a904f7bbd216617"}
[2025-08-01 09:36:15] 验证码结果: []
[2025-08-01 09:36:15] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 09:36:15] 请求数据: {"orderId":"P1010129058032250801093614000001","orderToken":"a4932dde7c8d40a8bd81fe7bb5cbb704","parentChannelId":"3","payChannelId":10003,"payToken":"2e917939cdd044cb8a904f7bbd216617"}
[2025-08-01 09:36:15] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 09:36:15] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 09:36:15] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:36:15] 验证码格式 3 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:36:15] 尝试验证码格式 4: {"picCode":"gtest","gtData":{"challenge":"5f747015cd388bdf3b41475a3163fbc2","validate":"5eedac6e512a0ed5b86398da0b6c08bc","seccode":"5eedac6e512a0ed5b86398da0b6c08bc|jordan"}}
[2025-08-01 09:36:15] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 09:36:15] 第三步请求参数: {"orderId":"P1010129058032250801093614000001","orderToken":"a4932dde7c8d40a8bd81fe7bb5cbb704","parentChannelId":"3","payChannelId":10003,"payToken":"2e917939cdd044cb8a904f7bbd216617","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"5f747015cd388bdf3b41475a3163fbc2\",\"validate\":\"5eedac6e512a0ed5b86398da0b6c08bc\",\"seccode\":\"5eedac6e512a0ed5b86398da0b6c08bc|jordan\"}}"}
[2025-08-01 09:36:15] 验证码结果: {"picCode":"gtest","gtData":{"challenge":"5f747015cd388bdf3b41475a3163fbc2","validate":"5eedac6e512a0ed5b86398da0b6c08bc","seccode":"5eedac6e512a0ed5b86398da0b6c08bc|jordan"}}
[2025-08-01 09:36:15] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 09:36:15] 请求数据: {"orderId":"P1010129058032250801093614000001","orderToken":"a4932dde7c8d40a8bd81fe7bb5cbb704","parentChannelId":"3","payChannelId":10003,"payToken":"2e917939cdd044cb8a904f7bbd216617","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"5f747015cd388bdf3b41475a3163fbc2\",\"validate\":\"5eedac6e512a0ed5b86398da0b6c08bc\",\"seccode\":\"5eedac6e512a0ed5b86398da0b6c08bc|jordan\"}}"}
[2025-08-01 09:36:15] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 09:36:15] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 09:36:15] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:36:15] 验证码格式 4 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 09:36:15] 即时处理失败: 所有验证码格式都已尝试，均未成功
[2025-08-01 09:36:15] 构造验证码页面URL: /sdo_browser_captcha.php?trade_no=2025080109361376816&orderToken=a4932dde7c8d40a8bd81fe7bb5cbb704&orderId=P1010129058032250801093614000001&payToken=2e917939cdd044cb8a904f7bbd216617&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT01Zjc0NzAxNWNkMzg4YmRmM2I0MTQ3NWEzMTYzZmJjMiZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiI1Zjc0NzAxNWNkMzg4YmRmM2I0MTQ3NWEzMTYzZmJjMiJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-08-01 09:36:15] SDO支付流程完成，最终支付URL: Array
[2025-08-01 09:36:15] extractQrcodeFromPaymentUrl返回结果类型: array
[2025-08-01 09:36:15] 返回数组内容: {"type":"jump","url":"\/sdo_browser_captcha.php?trade_no=2025080109361376816&orderToken=a4932dde7c8d40a8bd81fe7bb5cbb704&orderId=P1010129058032250801093614000001&payToken=2e917939cdd044cb8a904f7bbd216617&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT01Zjc0NzAxNWNkMzg4YmRmM2I0MTQ3NWEzMTYzZmJjMiZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiI1Zjc0NzAxNWNkMzg4YmRmM2I0MTQ3NWEzMTYzZmJjMiJ9fSwiY2FwdGNoYVR5cGUiOjR9","message":"需要完成验证码验证"}
[2025-08-01 09:36:15] 返回验证码页面，URL: /sdo_browser_captcha.php?trade_no=2025080109361376816&orderToken=a4932dde7c8d40a8bd81fe7bb5cbb704&orderId=P1010129058032250801093614000001&payToken=2e917939cdd044cb8a904f7bbd216617&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT01Zjc0NzAxNWNkMzg4YmRmM2I0MTQ3NWEzMTYzZmJjMiZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiI1Zjc0NzAxNWNkMzg4YmRmM2I0MTQ3NWEzMTYzZmJjMiJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-08-01 10:17:04] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-08-01 10:17:04] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-08-01 10:17:04] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-08-01 10:17:04] 充值页面访问成功，页面长度: 27735
[2025-08-01 10:17:04] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-08-01 10:17:04] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-08-01 10:17:04] 成功提取token - itemToken: 1319da4722e14c86813f24536698fc8d, captchaToken: c473c3590419449f8190d683545ae306
[2025-08-01 10:17:04] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-08-01 10:17:04] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"c473c3590419449f8190d683545ae306","itemToken":"1319da4722e14c86813f24536698fc8d","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 10:17:04] 开始提交充值订单
[2025-08-01 10:17:04] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-08-01 10:17:04] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"c473c3590419449f8190d683545ae306","itemToken":"1319da4722e14c86813f24536698fc8d","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 10:17:05] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-08-01 10:17:05] 订单提交完成，响应类型: string
[2025-08-01 10:17:05] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"52da74da0aea443da1371072f631ed2b","orderId":"P1010127026168250801101705000001"}}
[2025-08-01 10:17:05] 开始处理订单响应，支付方式: alipay
[2025-08-01 10:17:05] 开始处理订单响应，响应类型: string
[2025-08-01 10:17:05] 检测到JSON响应，开始解析
[2025-08-01 10:17:05] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"52da74da0aea443da1371072f631ed2b","orderId":"P1010127026168250801101705000001"}}
[2025-08-01 10:17:05] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=52da74da0aea443da1371072f631ed2b&orderId=P1010127026168250801101705000001
[2025-08-01 10:17:05] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=52da74da0aea443da1371072f631ed2b&orderId=P1010127026168250801101705000001
[2025-08-01 10:17:05] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=52da74da0aea443da1371072f631ed2b&orderId=P1010127026168250801101705000001
[2025-08-01 10:17:05] 提取参数成功 - orderToken: 52da74da0aea443da1371072f631ed2b, orderId: P1010127026168250801101705000001
[2025-08-01 10:17:05] 步骤1: 访问支付页面获取payToken
[2025-08-01 10:17:05] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=52da74da0aea443da1371072f631ed2b&orderId=P1010127026168250801101705000001, 方法: GET
[2025-08-01 10:17:05] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-08-01 10:17:05] 成功提取payToken: 2318b52cb2164e33995090085b3ca6d6
[2025-08-01 10:17:05] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-08-01 10:17:05] 第一次请求参数: {"orderId":"P1010127026168250801101705000001","orderToken":"52da74da0aea443da1371072f631ed2b","payChannelId":"3","payToken":"2318b52cb2164e33995090085b3ca6d6","routerFlg":"1"}
[2025-08-01 10:17:05] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 10:17:05] 请求数据: {"orderId":"P1010127026168250801101705000001","orderToken":"52da74da0aea443da1371072f631ed2b","payChannelId":"3","payToken":"2318b52cb2164e33995090085b3ca6d6","routerFlg":"1"}
[2025-08-01 10:17:05] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-08-01 10:17:05] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-08-01 10:17:05] 第一次请求成功，获得最终payChannelId: 10003
[2025-08-01 10:17:05] 步骤3: 完成支付流程，payChannelId: 10003
[2025-08-01 10:17:05] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 10:17:05] 请求数据: {"orderId":"P1010127026168250801101705000001","orderToken":"52da74da0aea443da1371072f631ed2b","payChannelId":10003,"payToken":"2318b52cb2164e33995090085b3ca6d6"}
[2025-08-01 10:17:06] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-08-01 10:17:06] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=ac6d6f32d3113392e24096595417650d&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"ac6d6f32d3113392e24096595417650d\"}},\"captchaType\":4}"}}
[2025-08-01 10:17:06] 需要验证码，获取最新验证码参数
[2025-08-01 10:17:06] 最新验证码信息: {"captchaParams":{"gtData":{"gt":"31cc9ac8ae5eb9ef1aeaee9110bfc50c","success":1,"gt_url":"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=ac6d6f32d3113392e24096595417650d&offline=false&new_captcha=true&callfunction=","new_captcha":1,"challenge":"ac6d6f32d3113392e24096595417650d"}},"captchaType":4}
[2025-08-01 10:17:06] 尝试即时处理支付，跳过验证码验证
[2025-08-01 10:17:06] 使用captchaInfo中的challenge: ac6d6f32d3113392e24096595417650d
[2025-08-01 10:17:06] 尝试验证码格式 1: {"geetest_challenge":"ac6d6f32d3113392e24096595417650d","geetest_validate":"48a1005cea196cddccd9b67e2fecaae1","geetest_seccode":"48a1005cea196cddccd9b67e2fecaae1|jordan"}
[2025-08-01 10:17:06] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 10:17:06] 第三步请求参数: {"orderId":"P1010127026168250801101705000001","orderToken":"52da74da0aea443da1371072f631ed2b","parentChannelId":"3","payChannelId":10003,"payToken":"2318b52cb2164e33995090085b3ca6d6","geetest_challenge":"ac6d6f32d3113392e24096595417650d","geetest_validate":"48a1005cea196cddccd9b67e2fecaae1","geetest_seccode":"48a1005cea196cddccd9b67e2fecaae1|jordan"}
[2025-08-01 10:17:06] 验证码结果: {"geetest_challenge":"ac6d6f32d3113392e24096595417650d","geetest_validate":"48a1005cea196cddccd9b67e2fecaae1","geetest_seccode":"48a1005cea196cddccd9b67e2fecaae1|jordan"}
[2025-08-01 10:17:06] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 10:17:06] 请求数据: {"orderId":"P1010127026168250801101705000001","orderToken":"52da74da0aea443da1371072f631ed2b","parentChannelId":"3","payChannelId":10003,"payToken":"2318b52cb2164e33995090085b3ca6d6","geetest_challenge":"ac6d6f32d3113392e24096595417650d","geetest_validate":"48a1005cea196cddccd9b67e2fecaae1","geetest_seccode":"48a1005cea196cddccd9b67e2fecaae1|jordan"}
[2025-08-01 10:17:06] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 10:17:06] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 10:17:06] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 10:17:06] 验证码格式 1 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 10:17:06] 尝试验证码格式 2: {"geetest_challenge":"ac6d6f32d3113392e24096595417650d","geetest_validate":"a8d75202d9f27eb0ef96dc6b81b6f2e2f391c7789cc62096cb517d375b8f9d30","geetest_seccode":"a8d75202d9f27eb0ef96dc6b81b6f2e2f391c7789cc62096cb517d375b8f9d30|jordan"}
[2025-08-01 10:17:06] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 10:17:06] 第三步请求参数: {"orderId":"P1010127026168250801101705000001","orderToken":"52da74da0aea443da1371072f631ed2b","parentChannelId":"3","payChannelId":10003,"payToken":"2318b52cb2164e33995090085b3ca6d6","geetest_challenge":"ac6d6f32d3113392e24096595417650d","geetest_validate":"a8d75202d9f27eb0ef96dc6b81b6f2e2f391c7789cc62096cb517d375b8f9d30","geetest_seccode":"a8d75202d9f27eb0ef96dc6b81b6f2e2f391c7789cc62096cb517d375b8f9d30|jordan"}
[2025-08-01 10:17:06] 验证码结果: {"geetest_challenge":"ac6d6f32d3113392e24096595417650d","geetest_validate":"a8d75202d9f27eb0ef96dc6b81b6f2e2f391c7789cc62096cb517d375b8f9d30","geetest_seccode":"a8d75202d9f27eb0ef96dc6b81b6f2e2f391c7789cc62096cb517d375b8f9d30|jordan"}
[2025-08-01 10:17:06] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 10:17:06] 请求数据: {"orderId":"P1010127026168250801101705000001","orderToken":"52da74da0aea443da1371072f631ed2b","parentChannelId":"3","payChannelId":10003,"payToken":"2318b52cb2164e33995090085b3ca6d6","geetest_challenge":"ac6d6f32d3113392e24096595417650d","geetest_validate":"a8d75202d9f27eb0ef96dc6b81b6f2e2f391c7789cc62096cb517d375b8f9d30","geetest_seccode":"a8d75202d9f27eb0ef96dc6b81b6f2e2f391c7789cc62096cb517d375b8f9d30|jordan"}
[2025-08-01 10:17:06] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 10:17:06] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 10:17:06] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 10:17:06] 验证码格式 2 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 10:17:06] 尝试验证码格式 3: []
[2025-08-01 10:17:06] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 10:17:06] 第三步请求参数: {"orderId":"P1010127026168250801101705000001","orderToken":"52da74da0aea443da1371072f631ed2b","parentChannelId":"3","payChannelId":10003,"payToken":"2318b52cb2164e33995090085b3ca6d6"}
[2025-08-01 10:17:06] 验证码结果: []
[2025-08-01 10:17:06] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 10:17:06] 请求数据: {"orderId":"P1010127026168250801101705000001","orderToken":"52da74da0aea443da1371072f631ed2b","parentChannelId":"3","payChannelId":10003,"payToken":"2318b52cb2164e33995090085b3ca6d6"}
[2025-08-01 10:17:06] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 10:17:07] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 10:17:07] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 10:17:07] 验证码格式 3 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 10:17:07] 尝试验证码格式 4: {"picCode":"gtest","gtData":{"challenge":"ac6d6f32d3113392e24096595417650d","validate":"9b510c3991d3508890b462a01f6d1eb9","seccode":"9b510c3991d3508890b462a01f6d1eb9|jordan"}}
[2025-08-01 10:17:07] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 10:17:07] 第三步请求参数: {"orderId":"P1010127026168250801101705000001","orderToken":"52da74da0aea443da1371072f631ed2b","parentChannelId":"3","payChannelId":10003,"payToken":"2318b52cb2164e33995090085b3ca6d6","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"ac6d6f32d3113392e24096595417650d\",\"validate\":\"9b510c3991d3508890b462a01f6d1eb9\",\"seccode\":\"9b510c3991d3508890b462a01f6d1eb9|jordan\"}}"}
[2025-08-01 10:17:07] 验证码结果: {"picCode":"gtest","gtData":{"challenge":"ac6d6f32d3113392e24096595417650d","validate":"9b510c3991d3508890b462a01f6d1eb9","seccode":"9b510c3991d3508890b462a01f6d1eb9|jordan"}}
[2025-08-01 10:17:07] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 10:17:07] 请求数据: {"orderId":"P1010127026168250801101705000001","orderToken":"52da74da0aea443da1371072f631ed2b","parentChannelId":"3","payChannelId":10003,"payToken":"2318b52cb2164e33995090085b3ca6d6","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"ac6d6f32d3113392e24096595417650d\",\"validate\":\"9b510c3991d3508890b462a01f6d1eb9\",\"seccode\":\"9b510c3991d3508890b462a01f6d1eb9|jordan\"}}"}
[2025-08-01 10:17:07] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 10:17:07] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 10:17:07] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 10:17:07] 验证码格式 4 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 10:17:07] 即时处理失败: 所有验证码格式都已尝试，均未成功
[2025-08-01 10:17:07] 构造验证码页面URL: /sdo_browser_captcha.php?trade_no=2025080110170338154&orderToken=52da74da0aea443da1371072f631ed2b&orderId=P1010127026168250801101705000001&payToken=2318b52cb2164e33995090085b3ca6d6&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT1hYzZkNmYzMmQzMTEzMzkyZTI0MDk2NTk1NDE3NjUwZCZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiJhYzZkNmYzMmQzMTEzMzkyZTI0MDk2NTk1NDE3NjUwZCJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-08-01 10:17:07] SDO支付流程完成，最终支付URL: Array
[2025-08-01 10:17:07] extractQrcodeFromPaymentUrl返回结果类型: array
[2025-08-01 10:17:07] 返回数组内容: {"type":"jump","url":"\/sdo_browser_captcha.php?trade_no=2025080110170338154&orderToken=52da74da0aea443da1371072f631ed2b&orderId=P1010127026168250801101705000001&payToken=2318b52cb2164e33995090085b3ca6d6&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT1hYzZkNmYzMmQzMTEzMzkyZTI0MDk2NTk1NDE3NjUwZCZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiJhYzZkNmYzMmQzMTEzMzkyZTI0MDk2NTk1NDE3NjUwZCJ9fSwiY2FwdGNoYVR5cGUiOjR9","message":"需要完成验证码验证"}
[2025-08-01 10:17:07] 返回验证码页面，URL: /sdo_browser_captcha.php?trade_no=2025080110170338154&orderToken=52da74da0aea443da1371072f631ed2b&orderId=P1010127026168250801101705000001&payToken=2318b52cb2164e33995090085b3ca6d6&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT1hYzZkNmYzMmQzMTEzMzkyZTI0MDk2NTk1NDE3NjUwZCZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiJhYzZkNmYzMmQzMTEzMzkyZTI0MDk2NTk1NDE3NjUwZCJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-08-01 11:21:34] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-08-01 11:21:34] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-08-01 11:21:34] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-08-01 11:21:34] 充值页面访问成功，页面长度: 27735
[2025-08-01 11:21:34] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-08-01 11:21:34] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-08-01 11:21:34] 成功提取token - itemToken: b2db16e6d0814d77ac40ae717e9274a9, captchaToken: bcc542c920034a09853a192ff987ee0b
[2025-08-01 11:21:34] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-08-01 11:21:34] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"bcc542c920034a09853a192ff987ee0b","itemToken":"b2db16e6d0814d77ac40ae717e9274a9","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 11:21:34] 开始提交充值订单
[2025-08-01 11:21:34] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-08-01 11:21:34] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"bcc542c920034a09853a192ff987ee0b","itemToken":"b2db16e6d0814d77ac40ae717e9274a9","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 11:21:34] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-08-01 11:21:34] 订单提交完成，响应类型: string
[2025-08-01 11:21:34] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"386303374e7843c697b671f9ce4051b4","orderId":"P1010129058031250801112135000001"}}
[2025-08-01 11:21:34] 开始处理订单响应，支付方式: alipay
[2025-08-01 11:21:34] 开始处理订单响应，响应类型: string
[2025-08-01 11:21:34] 检测到JSON响应，开始解析
[2025-08-01 11:21:34] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"386303374e7843c697b671f9ce4051b4","orderId":"P1010129058031250801112135000001"}}
[2025-08-01 11:21:34] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=386303374e7843c697b671f9ce4051b4&orderId=P1010129058031250801112135000001
[2025-08-01 11:21:34] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=386303374e7843c697b671f9ce4051b4&orderId=P1010129058031250801112135000001
[2025-08-01 11:21:34] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=386303374e7843c697b671f9ce4051b4&orderId=P1010129058031250801112135000001
[2025-08-01 11:21:34] 提取参数成功 - orderToken: 386303374e7843c697b671f9ce4051b4, orderId: P1010129058031250801112135000001
[2025-08-01 11:21:34] 步骤1: 访问支付页面获取payToken
[2025-08-01 11:21:34] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=386303374e7843c697b671f9ce4051b4&orderId=P1010129058031250801112135000001, 方法: GET
[2025-08-01 11:21:35] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-08-01 11:21:35] 成功提取payToken: 690e2f01faa74803b5f14a22c92c2b2f
[2025-08-01 11:21:35] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-08-01 11:21:35] 第一次请求参数: {"orderId":"P1010129058031250801112135000001","orderToken":"386303374e7843c697b671f9ce4051b4","payChannelId":"3","payToken":"690e2f01faa74803b5f14a22c92c2b2f","routerFlg":"1"}
[2025-08-01 11:21:35] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 11:21:35] 请求数据: {"orderId":"P1010129058031250801112135000001","orderToken":"386303374e7843c697b671f9ce4051b4","payChannelId":"3","payToken":"690e2f01faa74803b5f14a22c92c2b2f","routerFlg":"1"}
[2025-08-01 11:21:35] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-08-01 11:21:35] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-08-01 11:21:35] 第一次请求成功，获得最终payChannelId: 10003
[2025-08-01 11:21:35] 步骤3: 完成支付流程，payChannelId: 10003
[2025-08-01 11:21:35] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 11:21:35] 请求数据: {"orderId":"P1010129058031250801112135000001","orderToken":"386303374e7843c697b671f9ce4051b4","payChannelId":10003,"payToken":"690e2f01faa74803b5f14a22c92c2b2f"}
[2025-08-01 11:21:35] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-08-01 11:21:35] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=cd93e7ede7cfe2d744c46f1e8356ec3c&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"cd93e7ede7cfe2d744c46f1e8356ec3c\"}},\"captchaType\":4}"}}
[2025-08-01 11:21:35] 需要验证码，获取最新验证码参数
[2025-08-01 11:21:35] 最新验证码信息: {"captchaParams":{"gtData":{"gt":"31cc9ac8ae5eb9ef1aeaee9110bfc50c","success":1,"gt_url":"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=cd93e7ede7cfe2d744c46f1e8356ec3c&offline=false&new_captcha=true&callfunction=","new_captcha":1,"challenge":"cd93e7ede7cfe2d744c46f1e8356ec3c"}},"captchaType":4}
[2025-08-01 11:21:35] 尝试即时处理支付，跳过验证码验证
[2025-08-01 11:21:35] 使用captchaInfo中的challenge: cd93e7ede7cfe2d744c46f1e8356ec3c
[2025-08-01 11:21:35] 尝试验证码格式 1: {"geetest_challenge":"cd93e7ede7cfe2d744c46f1e8356ec3c","geetest_validate":"a48d81627cfae6f555de67f8665f7d6a","geetest_seccode":"a48d81627cfae6f555de67f8665f7d6a|jordan"}
[2025-08-01 11:21:35] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 11:21:35] 第三步请求参数: {"orderId":"P1010129058031250801112135000001","orderToken":"386303374e7843c697b671f9ce4051b4","parentChannelId":"3","payChannelId":10003,"payToken":"690e2f01faa74803b5f14a22c92c2b2f","geetest_challenge":"cd93e7ede7cfe2d744c46f1e8356ec3c","geetest_validate":"a48d81627cfae6f555de67f8665f7d6a","geetest_seccode":"a48d81627cfae6f555de67f8665f7d6a|jordan"}
[2025-08-01 11:21:35] 验证码结果: {"geetest_challenge":"cd93e7ede7cfe2d744c46f1e8356ec3c","geetest_validate":"a48d81627cfae6f555de67f8665f7d6a","geetest_seccode":"a48d81627cfae6f555de67f8665f7d6a|jordan"}
[2025-08-01 11:21:35] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 11:21:35] 请求数据: {"orderId":"P1010129058031250801112135000001","orderToken":"386303374e7843c697b671f9ce4051b4","parentChannelId":"3","payChannelId":10003,"payToken":"690e2f01faa74803b5f14a22c92c2b2f","geetest_challenge":"cd93e7ede7cfe2d744c46f1e8356ec3c","geetest_validate":"a48d81627cfae6f555de67f8665f7d6a","geetest_seccode":"a48d81627cfae6f555de67f8665f7d6a|jordan"}
[2025-08-01 11:21:35] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 11:21:35] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 11:21:35] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 11:21:35] 验证码格式 1 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 11:21:35] 尝试验证码格式 2: {"geetest_challenge":"cd93e7ede7cfe2d744c46f1e8356ec3c","geetest_validate":"2ab8ce3d485baad20708a04929569582c9e69ef9626d6b84b5648f2e40edd4ba","geetest_seccode":"2ab8ce3d485baad20708a04929569582c9e69ef9626d6b84b5648f2e40edd4ba|jordan"}
[2025-08-01 11:21:35] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 11:21:35] 第三步请求参数: {"orderId":"P1010129058031250801112135000001","orderToken":"386303374e7843c697b671f9ce4051b4","parentChannelId":"3","payChannelId":10003,"payToken":"690e2f01faa74803b5f14a22c92c2b2f","geetest_challenge":"cd93e7ede7cfe2d744c46f1e8356ec3c","geetest_validate":"2ab8ce3d485baad20708a04929569582c9e69ef9626d6b84b5648f2e40edd4ba","geetest_seccode":"2ab8ce3d485baad20708a04929569582c9e69ef9626d6b84b5648f2e40edd4ba|jordan"}
[2025-08-01 11:21:35] 验证码结果: {"geetest_challenge":"cd93e7ede7cfe2d744c46f1e8356ec3c","geetest_validate":"2ab8ce3d485baad20708a04929569582c9e69ef9626d6b84b5648f2e40edd4ba","geetest_seccode":"2ab8ce3d485baad20708a04929569582c9e69ef9626d6b84b5648f2e40edd4ba|jordan"}
[2025-08-01 11:21:35] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 11:21:35] 请求数据: {"orderId":"P1010129058031250801112135000001","orderToken":"386303374e7843c697b671f9ce4051b4","parentChannelId":"3","payChannelId":10003,"payToken":"690e2f01faa74803b5f14a22c92c2b2f","geetest_challenge":"cd93e7ede7cfe2d744c46f1e8356ec3c","geetest_validate":"2ab8ce3d485baad20708a04929569582c9e69ef9626d6b84b5648f2e40edd4ba","geetest_seccode":"2ab8ce3d485baad20708a04929569582c9e69ef9626d6b84b5648f2e40edd4ba|jordan"}
[2025-08-01 11:21:36] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 11:21:36] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 11:21:36] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 11:21:36] 验证码格式 2 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 11:21:36] 尝试验证码格式 3: []
[2025-08-01 11:21:36] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 11:21:36] 第三步请求参数: {"orderId":"P1010129058031250801112135000001","orderToken":"386303374e7843c697b671f9ce4051b4","parentChannelId":"3","payChannelId":10003,"payToken":"690e2f01faa74803b5f14a22c92c2b2f"}
[2025-08-01 11:21:36] 验证码结果: []
[2025-08-01 11:21:36] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 11:21:36] 请求数据: {"orderId":"P1010129058031250801112135000001","orderToken":"386303374e7843c697b671f9ce4051b4","parentChannelId":"3","payChannelId":10003,"payToken":"690e2f01faa74803b5f14a22c92c2b2f"}
[2025-08-01 11:21:36] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 11:21:36] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 11:21:36] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 11:21:36] 验证码格式 3 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 11:21:36] 尝试验证码格式 4: {"picCode":"gtest","gtData":{"challenge":"cd93e7ede7cfe2d744c46f1e8356ec3c","validate":"7693bdc466f5f0cdb239e567380f9eb9","seccode":"7693bdc466f5f0cdb239e567380f9eb9|jordan"}}
[2025-08-01 11:21:36] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 11:21:36] 第三步请求参数: {"orderId":"P1010129058031250801112135000001","orderToken":"386303374e7843c697b671f9ce4051b4","parentChannelId":"3","payChannelId":10003,"payToken":"690e2f01faa74803b5f14a22c92c2b2f","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"cd93e7ede7cfe2d744c46f1e8356ec3c\",\"validate\":\"7693bdc466f5f0cdb239e567380f9eb9\",\"seccode\":\"7693bdc466f5f0cdb239e567380f9eb9|jordan\"}}"}
[2025-08-01 11:21:36] 验证码结果: {"picCode":"gtest","gtData":{"challenge":"cd93e7ede7cfe2d744c46f1e8356ec3c","validate":"7693bdc466f5f0cdb239e567380f9eb9","seccode":"7693bdc466f5f0cdb239e567380f9eb9|jordan"}}
[2025-08-01 11:21:36] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 11:21:36] 请求数据: {"orderId":"P1010129058031250801112135000001","orderToken":"386303374e7843c697b671f9ce4051b4","parentChannelId":"3","payChannelId":10003,"payToken":"690e2f01faa74803b5f14a22c92c2b2f","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"cd93e7ede7cfe2d744c46f1e8356ec3c\",\"validate\":\"7693bdc466f5f0cdb239e567380f9eb9\",\"seccode\":\"7693bdc466f5f0cdb239e567380f9eb9|jordan\"}}"}
[2025-08-01 11:21:36] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 11:21:36] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 11:21:36] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 11:21:36] 验证码格式 4 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 11:21:36] 即时处理失败: 所有验证码格式都已尝试，均未成功
[2025-08-01 11:21:36] 构造验证码页面URL: /sdo_browser_captcha.php?trade_no=2025080111213377714&orderToken=386303374e7843c697b671f9ce4051b4&orderId=P1010129058031250801112135000001&payToken=690e2f01faa74803b5f14a22c92c2b2f&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT1jZDkzZTdlZGU3Y2ZlMmQ3NDRjNDZmMWU4MzU2ZWMzYyZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiJjZDkzZTdlZGU3Y2ZlMmQ3NDRjNDZmMWU4MzU2ZWMzYyJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-08-01 11:21:36] SDO支付流程完成，最终支付URL: Array
[2025-08-01 11:21:36] extractQrcodeFromPaymentUrl返回结果类型: array
[2025-08-01 11:21:36] 返回数组内容: {"type":"jump","url":"\/sdo_browser_captcha.php?trade_no=2025080111213377714&orderToken=386303374e7843c697b671f9ce4051b4&orderId=P1010129058031250801112135000001&payToken=690e2f01faa74803b5f14a22c92c2b2f&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT1jZDkzZTdlZGU3Y2ZlMmQ3NDRjNDZmMWU4MzU2ZWMzYyZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiJjZDkzZTdlZGU3Y2ZlMmQ3NDRjNDZmMWU4MzU2ZWMzYyJ9fSwiY2FwdGNoYVR5cGUiOjR9","message":"需要完成验证码验证"}
[2025-08-01 11:21:36] 返回验证码页面，URL: /sdo_browser_captcha.php?trade_no=2025080111213377714&orderToken=386303374e7843c697b671f9ce4051b4&orderId=P1010129058031250801112135000001&payToken=690e2f01faa74803b5f14a22c92c2b2f&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT1jZDkzZTdlZGU3Y2ZlMmQ3NDRjNDZmMWU4MzU2ZWMzYyZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiJjZDkzZTdlZGU3Y2ZlMmQ3NDRjNDZmMWU4MzU2ZWMzYyJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-08-01 13:58:40] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-08-01 13:58:40] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-08-01 13:58:40] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-08-01 13:58:40] 充值页面访问成功，页面长度: 27735
[2025-08-01 13:58:40] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-08-01 13:58:40] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-08-01 13:58:40] 成功提取token - itemToken: 7cc46fe93a4a4c50aa255aac08f054da, captchaToken: 26e1351e101f4ae8ab7ea38ef7bed5c6
[2025-08-01 13:58:40] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-08-01 13:58:40] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"26e1351e101f4ae8ab7ea38ef7bed5c6","itemToken":"7cc46fe93a4a4c50aa255aac08f054da","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 13:58:40] 开始提交充值订单
[2025-08-01 13:58:40] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-08-01 13:58:40] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"26e1351e101f4ae8ab7ea38ef7bed5c6","itemToken":"7cc46fe93a4a4c50aa255aac08f054da","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 13:58:40] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-08-01 13:58:41] 订单提交完成，响应类型: string
[2025-08-01 13:58:41] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"b3bea9ab24aa458db1ad6723d78e720b","orderId":"P1010129058032250801135841000001"}}
[2025-08-01 13:58:41] 开始处理订单响应，支付方式: alipay
[2025-08-01 13:58:41] 开始处理订单响应，响应类型: string
[2025-08-01 13:58:41] 检测到JSON响应，开始解析
[2025-08-01 13:58:41] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"b3bea9ab24aa458db1ad6723d78e720b","orderId":"P1010129058032250801135841000001"}}
[2025-08-01 13:58:41] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=b3bea9ab24aa458db1ad6723d78e720b&orderId=P1010129058032250801135841000001
[2025-08-01 13:58:41] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=b3bea9ab24aa458db1ad6723d78e720b&orderId=P1010129058032250801135841000001
[2025-08-01 13:58:41] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=b3bea9ab24aa458db1ad6723d78e720b&orderId=P1010129058032250801135841000001
[2025-08-01 13:58:41] 提取参数成功 - orderToken: b3bea9ab24aa458db1ad6723d78e720b, orderId: P1010129058032250801135841000001
[2025-08-01 13:58:41] 步骤1: 访问支付页面获取payToken
[2025-08-01 13:58:41] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=b3bea9ab24aa458db1ad6723d78e720b&orderId=P1010129058032250801135841000001, 方法: GET
[2025-08-01 13:58:41] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-08-01 13:58:41] 成功提取payToken: 65df6549078f44a880ab295956cf1b1a
[2025-08-01 13:58:41] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-08-01 13:58:41] 第一次请求参数: {"orderId":"P1010129058032250801135841000001","orderToken":"b3bea9ab24aa458db1ad6723d78e720b","payChannelId":"3","payToken":"65df6549078f44a880ab295956cf1b1a","routerFlg":"1"}
[2025-08-01 13:58:41] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 13:58:41] 请求数据: {"orderId":"P1010129058032250801135841000001","orderToken":"b3bea9ab24aa458db1ad6723d78e720b","payChannelId":"3","payToken":"65df6549078f44a880ab295956cf1b1a","routerFlg":"1"}
[2025-08-01 13:58:41] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-08-01 13:58:41] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-08-01 13:58:41] 第一次请求成功，获得最终payChannelId: 10003
[2025-08-01 13:58:41] 步骤3: 完成支付流程，payChannelId: 10003
[2025-08-01 13:58:41] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 13:58:41] 请求数据: {"orderId":"P1010129058032250801135841000001","orderToken":"b3bea9ab24aa458db1ad6723d78e720b","payChannelId":10003,"payToken":"65df6549078f44a880ab295956cf1b1a"}
[2025-08-01 13:58:41] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-08-01 13:58:41] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=1d70b971a45e0d2aa7d33c44be375496&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"1d70b971a45e0d2aa7d33c44be375496\"}},\"captchaType\":4}"}}
[2025-08-01 13:58:41] 需要验证码，获取最新验证码参数
[2025-08-01 13:58:41] 最新验证码信息: {"captchaParams":{"gtData":{"gt":"31cc9ac8ae5eb9ef1aeaee9110bfc50c","success":1,"gt_url":"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=1d70b971a45e0d2aa7d33c44be375496&offline=false&new_captcha=true&callfunction=","new_captcha":1,"challenge":"1d70b971a45e0d2aa7d33c44be375496"}},"captchaType":4}
[2025-08-01 13:58:41] 尝试即时处理支付，跳过验证码验证
[2025-08-01 13:58:41] 使用captchaInfo中的challenge: 1d70b971a45e0d2aa7d33c44be375496
[2025-08-01 13:58:41] 尝试验证码格式 1: {"geetest_challenge":"1d70b971a45e0d2aa7d33c44be375496","geetest_validate":"ade01a2c2c5bf985b71992658c08f83f","geetest_seccode":"ade01a2c2c5bf985b71992658c08f83f|jordan"}
[2025-08-01 13:58:41] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 13:58:41] 第三步请求参数: {"orderId":"P1010129058032250801135841000001","orderToken":"b3bea9ab24aa458db1ad6723d78e720b","parentChannelId":"3","payChannelId":10003,"payToken":"65df6549078f44a880ab295956cf1b1a","geetest_challenge":"1d70b971a45e0d2aa7d33c44be375496","geetest_validate":"ade01a2c2c5bf985b71992658c08f83f","geetest_seccode":"ade01a2c2c5bf985b71992658c08f83f|jordan"}
[2025-08-01 13:58:41] 验证码结果: {"geetest_challenge":"1d70b971a45e0d2aa7d33c44be375496","geetest_validate":"ade01a2c2c5bf985b71992658c08f83f","geetest_seccode":"ade01a2c2c5bf985b71992658c08f83f|jordan"}
[2025-08-01 13:58:41] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 13:58:41] 请求数据: {"orderId":"P1010129058032250801135841000001","orderToken":"b3bea9ab24aa458db1ad6723d78e720b","parentChannelId":"3","payChannelId":10003,"payToken":"65df6549078f44a880ab295956cf1b1a","geetest_challenge":"1d70b971a45e0d2aa7d33c44be375496","geetest_validate":"ade01a2c2c5bf985b71992658c08f83f","geetest_seccode":"ade01a2c2c5bf985b71992658c08f83f|jordan"}
[2025-08-01 13:58:42] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 13:58:42] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 13:58:42] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 13:58:42] 验证码格式 1 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 13:58:42] 尝试验证码格式 2: {"geetest_challenge":"1d70b971a45e0d2aa7d33c44be375496","geetest_validate":"cc293e3e4415123651d0edd271664014e6094d041b9a321e62f93419f9be50bd","geetest_seccode":"cc293e3e4415123651d0edd271664014e6094d041b9a321e62f93419f9be50bd|jordan"}
[2025-08-01 13:58:42] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 13:58:42] 第三步请求参数: {"orderId":"P1010129058032250801135841000001","orderToken":"b3bea9ab24aa458db1ad6723d78e720b","parentChannelId":"3","payChannelId":10003,"payToken":"65df6549078f44a880ab295956cf1b1a","geetest_challenge":"1d70b971a45e0d2aa7d33c44be375496","geetest_validate":"cc293e3e4415123651d0edd271664014e6094d041b9a321e62f93419f9be50bd","geetest_seccode":"cc293e3e4415123651d0edd271664014e6094d041b9a321e62f93419f9be50bd|jordan"}
[2025-08-01 13:58:42] 验证码结果: {"geetest_challenge":"1d70b971a45e0d2aa7d33c44be375496","geetest_validate":"cc293e3e4415123651d0edd271664014e6094d041b9a321e62f93419f9be50bd","geetest_seccode":"cc293e3e4415123651d0edd271664014e6094d041b9a321e62f93419f9be50bd|jordan"}
[2025-08-01 13:58:42] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 13:58:42] 请求数据: {"orderId":"P1010129058032250801135841000001","orderToken":"b3bea9ab24aa458db1ad6723d78e720b","parentChannelId":"3","payChannelId":10003,"payToken":"65df6549078f44a880ab295956cf1b1a","geetest_challenge":"1d70b971a45e0d2aa7d33c44be375496","geetest_validate":"cc293e3e4415123651d0edd271664014e6094d041b9a321e62f93419f9be50bd","geetest_seccode":"cc293e3e4415123651d0edd271664014e6094d041b9a321e62f93419f9be50bd|jordan"}
[2025-08-01 13:58:42] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 13:58:42] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 13:58:42] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 13:58:42] 验证码格式 2 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 13:58:42] 尝试验证码格式 3: []
[2025-08-01 13:58:42] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 13:58:42] 第三步请求参数: {"orderId":"P1010129058032250801135841000001","orderToken":"b3bea9ab24aa458db1ad6723d78e720b","parentChannelId":"3","payChannelId":10003,"payToken":"65df6549078f44a880ab295956cf1b1a"}
[2025-08-01 13:58:42] 验证码结果: []
[2025-08-01 13:58:42] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 13:58:42] 请求数据: {"orderId":"P1010129058032250801135841000001","orderToken":"b3bea9ab24aa458db1ad6723d78e720b","parentChannelId":"3","payChannelId":10003,"payToken":"65df6549078f44a880ab295956cf1b1a"}
[2025-08-01 13:58:42] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 13:58:42] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 13:58:42] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 13:58:42] 验证码格式 3 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 13:58:42] 尝试验证码格式 4: {"picCode":"gtest","gtData":{"challenge":"1d70b971a45e0d2aa7d33c44be375496","validate":"b5cfebca2682e72ba94663d14a9bb010","seccode":"b5cfebca2682e72ba94663d14a9bb010|jordan"}}
[2025-08-01 13:58:42] 步骤3: 第三步请求 - 提交验证码结果
[2025-08-01 13:58:42] 第三步请求参数: {"orderId":"P1010129058032250801135841000001","orderToken":"b3bea9ab24aa458db1ad6723d78e720b","parentChannelId":"3","payChannelId":10003,"payToken":"65df6549078f44a880ab295956cf1b1a","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"1d70b971a45e0d2aa7d33c44be375496\",\"validate\":\"b5cfebca2682e72ba94663d14a9bb010\",\"seccode\":\"b5cfebca2682e72ba94663d14a9bb010|jordan\"}}"}
[2025-08-01 13:58:42] 验证码结果: {"picCode":"gtest","gtData":{"challenge":"1d70b971a45e0d2aa7d33c44be375496","validate":"b5cfebca2682e72ba94663d14a9bb010","seccode":"b5cfebca2682e72ba94663d14a9bb010|jordan"}}
[2025-08-01 13:58:42] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 13:58:42] 请求数据: {"orderId":"P1010129058032250801135841000001","orderToken":"b3bea9ab24aa458db1ad6723d78e720b","parentChannelId":"3","payChannelId":10003,"payToken":"65df6549078f44a880ab295956cf1b1a","captchaInfo":"{\"picCode\":\"gtest\",\"gtData\":{\"challenge\":\"1d70b971a45e0d2aa7d33c44be375496\",\"validate\":\"b5cfebca2682e72ba94663d14a9bb010\",\"seccode\":\"b5cfebca2682e72ba94663d14a9bb010|jordan\"}}"}
[2025-08-01 13:58:42] HTTP响应 - 状态码: 200, 响应长度: 62
[2025-08-01 13:58:42] 第三步请求响应: {"return_code":2002,"return_message":"Unauthorized","data":[]}
[2025-08-01 13:58:42] 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 13:58:42] 验证码格式 4 失败: 第三步请求失败: return_code: 2002, return_message: Unauthorized, data: []
[2025-08-01 13:58:42] 即时处理失败: 所有验证码格式都已尝试，均未成功
[2025-08-01 13:58:42] 构造验证码页面URL: /sdo_browser_captcha.php?trade_no=2025080113583943781&orderToken=b3bea9ab24aa458db1ad6723d78e720b&orderId=P1010129058032250801135841000001&payToken=65df6549078f44a880ab295956cf1b1a&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT0xZDcwYjk3MWE0NWUwZDJhYTdkMzNjNDRiZTM3NTQ5NiZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiIxZDcwYjk3MWE0NWUwZDJhYTdkMzNjNDRiZTM3NTQ5NiJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-08-01 13:58:42] SDO支付流程完成，最终支付URL: Array
[2025-08-01 13:58:42] extractQrcodeFromPaymentUrl返回结果类型: array
[2025-08-01 13:58:42] 返回数组内容: {"type":"jump","url":"\/sdo_browser_captcha.php?trade_no=2025080113583943781&orderToken=b3bea9ab24aa458db1ad6723d78e720b&orderId=P1010129058032250801135841000001&payToken=65df6549078f44a880ab295956cf1b1a&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT0xZDcwYjk3MWE0NWUwZDJhYTdkMzNjNDRiZTM3NTQ5NiZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiIxZDcwYjk3MWE0NWUwZDJhYTdkMzNjNDRiZTM3NTQ5NiJ9fSwiY2FwdGNoYVR5cGUiOjR9","message":"需要完成验证码验证"}
[2025-08-01 13:58:42] 返回验证码页面，URL: /sdo_browser_captcha.php?trade_no=2025080113583943781&orderToken=b3bea9ab24aa458db1ad6723d78e720b&orderId=P1010129058032250801135841000001&payToken=65df6549078f44a880ab295956cf1b1a&payChannelId=10003&captchaInfo=eyJjYXB0Y2hhUGFyYW1zIjp7Imd0RGF0YSI6eyJndCI6IjMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjIiwic3VjY2VzcyI6MSwiZ3RfdXJsIjoiaHR0cHM6XC9cL2xvZ2luLnUuc2RvLmNvbVwvb3RoZXJcL2d0ZXN0P2d0PTMxY2M5YWM4YWU1ZWI5ZWYxYWVhZWU5MTEwYmZjNTBjJmNoYWxsZW5nZT0xZDcwYjk3MWE0NWUwZDJhYTdkMzNjNDRiZTM3NTQ5NiZvZmZsaW5lPWZhbHNlJm5ld19jYXB0Y2hhPXRydWUmY2FsbGZ1bmN0aW9uPSIsIm5ld19jYXB0Y2hhIjoxLCJjaGFsbGVuZ2UiOiIxZDcwYjk3MWE0NWUwZDJhYTdkMzNjNDRiZTM3NTQ5NiJ9fSwiY2FwdGNoYVR5cGUiOjR9
[2025-08-01 14:47:06] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-08-01 14:47:06] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-08-01 14:47:08] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-08-01 14:47:08] 充值页面访问成功，页面长度: 27735
[2025-08-01 14:47:08] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-08-01 14:47:08] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-08-01 14:47:08] 成功提取token - itemToken: b48da4a021e7444cb3f06a0f0e326695, captchaToken: 7b185ee51da644f881a3199253c3c27e
[2025-08-01 14:47:08] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-08-01 14:47:08] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"7b185ee51da644f881a3199253c3c27e","itemToken":"b48da4a021e7444cb3f06a0f0e326695","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 14:47:08] 开始提交充值订单
[2025-08-01 14:47:08] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-08-01 14:47:08] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"7b185ee51da644f881a3199253c3c27e","itemToken":"b48da4a021e7444cb3f06a0f0e326695","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 14:47:09] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-08-01 14:47:09] 订单提交完成，响应类型: string
[2025-08-01 14:47:09] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"60d254558e2e441e8630b9778929beff","orderId":"P1010127026170250801144709000001"}}
[2025-08-01 14:47:09] 开始处理订单响应，支付方式: alipay
[2025-08-01 14:47:09] 开始处理订单响应，响应类型: string
[2025-08-01 14:47:09] 检测到JSON响应，开始解析
[2025-08-01 14:47:09] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"60d254558e2e441e8630b9778929beff","orderId":"P1010127026170250801144709000001"}}
[2025-08-01 14:47:09] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=60d254558e2e441e8630b9778929beff&orderId=P1010127026170250801144709000001
[2025-08-01 14:47:09] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=60d254558e2e441e8630b9778929beff&orderId=P1010127026170250801144709000001
[2025-08-01 14:47:09] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=60d254558e2e441e8630b9778929beff&orderId=P1010127026170250801144709000001
[2025-08-01 14:47:09] 提取参数成功 - orderToken: 60d254558e2e441e8630b9778929beff, orderId: P1010127026170250801144709000001
[2025-08-01 14:47:09] 步骤1: 访问支付页面获取payToken
[2025-08-01 14:47:09] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=60d254558e2e441e8630b9778929beff&orderId=P1010127026170250801144709000001, 方法: GET
[2025-08-01 14:47:09] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-08-01 14:47:09] 成功提取payToken: 5bc7f6c17ee648bdbce9fb2363a7becf
[2025-08-01 14:47:09] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-08-01 14:47:09] 第一次请求参数: {"orderId":"P1010127026170250801144709000001","orderToken":"60d254558e2e441e8630b9778929beff","payChannelId":"3","payToken":"5bc7f6c17ee648bdbce9fb2363a7becf","routerFlg":"1"}
[2025-08-01 14:47:09] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 14:47:09] 请求数据: {"orderId":"P1010127026170250801144709000001","orderToken":"60d254558e2e441e8630b9778929beff","payChannelId":"3","payToken":"5bc7f6c17ee648bdbce9fb2363a7becf","routerFlg":"1"}
[2025-08-01 14:47:09] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-08-01 14:47:09] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-08-01 14:47:09] 第一次请求成功，获得最终payChannelId: 10003
[2025-08-01 14:47:09] 步骤3: 完成支付流程，payChannelId: 10003
[2025-08-01 14:47:09] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 14:47:09] 请求数据: {"orderId":"P1010127026170250801144709000001","orderToken":"60d254558e2e441e8630b9778929beff","payChannelId":10003,"payToken":"5bc7f6c17ee648bdbce9fb2363a7becf"}
[2025-08-01 14:47:09] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-08-01 14:47:09] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=48c90568be064eaa115d6e35d0aa9517&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"48c90568be064eaa115d6e35d0aa9517\"}},\"captchaType\":4}"}}
[2025-08-01 14:47:09] SDO支付流程失败: 无法获取最终支付URL: 
[2025-08-01 14:47:09] extractQrcodeFromPaymentUrl返回结果类型: string
[2025-08-01 14:47:09] 返回字符串内容: https://pay.sdo.com/cashier/pay?orderToken=60d254558e2e441e8630b9778929beff&orderId=P1010127026170250801144709000001
[2025-08-01 14:47:09] 返回原始支付页面URL: https://pay.sdo.com/cashier/pay?orderToken=60d254558e2e441e8630b9778929beff&orderId=P1010127026170250801144709000001
[2025-08-01 15:15:50] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-08-01 15:15:50] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-08-01 15:15:50] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-08-01 15:15:50] 充值页面访问成功，页面长度: 27735
[2025-08-01 15:15:50] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-08-01 15:15:50] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-08-01 15:15:50] 成功提取token - itemToken: a92087a4b2174d37adc0e250d1ddd9a2, captchaToken: 78da01ef850f407e8b0b33cf212f4a8c
[2025-08-01 15:15:50] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-08-01 15:15:50] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"78da01ef850f407e8b0b33cf212f4a8c","itemToken":"a92087a4b2174d37adc0e250d1ddd9a2","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 15:15:50] 开始提交充值订单
[2025-08-01 15:15:50] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-08-01 15:15:50] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"78da01ef850f407e8b0b33cf212f4a8c","itemToken":"a92087a4b2174d37adc0e250d1ddd9a2","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 15:15:50] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-08-01 15:15:50] 订单提交完成，响应类型: string
[2025-08-01 15:15:50] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"7e625e68121c487c9530da93837a4ac3","orderId":"P1010127026167250801151551000001"}}
[2025-08-01 15:15:50] 开始处理订单响应，支付方式: alipay
[2025-08-01 15:15:50] 开始处理订单响应，响应类型: string
[2025-08-01 15:15:50] 检测到JSON响应，开始解析
[2025-08-01 15:15:50] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"7e625e68121c487c9530da93837a4ac3","orderId":"P1010127026167250801151551000001"}}
[2025-08-01 15:15:50] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=7e625e68121c487c9530da93837a4ac3&orderId=P1010127026167250801151551000001
[2025-08-01 15:15:50] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=7e625e68121c487c9530da93837a4ac3&orderId=P1010127026167250801151551000001
[2025-08-01 15:15:50] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=7e625e68121c487c9530da93837a4ac3&orderId=P1010127026167250801151551000001
[2025-08-01 15:15:50] 提取参数成功 - orderToken: 7e625e68121c487c9530da93837a4ac3, orderId: P1010127026167250801151551000001
[2025-08-01 15:15:50] 步骤1: 访问支付页面获取payToken
[2025-08-01 15:15:50] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=7e625e68121c487c9530da93837a4ac3&orderId=P1010127026167250801151551000001, 方法: GET
[2025-08-01 15:15:50] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-08-01 15:15:50] 成功提取payToken: 8a7a643cb6594557b090957232ae761c
[2025-08-01 15:15:50] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-08-01 15:15:50] 第一次请求参数: {"orderId":"P1010127026167250801151551000001","orderToken":"7e625e68121c487c9530da93837a4ac3","payChannelId":"3","payToken":"8a7a643cb6594557b090957232ae761c","routerFlg":"1"}
[2025-08-01 15:15:50] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 15:15:50] 请求数据: {"orderId":"P1010127026167250801151551000001","orderToken":"7e625e68121c487c9530da93837a4ac3","payChannelId":"3","payToken":"8a7a643cb6594557b090957232ae761c","routerFlg":"1"}
[2025-08-01 15:15:51] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-08-01 15:15:51] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-08-01 15:15:51] 第一次请求成功，获得最终payChannelId: 10003
[2025-08-01 15:15:51] 步骤3: 完成支付流程，payChannelId: 10003
[2025-08-01 15:15:51] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 15:15:51] 请求数据: {"orderId":"P1010127026167250801151551000001","orderToken":"7e625e68121c487c9530da93837a4ac3","payChannelId":10003,"payToken":"8a7a643cb6594557b090957232ae761c"}
[2025-08-01 15:15:51] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-08-01 15:15:51] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=c0188aa884765c5d383c68e049b0e488&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"c0188aa884765c5d383c68e049b0e488\"}},\"captchaType\":4}"}}
[2025-08-01 15:15:51] SDO支付流程失败: 无法获取最终支付URL: 
[2025-08-01 15:15:51] extractQrcodeFromPaymentUrl返回结果类型: string
[2025-08-01 15:15:51] 返回字符串内容: https://pay.sdo.com/cashier/pay?orderToken=7e625e68121c487c9530da93837a4ac3&orderId=P1010127026167250801151551000001
[2025-08-01 15:15:51] 返回原始支付页面URL: https://pay.sdo.com/cashier/pay?orderToken=7e625e68121c487c9530da93837a4ac3&orderId=P1010127026167250801151551000001
[2025-08-01 15:18:04] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-08-01 15:18:04] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-08-01 15:18:04] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-08-01 15:18:04] 充值页面访问成功，页面长度: 27735
[2025-08-01 15:18:04] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-08-01 15:18:04] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-08-01 15:18:04] 成功提取token - itemToken: 20a782563bf04e1d953fc1c2473f4d0b, captchaToken: 2da1814ca8e94e938de1e2dd6b8e3375
[2025-08-01 15:18:04] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-08-01 15:18:04] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"2da1814ca8e94e938de1e2dd6b8e3375","itemToken":"20a782563bf04e1d953fc1c2473f4d0b","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 15:18:04] 开始提交充值订单
[2025-08-01 15:18:04] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-08-01 15:18:04] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"2da1814ca8e94e938de1e2dd6b8e3375","itemToken":"20a782563bf04e1d953fc1c2473f4d0b","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 15:18:04] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-08-01 15:18:04] 订单提交完成，响应类型: string
[2025-08-01 15:18:04] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"3745781c8e814714a59d00bba06920ef","orderId":"P1010129058032250801151805000001"}}
[2025-08-01 15:18:04] 开始处理订单响应，支付方式: alipay
[2025-08-01 15:18:04] 开始处理订单响应，响应类型: string
[2025-08-01 15:18:04] 检测到JSON响应，开始解析
[2025-08-01 15:18:04] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"3745781c8e814714a59d00bba06920ef","orderId":"P1010129058032250801151805000001"}}
[2025-08-01 15:18:04] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=3745781c8e814714a59d00bba06920ef&orderId=P1010129058032250801151805000001
[2025-08-01 15:18:04] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=3745781c8e814714a59d00bba06920ef&orderId=P1010129058032250801151805000001
[2025-08-01 15:18:04] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=3745781c8e814714a59d00bba06920ef&orderId=P1010129058032250801151805000001
[2025-08-01 15:18:04] 提取参数成功 - orderToken: 3745781c8e814714a59d00bba06920ef, orderId: P1010129058032250801151805000001
[2025-08-01 15:18:04] 步骤1: 访问支付页面获取payToken
[2025-08-01 15:18:04] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=3745781c8e814714a59d00bba06920ef&orderId=P1010129058032250801151805000001, 方法: GET
[2025-08-01 15:18:05] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-08-01 15:18:05] 成功提取payToken: cb7c73efb0804249b1e1af313d25944a
[2025-08-01 15:18:05] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-08-01 15:18:05] 第一次请求参数: {"orderId":"P1010129058032250801151805000001","orderToken":"3745781c8e814714a59d00bba06920ef","payChannelId":"3","payToken":"cb7c73efb0804249b1e1af313d25944a","routerFlg":"1"}
[2025-08-01 15:18:05] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 15:18:05] 请求数据: {"orderId":"P1010129058032250801151805000001","orderToken":"3745781c8e814714a59d00bba06920ef","payChannelId":"3","payToken":"cb7c73efb0804249b1e1af313d25944a","routerFlg":"1"}
[2025-08-01 15:18:05] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-08-01 15:18:05] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-08-01 15:18:05] 第一次请求成功，获得最终payChannelId: 10003
[2025-08-01 15:18:05] 步骤3: 完成支付流程，payChannelId: 10003
[2025-08-01 15:18:05] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 15:18:05] 请求数据: {"orderId":"P1010129058032250801151805000001","orderToken":"3745781c8e814714a59d00bba06920ef","payChannelId":10003,"payToken":"cb7c73efb0804249b1e1af313d25944a"}
[2025-08-01 15:18:05] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-08-01 15:18:05] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=dbb085107ac104a0d6ff1feab923ebed&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"dbb085107ac104a0d6ff1feab923ebed\"}},\"captchaType\":4}"}}
[2025-08-01 15:18:05] SDO支付流程失败: 无法获取最终支付URL: 
[2025-08-01 15:18:05] extractQrcodeFromPaymentUrl返回结果类型: string
[2025-08-01 15:18:05] 返回字符串内容: https://pay.sdo.com/cashier/pay?orderToken=3745781c8e814714a59d00bba06920ef&orderId=P1010129058032250801151805000001
[2025-08-01 15:18:05] 返回原始支付页面URL: https://pay.sdo.com/cashier/pay?orderToken=3745781c8e814714a59d00bba06920ef&orderId=P1010129058032250801151805000001
[2025-08-01 15:24:29] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-08-01 15:24:29] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-08-01 15:24:29] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-08-01 15:24:29] 充值页面访问成功，页面长度: 27735
[2025-08-01 15:24:29] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-08-01 15:24:29] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-08-01 15:24:29] 成功提取token - itemToken: aef27c06a9bb4872879446e5a73eb99a, captchaToken: 0abbb645d2534c0fba2c42aa46332977
[2025-08-01 15:24:29] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-08-01 15:24:29] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"0abbb645d2534c0fba2c42aa46332977","itemToken":"aef27c06a9bb4872879446e5a73eb99a","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 15:24:29] 开始提交充值订单
[2025-08-01 15:24:29] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-08-01 15:24:29] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"0abbb645d2534c0fba2c42aa46332977","itemToken":"aef27c06a9bb4872879446e5a73eb99a","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 15:24:30] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-08-01 15:24:30] 订单提交完成，响应类型: string
[2025-08-01 15:24:30] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"04eb3648c5564b2c9b1764f357c3bc0c","orderId":"P1010127026168250801152430000001"}}
[2025-08-01 15:24:30] 开始处理订单响应，支付方式: alipay
[2025-08-01 15:24:30] 开始处理订单响应，响应类型: string
[2025-08-01 15:24:30] 检测到JSON响应，开始解析
[2025-08-01 15:24:30] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"04eb3648c5564b2c9b1764f357c3bc0c","orderId":"P1010127026168250801152430000001"}}
[2025-08-01 15:24:30] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=04eb3648c5564b2c9b1764f357c3bc0c&orderId=P1010127026168250801152430000001
[2025-08-01 15:24:30] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=04eb3648c5564b2c9b1764f357c3bc0c&orderId=P1010127026168250801152430000001
[2025-08-01 15:24:30] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=04eb3648c5564b2c9b1764f357c3bc0c&orderId=P1010127026168250801152430000001
[2025-08-01 15:24:30] 提取参数成功 - orderToken: 04eb3648c5564b2c9b1764f357c3bc0c, orderId: P1010127026168250801152430000001
[2025-08-01 15:24:30] 步骤1: 访问支付页面获取payToken
[2025-08-01 15:24:30] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=04eb3648c5564b2c9b1764f357c3bc0c&orderId=P1010127026168250801152430000001, 方法: GET
[2025-08-01 15:24:30] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-08-01 15:24:30] 成功提取payToken: 7e235b3e36e840369dff7cf48adb6d44
[2025-08-01 15:24:30] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-08-01 15:24:30] 第一次请求参数: {"orderId":"P1010127026168250801152430000001","orderToken":"04eb3648c5564b2c9b1764f357c3bc0c","payChannelId":"3","payToken":"7e235b3e36e840369dff7cf48adb6d44","routerFlg":"1"}
[2025-08-01 15:24:30] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 15:24:30] 请求数据: {"orderId":"P1010127026168250801152430000001","orderToken":"04eb3648c5564b2c9b1764f357c3bc0c","payChannelId":"3","payToken":"7e235b3e36e840369dff7cf48adb6d44","routerFlg":"1"}
[2025-08-01 15:24:30] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-08-01 15:24:30] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-08-01 15:24:30] 第一次请求成功，获得最终payChannelId: 10003
[2025-08-01 15:24:30] 步骤3: 完成支付流程，payChannelId: 10003
[2025-08-01 15:24:30] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 15:24:30] 请求数据: {"orderId":"P1010127026168250801152430000001","orderToken":"04eb3648c5564b2c9b1764f357c3bc0c","payChannelId":10003,"payToken":"7e235b3e36e840369dff7cf48adb6d44"}
[2025-08-01 15:24:30] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-08-01 15:24:30] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=9cfdd5cf0b6457834212a442f9bb9068&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"9cfdd5cf0b6457834212a442f9bb9068\"}},\"captchaType\":4}"}}
[2025-08-01 15:24:30] SDO支付流程失败: 无法获取最终支付URL: 
[2025-08-01 15:24:30] extractQrcodeFromPaymentUrl返回结果类型: string
[2025-08-01 15:24:30] 返回字符串内容: https://pay.sdo.com/cashier/pay?orderToken=04eb3648c5564b2c9b1764f357c3bc0c&orderId=P1010127026168250801152430000001
[2025-08-01 15:24:30] 返回原始支付页面URL: https://pay.sdo.com/cashier/pay?orderToken=04eb3648c5564b2c9b1764f357c3bc0c&orderId=P1010127026168250801152430000001
[2025-08-01 15:28:43] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-08-01 15:28:43] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-08-01 15:28:43] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-08-01 15:28:43] 充值页面访问成功，页面长度: 27735
[2025-08-01 15:28:43] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-08-01 15:28:43] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-08-01 15:28:43] 成功提取token - itemToken: 3c9d92eccb6f4d7299bbc38c657ec2f2, captchaToken: 5b73bfe2bf3e44eebbadba59431b373f
[2025-08-01 15:28:43] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-08-01 15:28:43] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"5b73bfe2bf3e44eebbadba59431b373f","itemToken":"3c9d92eccb6f4d7299bbc38c657ec2f2","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 15:28:43] 开始提交充值订单
[2025-08-01 15:28:43] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-08-01 15:28:43] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"5b73bfe2bf3e44eebbadba59431b373f","itemToken":"3c9d92eccb6f4d7299bbc38c657ec2f2","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 15:28:43] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-08-01 15:28:43] 订单提交完成，响应类型: string
[2025-08-01 15:28:43] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"4d43ddd9114f4342a25b400a1a8819e1","orderId":"P1010127026170250801152844000001"}}
[2025-08-01 15:28:43] 开始处理订单响应，支付方式: alipay
[2025-08-01 15:28:43] 开始处理订单响应，响应类型: string
[2025-08-01 15:28:43] 检测到JSON响应，开始解析
[2025-08-01 15:28:43] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"4d43ddd9114f4342a25b400a1a8819e1","orderId":"P1010127026170250801152844000001"}}
[2025-08-01 15:28:43] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=4d43ddd9114f4342a25b400a1a8819e1&orderId=P1010127026170250801152844000001
[2025-08-01 15:28:43] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=4d43ddd9114f4342a25b400a1a8819e1&orderId=P1010127026170250801152844000001
[2025-08-01 15:28:43] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=4d43ddd9114f4342a25b400a1a8819e1&orderId=P1010127026170250801152844000001
[2025-08-01 15:28:43] 提取参数成功 - orderToken: 4d43ddd9114f4342a25b400a1a8819e1, orderId: P1010127026170250801152844000001
[2025-08-01 15:28:43] 步骤1: 访问支付页面获取payToken
[2025-08-01 15:28:43] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=4d43ddd9114f4342a25b400a1a8819e1&orderId=P1010127026170250801152844000001, 方法: GET
[2025-08-01 15:28:43] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-08-01 15:28:43] 成功提取payToken: d3fba7693d874c329c59aec6cb299276
[2025-08-01 15:28:43] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-08-01 15:28:43] 第一次请求参数: {"orderId":"P1010127026170250801152844000001","orderToken":"4d43ddd9114f4342a25b400a1a8819e1","payChannelId":"3","payToken":"d3fba7693d874c329c59aec6cb299276","routerFlg":"1"}
[2025-08-01 15:28:43] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 15:28:43] 请求数据: {"orderId":"P1010127026170250801152844000001","orderToken":"4d43ddd9114f4342a25b400a1a8819e1","payChannelId":"3","payToken":"d3fba7693d874c329c59aec6cb299276","routerFlg":"1"}
[2025-08-01 15:28:44] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-08-01 15:28:44] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-08-01 15:28:44] 第一次请求成功，获得最终payChannelId: 10003
[2025-08-01 15:28:44] 步骤3: 完成支付流程，payChannelId: 10003
[2025-08-01 15:28:44] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 15:28:44] 请求数据: {"orderId":"P1010127026170250801152844000001","orderToken":"4d43ddd9114f4342a25b400a1a8819e1","payChannelId":10003,"payToken":"d3fba7693d874c329c59aec6cb299276"}
[2025-08-01 15:28:44] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-08-01 15:28:44] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=2c2add67ceafe7e77e5dc52bc0b972af&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"2c2add67ceafe7e77e5dc52bc0b972af\"}},\"captchaType\":4}"}}
[2025-08-01 15:28:44] SDO支付流程失败: 无法获取最终支付URL: 
[2025-08-01 15:28:44] extractQrcodeFromPaymentUrl返回结果类型: string
[2025-08-01 15:28:44] 返回字符串内容: https://pay.sdo.com/cashier/pay?orderToken=4d43ddd9114f4342a25b400a1a8819e1&orderId=P1010127026170250801152844000001
[2025-08-01 15:28:44] 返回原始支付页面URL: https://pay.sdo.com/cashier/pay?orderToken=4d43ddd9114f4342a25b400a1a8819e1&orderId=P1010127026170250801152844000001
[2025-08-01 15:32:27] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-08-01 15:32:27] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-08-01 15:32:27] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-08-01 15:32:27] 充值页面访问成功，页面长度: 27735
[2025-08-01 15:32:27] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-08-01 15:32:27] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-08-01 15:32:27] 成功提取token - itemToken: 0e6783d265c540eb99281966e5707cd8, captchaToken: 11d993bce8954fd18fa8d013b488e4f3
[2025-08-01 15:32:27] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-08-01 15:32:27] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"11d993bce8954fd18fa8d013b488e4f3","itemToken":"0e6783d265c540eb99281966e5707cd8","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 15:32:27] 开始提交充值订单
[2025-08-01 15:32:27] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-08-01 15:32:27] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"11d993bce8954fd18fa8d013b488e4f3","itemToken":"0e6783d265c540eb99281966e5707cd8","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 15:32:27] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-08-01 15:32:27] 订单提交完成，响应类型: string
[2025-08-01 15:32:27] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"53f1b0f7f3234c77910cdebae5eda1e6","orderId":"P1010129058031250801153227000001"}}
[2025-08-01 15:32:27] 开始处理订单响应，支付方式: alipay
[2025-08-01 15:32:27] 开始处理订单响应，响应类型: string
[2025-08-01 15:32:27] 检测到JSON响应，开始解析
[2025-08-01 15:32:27] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"53f1b0f7f3234c77910cdebae5eda1e6","orderId":"P1010129058031250801153227000001"}}
[2025-08-01 15:32:27] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=53f1b0f7f3234c77910cdebae5eda1e6&orderId=P1010129058031250801153227000001
[2025-08-01 15:32:27] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=53f1b0f7f3234c77910cdebae5eda1e6&orderId=P1010129058031250801153227000001
[2025-08-01 15:32:27] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=53f1b0f7f3234c77910cdebae5eda1e6&orderId=P1010129058031250801153227000001
[2025-08-01 15:32:27] 提取参数成功 - orderToken: 53f1b0f7f3234c77910cdebae5eda1e6, orderId: P1010129058031250801153227000001
[2025-08-01 15:32:27] 步骤1: 访问支付页面获取payToken
[2025-08-01 15:32:27] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=53f1b0f7f3234c77910cdebae5eda1e6&orderId=P1010129058031250801153227000001, 方法: GET
[2025-08-01 15:32:27] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-08-01 15:32:27] 成功提取payToken: 34f00e57fdeb4eca8cd7b6f1863e310d
[2025-08-01 15:32:27] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-08-01 15:32:27] 第一次请求参数: {"orderId":"P1010129058031250801153227000001","orderToken":"53f1b0f7f3234c77910cdebae5eda1e6","payChannelId":"3","payToken":"34f00e57fdeb4eca8cd7b6f1863e310d","routerFlg":"1"}
[2025-08-01 15:32:27] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 15:32:27] 请求数据: {"orderId":"P1010129058031250801153227000001","orderToken":"53f1b0f7f3234c77910cdebae5eda1e6","payChannelId":"3","payToken":"34f00e57fdeb4eca8cd7b6f1863e310d","routerFlg":"1"}
[2025-08-01 15:32:27] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-08-01 15:32:27] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-08-01 15:32:27] 第一次请求成功，获得最终payChannelId: 10003
[2025-08-01 15:32:27] 步骤3: 完成支付流程，payChannelId: 10003
[2025-08-01 15:32:27] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 15:32:27] 请求数据: {"orderId":"P1010129058031250801153227000001","orderToken":"53f1b0f7f3234c77910cdebae5eda1e6","payChannelId":10003,"payToken":"34f00e57fdeb4eca8cd7b6f1863e310d"}
[2025-08-01 15:32:27] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-08-01 15:32:27] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=26f64f2c946afec5e0e64c356f81f20e&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"26f64f2c946afec5e0e64c356f81f20e\"}},\"captchaType\":4}"}}
[2025-08-01 15:32:27] SDO支付流程失败: 无法获取最终支付URL: 
[2025-08-01 15:32:27] extractQrcodeFromPaymentUrl返回结果类型: string
[2025-08-01 15:32:27] 返回字符串内容: https://pay.sdo.com/cashier/pay?orderToken=53f1b0f7f3234c77910cdebae5eda1e6&orderId=P1010129058031250801153227000001
[2025-08-01 15:32:27] 返回原始支付页面URL: https://pay.sdo.com/cashier/pay?orderToken=53f1b0f7f3234c77910cdebae5eda1e6&orderId=P1010129058031250801153227000001
[2025-08-01 15:37:13] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-08-01 15:37:13] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-08-01 15:37:13] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-08-01 15:37:13] 充值页面访问成功，页面长度: 27735
[2025-08-01 15:37:13] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-08-01 15:37:13] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-08-01 15:37:13] 成功提取token - itemToken: a21ba00cd39f40d285f7af2e2236e575, captchaToken: db52d4265dab432b99e68f02e1c8af8c
[2025-08-01 15:37:13] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-08-01 15:37:13] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"db52d4265dab432b99e68f02e1c8af8c","itemToken":"a21ba00cd39f40d285f7af2e2236e575","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 15:37:13] 开始提交充值订单
[2025-08-01 15:37:13] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-08-01 15:37:13] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"db52d4265dab432b99e68f02e1c8af8c","itemToken":"a21ba00cd39f40d285f7af2e2236e575","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 15:37:14] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-08-01 15:37:14] 订单提交完成，响应类型: string
[2025-08-01 15:37:14] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"63da12a18b4c42c6bb46945598b058dc","orderId":"P1010127026168250801153714000001"}}
[2025-08-01 15:37:14] 开始处理订单响应，支付方式: alipay
[2025-08-01 15:37:14] 开始处理订单响应，响应类型: string
[2025-08-01 15:37:14] 检测到JSON响应，开始解析
[2025-08-01 15:37:14] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"63da12a18b4c42c6bb46945598b058dc","orderId":"P1010127026168250801153714000001"}}
[2025-08-01 15:37:14] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=63da12a18b4c42c6bb46945598b058dc&orderId=P1010127026168250801153714000001
[2025-08-01 15:37:14] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=63da12a18b4c42c6bb46945598b058dc&orderId=P1010127026168250801153714000001
[2025-08-01 15:37:14] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=63da12a18b4c42c6bb46945598b058dc&orderId=P1010127026168250801153714000001
[2025-08-01 15:37:14] 提取参数成功 - orderToken: 63da12a18b4c42c6bb46945598b058dc, orderId: P1010127026168250801153714000001
[2025-08-01 15:37:14] 步骤1: 访问支付页面获取payToken
[2025-08-01 15:37:14] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=63da12a18b4c42c6bb46945598b058dc&orderId=P1010127026168250801153714000001, 方法: GET
[2025-08-01 15:37:14] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-08-01 15:37:14] 成功提取payToken: ae8de6d5c0934689b2021b2564727a9f
[2025-08-01 15:37:14] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-08-01 15:37:14] 第一次请求参数: {"orderId":"P1010127026168250801153714000001","orderToken":"63da12a18b4c42c6bb46945598b058dc","payChannelId":"3","payToken":"ae8de6d5c0934689b2021b2564727a9f","routerFlg":"1"}
[2025-08-01 15:37:14] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 15:37:14] 请求数据: {"orderId":"P1010127026168250801153714000001","orderToken":"63da12a18b4c42c6bb46945598b058dc","payChannelId":"3","payToken":"ae8de6d5c0934689b2021b2564727a9f","routerFlg":"1"}
[2025-08-01 15:37:14] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-08-01 15:37:14] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-08-01 15:37:14] 第一次请求成功，获得最终payChannelId: 10003
[2025-08-01 15:37:14] 步骤3: 完成支付流程，payChannelId: 10003
[2025-08-01 15:37:14] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 15:37:14] 请求数据: {"orderId":"P1010127026168250801153714000001","orderToken":"63da12a18b4c42c6bb46945598b058dc","payChannelId":10003,"payToken":"ae8de6d5c0934689b2021b2564727a9f"}
[2025-08-01 15:37:14] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-08-01 15:37:14] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=9206a9c1ffd014d91d3d2c44699ab26c&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"9206a9c1ffd014d91d3d2c44699ab26c\"}},\"captchaType\":4}"}}
[2025-08-01 15:37:14] SDO支付流程失败: 无法获取最终支付URL: 
[2025-08-01 15:37:14] extractQrcodeFromPaymentUrl返回结果类型: string
[2025-08-01 15:37:14] 返回字符串内容: https://pay.sdo.com/cashier/pay?orderToken=63da12a18b4c42c6bb46945598b058dc&orderId=P1010127026168250801153714000001
[2025-08-01 15:37:14] 返回原始支付页面URL: https://pay.sdo.com/cashier/pay?orderToken=63da12a18b4c42c6bb46945598b058dc&orderId=P1010127026168250801153714000001
[2025-08-01 15:47:13] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-08-01 15:47:13] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-08-01 15:47:14] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-08-01 15:47:14] 充值页面访问成功，页面长度: 27735
[2025-08-01 15:47:14] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-08-01 15:47:14] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-08-01 15:47:14] 成功提取token - itemToken: a8a31765ed244805a4101740ffa3bce1, captchaToken: 7d9d9bf6d10c4a28acd4361753ac3ada
[2025-08-01 15:47:14] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-08-01 15:47:14] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"7d9d9bf6d10c4a28acd4361753ac3ada","itemToken":"a8a31765ed244805a4101740ffa3bce1","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 15:47:14] 开始提交充值订单
[2025-08-01 15:47:14] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-08-01 15:47:14] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"7d9d9bf6d10c4a28acd4361753ac3ada","itemToken":"a8a31765ed244805a4101740ffa3bce1","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 15:47:14] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-08-01 15:47:14] 订单提交完成，响应类型: string
[2025-08-01 15:47:14] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"3af3a45a98264aa1be7a9add0c086e9f","orderId":"P1010127026167250801154714000001"}}
[2025-08-01 15:47:14] 开始处理订单响应，支付方式: alipay
[2025-08-01 15:47:14] 开始处理订单响应，响应类型: string
[2025-08-01 15:47:14] 检测到JSON响应，开始解析
[2025-08-01 15:47:14] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"3af3a45a98264aa1be7a9add0c086e9f","orderId":"P1010127026167250801154714000001"}}
[2025-08-01 15:47:14] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=3af3a45a98264aa1be7a9add0c086e9f&orderId=P1010127026167250801154714000001
[2025-08-01 15:47:14] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=3af3a45a98264aa1be7a9add0c086e9f&orderId=P1010127026167250801154714000001
[2025-08-01 15:47:14] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=3af3a45a98264aa1be7a9add0c086e9f&orderId=P1010127026167250801154714000001
[2025-08-01 15:47:14] 提取参数成功 - orderToken: 3af3a45a98264aa1be7a9add0c086e9f, orderId: P1010127026167250801154714000001
[2025-08-01 15:47:14] 步骤1: 访问支付页面获取payToken
[2025-08-01 15:47:14] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=3af3a45a98264aa1be7a9add0c086e9f&orderId=P1010127026167250801154714000001, 方法: GET
[2025-08-01 15:47:14] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-08-01 15:47:14] 成功提取payToken: fcefafb61c364bf9be93e0b484c6aa84
[2025-08-01 15:47:14] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-08-01 15:47:14] 第一次请求参数: {"orderId":"P1010127026167250801154714000001","orderToken":"3af3a45a98264aa1be7a9add0c086e9f","payChannelId":"3","payToken":"fcefafb61c364bf9be93e0b484c6aa84","routerFlg":"1"}
[2025-08-01 15:47:14] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 15:47:14] 请求数据: {"orderId":"P1010127026167250801154714000001","orderToken":"3af3a45a98264aa1be7a9add0c086e9f","payChannelId":"3","payToken":"fcefafb61c364bf9be93e0b484c6aa84","routerFlg":"1"}
[2025-08-01 15:47:14] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-08-01 15:47:14] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-08-01 15:47:14] 第一次请求成功，获得最终payChannelId: 10003
[2025-08-01 15:47:14] 步骤3: 完成支付流程，payChannelId: 10003
[2025-08-01 15:47:14] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 15:47:14] 请求数据: {"orderId":"P1010127026167250801154714000001","orderToken":"3af3a45a98264aa1be7a9add0c086e9f","payChannelId":10003,"payToken":"fcefafb61c364bf9be93e0b484c6aa84"}
[2025-08-01 15:47:15] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-08-01 15:47:15] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=e4b1aca625f4ae1800ed97c2a0ea90f5&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"e4b1aca625f4ae1800ed97c2a0ea90f5\"}},\"captchaType\":4}"}}
[2025-08-01 15:47:15] SDO支付流程失败: 无法获取最终支付URL: 
[2025-08-01 15:47:15] extractQrcodeFromPaymentUrl返回结果类型: string
[2025-08-01 15:47:15] 返回字符串内容: https://pay.sdo.com/cashier/pay?orderToken=3af3a45a98264aa1be7a9add0c086e9f&orderId=P1010127026167250801154714000001
[2025-08-01 15:47:15] 返回原始支付页面URL: https://pay.sdo.com/cashier/pay?orderToken=3af3a45a98264aa1be7a9add0c086e9f&orderId=P1010127026167250801154714000001
[2025-08-01 15:49:20] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-08-01 15:49:20] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-08-01 15:49:21] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-08-01 15:49:21] 充值页面访问成功，页面长度: 27735
[2025-08-01 15:49:21] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-08-01 15:49:21] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-08-01 15:49:21] 成功提取token - itemToken: de5c2d655ce2419e920fdde0022cd1fe, captchaToken: 58fb906f30194d6fa4a13b4d0adb1aad
[2025-08-01 15:49:21] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-08-01 15:49:21] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"58fb906f30194d6fa4a13b4d0adb1aad","itemToken":"de5c2d655ce2419e920fdde0022cd1fe","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 15:49:21] 开始提交充值订单
[2025-08-01 15:49:21] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-08-01 15:49:21] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"58fb906f30194d6fa4a13b4d0adb1aad","itemToken":"de5c2d655ce2419e920fdde0022cd1fe","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 15:49:21] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-08-01 15:49:21] 订单提交完成，响应类型: string
[2025-08-01 15:49:21] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"b8a76bcb4b2d4ca68caa400ab45a5a31","orderId":"P1010127026170250801154921000001"}}
[2025-08-01 15:49:21] 开始处理订单响应，支付方式: alipay
[2025-08-01 15:49:21] 开始处理订单响应，响应类型: string
[2025-08-01 15:49:21] 检测到JSON响应，开始解析
[2025-08-01 15:49:21] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"b8a76bcb4b2d4ca68caa400ab45a5a31","orderId":"P1010127026170250801154921000001"}}
[2025-08-01 15:49:21] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=b8a76bcb4b2d4ca68caa400ab45a5a31&orderId=P1010127026170250801154921000001
[2025-08-01 15:49:21] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=b8a76bcb4b2d4ca68caa400ab45a5a31&orderId=P1010127026170250801154921000001
[2025-08-01 15:49:21] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=b8a76bcb4b2d4ca68caa400ab45a5a31&orderId=P1010127026170250801154921000001
[2025-08-01 15:49:21] 提取参数成功 - orderToken: b8a76bcb4b2d4ca68caa400ab45a5a31, orderId: P1010127026170250801154921000001
[2025-08-01 15:49:21] 步骤1: 访问支付页面获取payToken
[2025-08-01 15:49:21] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=b8a76bcb4b2d4ca68caa400ab45a5a31&orderId=P1010127026170250801154921000001, 方法: GET
[2025-08-01 15:49:21] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-08-01 15:49:21] 成功提取payToken: ffba0af982244e0ca9bb62c17871905e
[2025-08-01 15:49:21] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-08-01 15:49:21] 第一次请求参数: {"orderId":"P1010127026170250801154921000001","orderToken":"b8a76bcb4b2d4ca68caa400ab45a5a31","payChannelId":"3","payToken":"ffba0af982244e0ca9bb62c17871905e","routerFlg":"1"}
[2025-08-01 15:49:21] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 15:49:21] 请求数据: {"orderId":"P1010127026170250801154921000001","orderToken":"b8a76bcb4b2d4ca68caa400ab45a5a31","payChannelId":"3","payToken":"ffba0af982244e0ca9bb62c17871905e","routerFlg":"1"}
[2025-08-01 15:49:21] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-08-01 15:49:21] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-08-01 15:49:21] 第一次请求成功，获得最终payChannelId: 10003
[2025-08-01 15:49:21] 步骤3: 完成支付流程，payChannelId: 10003
[2025-08-01 15:49:21] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 15:49:21] 请求数据: {"orderId":"P1010127026170250801154921000001","orderToken":"b8a76bcb4b2d4ca68caa400ab45a5a31","payChannelId":10003,"payToken":"ffba0af982244e0ca9bb62c17871905e"}
[2025-08-01 15:49:21] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-08-01 15:49:21] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=a3b2ef7e45453997855fbe13b5ea4f34&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"a3b2ef7e45453997855fbe13b5ea4f34\"}},\"captchaType\":4}"}}
[2025-08-01 15:49:21] SDO支付流程失败: 无法获取最终支付URL: 
[2025-08-01 15:49:21] extractQrcodeFromPaymentUrl返回结果类型: string
[2025-08-01 15:49:21] 返回字符串内容: https://pay.sdo.com/cashier/pay?orderToken=b8a76bcb4b2d4ca68caa400ab45a5a31&orderId=P1010127026170250801154921000001
[2025-08-01 15:49:21] 返回原始支付页面URL: https://pay.sdo.com/cashier/pay?orderToken=b8a76bcb4b2d4ca68caa400ab45a5a31&orderId=P1010127026170250801154921000001
[2025-08-01 16:05:37] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-08-01 16:05:37] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-08-01 16:05:38] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-08-01 16:05:38] 充值页面访问成功，页面长度: 27735
[2025-08-01 16:05:38] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-08-01 16:05:38] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-08-01 16:05:38] 成功提取token - itemToken: d7f7dfbe7a1544378d32f04aa22d9f05, captchaToken: 905f0cd4a7054c70b13bdb9968b269a7
[2025-08-01 16:05:38] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-08-01 16:05:38] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"905f0cd4a7054c70b13bdb9968b269a7","itemToken":"d7f7dfbe7a1544378d32f04aa22d9f05","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 16:05:38] 开始提交充值订单
[2025-08-01 16:05:38] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-08-01 16:05:38] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"905f0cd4a7054c70b13bdb9968b269a7","itemToken":"d7f7dfbe7a1544378d32f04aa22d9f05","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 16:05:38] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-08-01 16:05:38] 订单提交完成，响应类型: string
[2025-08-01 16:05:38] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"b997a42e38934010b840eab8bb4105b3","orderId":"P1010129058031250801160538000001"}}
[2025-08-01 16:05:38] 开始处理订单响应，支付方式: alipay
[2025-08-01 16:05:38] 开始处理订单响应，响应类型: string
[2025-08-01 16:05:38] 检测到JSON响应，开始解析
[2025-08-01 16:05:38] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"b997a42e38934010b840eab8bb4105b3","orderId":"P1010129058031250801160538000001"}}
[2025-08-01 16:05:38] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=b997a42e38934010b840eab8bb4105b3&orderId=P1010129058031250801160538000001
[2025-08-01 16:05:38] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=b997a42e38934010b840eab8bb4105b3&orderId=P1010129058031250801160538000001
[2025-08-01 16:05:38] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=b997a42e38934010b840eab8bb4105b3&orderId=P1010129058031250801160538000001
[2025-08-01 16:05:38] 提取参数成功 - orderToken: b997a42e38934010b840eab8bb4105b3, orderId: P1010129058031250801160538000001
[2025-08-01 16:05:38] 步骤1: 访问支付页面获取payToken
[2025-08-01 16:05:38] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=b997a42e38934010b840eab8bb4105b3&orderId=P1010129058031250801160538000001, 方法: GET
[2025-08-01 16:05:38] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-08-01 16:05:38] 成功提取payToken: ee37ef78368847bc8034a7051278dc22
[2025-08-01 16:05:38] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-08-01 16:05:38] 第一次请求参数: {"orderId":"P1010129058031250801160538000001","orderToken":"b997a42e38934010b840eab8bb4105b3","payChannelId":"3","payToken":"ee37ef78368847bc8034a7051278dc22","routerFlg":"1"}
[2025-08-01 16:05:38] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 16:05:38] 请求数据: {"orderId":"P1010129058031250801160538000001","orderToken":"b997a42e38934010b840eab8bb4105b3","payChannelId":"3","payToken":"ee37ef78368847bc8034a7051278dc22","routerFlg":"1"}
[2025-08-01 16:05:38] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-08-01 16:05:38] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-08-01 16:05:38] 第一次请求成功，获得最终payChannelId: 10003
[2025-08-01 16:05:38] 步骤3: 完成支付流程，payChannelId: 10003
[2025-08-01 16:05:38] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 16:05:38] 请求数据: {"orderId":"P1010129058031250801160538000001","orderToken":"b997a42e38934010b840eab8bb4105b3","payChannelId":10003,"payToken":"ee37ef78368847bc8034a7051278dc22"}
[2025-08-01 16:05:39] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-08-01 16:05:39] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=08998c7cc7180c098fd39d30f983c180&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"08998c7cc7180c098fd39d30f983c180\"}},\"captchaType\":4}"}}
[2025-08-01 16:05:39] SDO支付流程失败: 无法获取最终支付URL: 
[2025-08-01 16:05:39] extractQrcodeFromPaymentUrl返回结果类型: string
[2025-08-01 16:05:39] 返回字符串内容: https://pay.sdo.com/cashier/pay?orderToken=b997a42e38934010b840eab8bb4105b3&orderId=P1010129058031250801160538000001
[2025-08-01 16:05:39] 返回原始支付页面URL: https://pay.sdo.com/cashier/pay?orderToken=b997a42e38934010b840eab8bb4105b3&orderId=P1010129058031250801160538000001
[2025-08-01 16:21:21] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-08-01 16:21:21] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-08-01 16:21:21] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-08-01 16:21:21] 充值页面访问成功，页面长度: 27735
[2025-08-01 16:21:21] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-08-01 16:21:21] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-08-01 16:21:21] 成功提取token - itemToken: b6d9a28e7e69477a97e304a5a63b7735, captchaToken: fe470ff3a6c64f16ad7b2a8f5d5a987e
[2025-08-01 16:21:21] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-08-01 16:21:21] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"fe470ff3a6c64f16ad7b2a8f5d5a987e","itemToken":"b6d9a28e7e69477a97e304a5a63b7735","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 16:21:21] 开始提交充值订单
[2025-08-01 16:21:21] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-08-01 16:21:21] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"fe470ff3a6c64f16ad7b2a8f5d5a987e","itemToken":"b6d9a28e7e69477a97e304a5a63b7735","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 16:21:22] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-08-01 16:21:22] 订单提交完成，响应类型: string
[2025-08-01 16:21:22] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"ef8ea758692c4e818f33f87e02378847","orderId":"P1010129058032250801162122000001"}}
[2025-08-01 16:21:22] 开始处理订单响应，支付方式: alipay
[2025-08-01 16:21:22] 开始处理订单响应，响应类型: string
[2025-08-01 16:21:22] 检测到JSON响应，开始解析
[2025-08-01 16:21:22] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"ef8ea758692c4e818f33f87e02378847","orderId":"P1010129058032250801162122000001"}}
[2025-08-01 16:21:22] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=ef8ea758692c4e818f33f87e02378847&orderId=P1010129058032250801162122000001
[2025-08-01 16:21:22] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=ef8ea758692c4e818f33f87e02378847&orderId=P1010129058032250801162122000001
[2025-08-01 16:21:22] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=ef8ea758692c4e818f33f87e02378847&orderId=P1010129058032250801162122000001
[2025-08-01 16:21:22] 提取参数成功 - orderToken: ef8ea758692c4e818f33f87e02378847, orderId: P1010129058032250801162122000001
[2025-08-01 16:21:22] 步骤1: 访问支付页面获取payToken
[2025-08-01 16:21:22] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=ef8ea758692c4e818f33f87e02378847&orderId=P1010129058032250801162122000001, 方法: GET
[2025-08-01 16:21:22] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-08-01 16:21:22] 成功提取payToken: 5564f2e1231b464eae191d3193e4da74
[2025-08-01 16:21:22] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-08-01 16:21:22] 第一次请求参数: {"orderId":"P1010129058032250801162122000001","orderToken":"ef8ea758692c4e818f33f87e02378847","payChannelId":"3","payToken":"5564f2e1231b464eae191d3193e4da74","routerFlg":"1"}
[2025-08-01 16:21:22] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 16:21:22] 请求数据: {"orderId":"P1010129058032250801162122000001","orderToken":"ef8ea758692c4e818f33f87e02378847","payChannelId":"3","payToken":"5564f2e1231b464eae191d3193e4da74","routerFlg":"1"}
[2025-08-01 16:21:22] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-08-01 16:21:22] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-08-01 16:21:22] 第一次请求成功，获得最终payChannelId: 10003
[2025-08-01 16:21:22] 步骤3: 完成支付流程，payChannelId: 10003
[2025-08-01 16:21:22] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 16:21:22] 请求数据: {"orderId":"P1010129058032250801162122000001","orderToken":"ef8ea758692c4e818f33f87e02378847","payChannelId":10003,"payToken":"5564f2e1231b464eae191d3193e4da74"}
[2025-08-01 16:21:23] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-08-01 16:21:23] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=db2aaf7ae83b0160d4fa003511dc9303&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"db2aaf7ae83b0160d4fa003511dc9303\"}},\"captchaType\":4}"}}
[2025-08-01 16:21:23] SDO支付流程失败: 无法获取最终支付URL: 
[2025-08-01 16:21:23] extractQrcodeFromPaymentUrl返回结果类型: string
[2025-08-01 16:21:23] 返回字符串内容: https://pay.sdo.com/cashier/pay?orderToken=ef8ea758692c4e818f33f87e02378847&orderId=P1010129058032250801162122000001
[2025-08-01 16:21:23] 返回原始支付页面URL: https://pay.sdo.com/cashier/pay?orderToken=ef8ea758692c4e818f33f87e02378847&orderId=P1010129058032250801162122000001
[2025-08-01 16:22:56] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-08-01 16:22:56] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-08-01 16:22:56] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-08-01 16:22:56] 充值页面访问成功，页面长度: 27735
[2025-08-01 16:22:56] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-08-01 16:22:56] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-08-01 16:22:56] 成功提取token - itemToken: 5c745111e9e842cf9b251ec55d54185e, captchaToken: 00a0e9edfe934bbaba96a8092e5b225a
[2025-08-01 16:22:56] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-08-01 16:22:56] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"00a0e9edfe934bbaba96a8092e5b225a","itemToken":"5c745111e9e842cf9b251ec55d54185e","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 16:22:56] 开始提交充值订单
[2025-08-01 16:22:56] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-08-01 16:22:56] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"00a0e9edfe934bbaba96a8092e5b225a","itemToken":"5c745111e9e842cf9b251ec55d54185e","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 16:22:56] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-08-01 16:22:56] 订单提交完成，响应类型: string
[2025-08-01 16:22:56] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"0999c631932b4766bda71d14797de644","orderId":"P1010129058031250801162257000001"}}
[2025-08-01 16:22:56] 开始处理订单响应，支付方式: alipay
[2025-08-01 16:22:56] 开始处理订单响应，响应类型: string
[2025-08-01 16:22:56] 检测到JSON响应，开始解析
[2025-08-01 16:22:56] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"0999c631932b4766bda71d14797de644","orderId":"P1010129058031250801162257000001"}}
[2025-08-01 16:22:56] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=0999c631932b4766bda71d14797de644&orderId=P1010129058031250801162257000001
[2025-08-01 16:22:56] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=0999c631932b4766bda71d14797de644&orderId=P1010129058031250801162257000001
[2025-08-01 16:22:56] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=0999c631932b4766bda71d14797de644&orderId=P1010129058031250801162257000001
[2025-08-01 16:22:56] 提取参数成功 - orderToken: 0999c631932b4766bda71d14797de644, orderId: P1010129058031250801162257000001
[2025-08-01 16:22:56] 步骤1: 访问支付页面获取payToken
[2025-08-01 16:22:56] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=0999c631932b4766bda71d14797de644&orderId=P1010129058031250801162257000001, 方法: GET
[2025-08-01 16:22:57] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-08-01 16:22:57] 成功提取payToken: 8def3aa131b24d39a67974f9ea9eecdb
[2025-08-01 16:22:57] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-08-01 16:22:57] 第一次请求参数: {"orderId":"P1010129058031250801162257000001","orderToken":"0999c631932b4766bda71d14797de644","payChannelId":"3","payToken":"8def3aa131b24d39a67974f9ea9eecdb","routerFlg":"1"}
[2025-08-01 16:22:57] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 16:22:57] 请求数据: {"orderId":"P1010129058031250801162257000001","orderToken":"0999c631932b4766bda71d14797de644","payChannelId":"3","payToken":"8def3aa131b24d39a67974f9ea9eecdb","routerFlg":"1"}
[2025-08-01 16:22:57] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-08-01 16:22:57] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-08-01 16:22:57] 第一次请求成功，获得最终payChannelId: 10003
[2025-08-01 16:22:57] 步骤3: 完成支付流程，payChannelId: 10003
[2025-08-01 16:22:57] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 16:22:57] 请求数据: {"orderId":"P1010129058031250801162257000001","orderToken":"0999c631932b4766bda71d14797de644","payChannelId":10003,"payToken":"8def3aa131b24d39a67974f9ea9eecdb"}
[2025-08-01 16:22:57] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-08-01 16:22:57] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=39f26fa42454246c2a7cd309c9bcb823&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"39f26fa42454246c2a7cd309c9bcb823\"}},\"captchaType\":4}"}}
[2025-08-01 16:22:57] SDO支付流程失败: 无法获取最终支付URL: 
[2025-08-01 16:22:57] extractQrcodeFromPaymentUrl返回结果类型: string
[2025-08-01 16:22:57] 返回字符串内容: https://pay.sdo.com/cashier/pay?orderToken=0999c631932b4766bda71d14797de644&orderId=P1010129058031250801162257000001
[2025-08-01 16:22:57] 返回原始支付页面URL: https://pay.sdo.com/cashier/pay?orderToken=0999c631932b4766bda71d14797de644&orderId=P1010129058031250801162257000001
[2025-08-01 16:23:59] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-08-01 16:23:59] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-08-01 16:23:59] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-08-01 16:23:59] 充值页面访问成功，页面长度: 27735
[2025-08-01 16:23:59] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-08-01 16:23:59] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-08-01 16:23:59] 成功提取token - itemToken: 1ce61607efa34daeae5a36b13740eebe, captchaToken: 44a53d0783734e768f5ab12735f6d10a
[2025-08-01 16:23:59] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-08-01 16:23:59] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"44a53d0783734e768f5ab12735f6d10a","itemToken":"1ce61607efa34daeae5a36b13740eebe","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 16:23:59] 开始提交充值订单
[2025-08-01 16:23:59] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-08-01 16:23:59] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"44a53d0783734e768f5ab12735f6d10a","itemToken":"1ce61607efa34daeae5a36b13740eebe","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 16:24:00] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-08-01 16:24:00] 订单提交完成，响应类型: string
[2025-08-01 16:24:00] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"e7bbe18b60154c0aa1df9e98fb07cd4d","orderId":"P1010127026170250801162400000001"}}
[2025-08-01 16:24:00] 开始处理订单响应，支付方式: alipay
[2025-08-01 16:24:00] 开始处理订单响应，响应类型: string
[2025-08-01 16:24:00] 检测到JSON响应，开始解析
[2025-08-01 16:24:00] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"e7bbe18b60154c0aa1df9e98fb07cd4d","orderId":"P1010127026170250801162400000001"}}
[2025-08-01 16:24:00] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=e7bbe18b60154c0aa1df9e98fb07cd4d&orderId=P1010127026170250801162400000001
[2025-08-01 16:24:00] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=e7bbe18b60154c0aa1df9e98fb07cd4d&orderId=P1010127026170250801162400000001
[2025-08-01 16:24:00] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=e7bbe18b60154c0aa1df9e98fb07cd4d&orderId=P1010127026170250801162400000001
[2025-08-01 16:24:00] 提取参数成功 - orderToken: e7bbe18b60154c0aa1df9e98fb07cd4d, orderId: P1010127026170250801162400000001
[2025-08-01 16:24:00] 步骤1: 访问支付页面获取payToken
[2025-08-01 16:24:00] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=e7bbe18b60154c0aa1df9e98fb07cd4d&orderId=P1010127026170250801162400000001, 方法: GET
[2025-08-01 16:24:00] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-08-01 16:24:00] 成功提取payToken: a212eb1adb674b65a15df7a01e19e20e
[2025-08-01 16:24:00] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-08-01 16:24:00] 第一次请求参数: {"orderId":"P1010127026170250801162400000001","orderToken":"e7bbe18b60154c0aa1df9e98fb07cd4d","payChannelId":"3","payToken":"a212eb1adb674b65a15df7a01e19e20e","routerFlg":"1"}
[2025-08-01 16:24:00] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 16:24:00] 请求数据: {"orderId":"P1010127026170250801162400000001","orderToken":"e7bbe18b60154c0aa1df9e98fb07cd4d","payChannelId":"3","payToken":"a212eb1adb674b65a15df7a01e19e20e","routerFlg":"1"}
[2025-08-01 16:24:00] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-08-01 16:24:00] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-08-01 16:24:00] 第一次请求成功，获得最终payChannelId: 10003
[2025-08-01 16:24:00] 步骤3: 完成支付流程，payChannelId: 10003
[2025-08-01 16:24:00] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 16:24:00] 请求数据: {"orderId":"P1010127026170250801162400000001","orderToken":"e7bbe18b60154c0aa1df9e98fb07cd4d","payChannelId":10003,"payToken":"a212eb1adb674b65a15df7a01e19e20e"}
[2025-08-01 16:24:00] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-08-01 16:24:00] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=4cb833ff9dade4b1784d17323ba03fc3&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"4cb833ff9dade4b1784d17323ba03fc3\"}},\"captchaType\":4}"}}
[2025-08-01 16:24:00] SDO支付流程失败: 无法获取最终支付URL: 
[2025-08-01 16:24:00] extractQrcodeFromPaymentUrl返回结果类型: string
[2025-08-01 16:24:00] 返回字符串内容: https://pay.sdo.com/cashier/pay?orderToken=e7bbe18b60154c0aa1df9e98fb07cd4d&orderId=P1010127026170250801162400000001
[2025-08-01 16:24:00] 返回原始支付页面URL: https://pay.sdo.com/cashier/pay?orderToken=e7bbe18b60154c0aa1df9e98fb07cd4d&orderId=P1010127026170250801162400000001
[2025-08-01 16:25:43] 开始访问充值页面: https://pay.sdo.com/item/GWPAY-*********
[2025-08-01 16:25:43] 发起HTTP请求 - URL: https://pay.sdo.com/item/GWPAY-*********, 方法: GET
[2025-08-01 16:25:43] HTTP响应 - 状态码: 200, 响应长度: 27735
[2025-08-01 16:25:43] 充值页面访问成功，页面长度: 27735
[2025-08-01 16:25:43] 开始提取表单数据，金额: 1.00, 区服: 1-盟重
[2025-08-01 16:25:43] 使用模式提取到itemToken: /value=["']([^"']+)["'][^>]*id=["']current_itemToken["']/
[2025-08-01 16:25:43] 成功提取token - itemToken: 7ea2df72637140e9b6e89c0da5da93dd, captchaToken: c184e5e3a65a4350933b7bc1e68815fa
[2025-08-01 16:25:43] 充值金额: 1.00元, 传奇币数量: 1个, chargeAmount: 116分
[2025-08-01 16:25:43] 表单数据提取完成: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"c184e5e3a65a4350933b7bc1e68815fa","itemToken":"7ea2df72637140e9b6e89c0da5da93dd","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 16:25:43] 开始提交充值订单
[2025-08-01 16:25:43] 发起HTTP请求 - URL: https://pay.sdo.com/order, 方法: POST
[2025-08-01 16:25:43] 请求数据: {"account":"***********","accountType":"0","productName":"传奇币","gameArea":"101","areaName":"1区","chargeAmount":"116","version":"***********","productId":"2609","appId":"*********","platformCode":"GWPAY","productParentId":"","captchaToken":"c184e5e3a65a4350933b7bc1e68815fa","itemToken":"7ea2df72637140e9b6e89c0da5da93dd","gameRole":"","ptNumId":"","gameid":"","extend":"1"}
[2025-08-01 16:25:44] HTTP响应 - 状态码: 200, 响应长度: 181
[2025-08-01 16:25:44] 订单提交完成，响应类型: string
[2025-08-01 16:25:44] 订单响应内容(前500字符): {"return_code":0,"return_message":"ok","data":{"url":"https://pay.sdo.com/cashier/pay","orderToken":"aceb5ec6de13443e8b83606047aa2ce6","orderId":"P1010127026170250801162544000001"}}
[2025-08-01 16:25:44] 开始处理订单响应，支付方式: alipay
[2025-08-01 16:25:44] 开始处理订单响应，响应类型: string
[2025-08-01 16:25:44] 检测到JSON响应，开始解析
[2025-08-01 16:25:44] JSON解析成功: {"return_code":0,"return_message":"ok","data":{"url":"https:\/\/pay.sdo.com\/cashier\/pay","orderToken":"aceb5ec6de13443e8b83606047aa2ce6","orderId":"P1010127026170250801162544000001"}}
[2025-08-01 16:25:44] 构造支付URL: https://pay.sdo.com/cashier/pay?orderToken=aceb5ec6de13443e8b83606047aa2ce6&orderId=P1010127026170250801162544000001
[2025-08-01 16:25:44] 支付链接提取完成: https://pay.sdo.com/cashier/pay?orderToken=aceb5ec6de13443e8b83606047aa2ce6&orderId=P1010127026170250801162544000001
[2025-08-01 16:25:44] 开始处理SDO支付流程，支付URL: https://pay.sdo.com/cashier/pay?orderToken=aceb5ec6de13443e8b83606047aa2ce6&orderId=P1010127026170250801162544000001
[2025-08-01 16:25:44] 提取参数成功 - orderToken: aceb5ec6de13443e8b83606047aa2ce6, orderId: P1010127026170250801162544000001
[2025-08-01 16:25:44] 步骤1: 访问支付页面获取payToken
[2025-08-01 16:25:44] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/pay?orderToken=aceb5ec6de13443e8b83606047aa2ce6&orderId=P1010127026170250801162544000001, 方法: GET
[2025-08-01 16:25:44] HTTP响应 - 状态码: 200, 响应长度: 38764
[2025-08-01 16:25:44] 成功提取payToken: a3d93ff2be4241e1a53af7bb6dd5851d
[2025-08-01 16:25:44] 步骤2: 第一次请求 - 选择支付方式 alipay
[2025-08-01 16:25:44] 第一次请求参数: {"orderId":"P1010127026170250801162544000001","orderToken":"aceb5ec6de13443e8b83606047aa2ce6","payChannelId":"3","payToken":"a3d93ff2be4241e1a53af7bb6dd5851d","routerFlg":"1"}
[2025-08-01 16:25:44] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 16:25:44] 请求数据: {"orderId":"P1010127026170250801162544000001","orderToken":"aceb5ec6de13443e8b83606047aa2ce6","payChannelId":"3","payToken":"a3d93ff2be4241e1a53af7bb6dd5851d","routerFlg":"1"}
[2025-08-01 16:25:44] HTTP响应 - 状态码: 200, 响应长度: 70
[2025-08-01 16:25:44] 第一次请求响应: {"return_code":0,"return_message":"ok","data":[],"payChannelId":10003}
[2025-08-01 16:25:44] 第一次请求成功，获得最终payChannelId: 10003
[2025-08-01 16:25:44] 步骤3: 完成支付流程，payChannelId: 10003
[2025-08-01 16:25:44] 发起HTTP请求 - URL: https://pay.sdo.com/cashier/go, 方法: POST
[2025-08-01 16:25:44] 请求数据: {"orderId":"P1010127026170250801162544000001","orderToken":"aceb5ec6de13443e8b83606047aa2ce6","payChannelId":10003,"payToken":"a3d93ff2be4241e1a53af7bb6dd5851d"}
[2025-08-01 16:25:45] HTTP响应 - 状态码: 200, 响应长度: 425
[2025-08-01 16:25:45] 完成支付响应: {"return_code":-10220288,"return_message":"","data":{"captchaInfo":"{\"captchaParams\":{\"gtData\":{\"gt\":\"31cc9ac8ae5eb9ef1aeaee9110bfc50c\",\"success\":1,\"gt_url\":\"https:\/\/login.u.sdo.com\/other\/gtest?gt=31cc9ac8ae5eb9ef1aeaee9110bfc50c&challenge=db3527f7f330d948773c59e2ef82c430&offline=false&new_captcha=true&callfunction=\",\"new_captcha\":1,\"challenge\":\"db3527f7f330d948773c59e2ef82c430\"}},\"captchaType\":4}"}}
[2025-08-01 16:25:45] SDO支付流程失败: 无法获取最终支付URL: 
[2025-08-01 16:25:45] extractQrcodeFromPaymentUrl返回结果类型: string
[2025-08-01 16:25:45] 返回字符串内容: https://pay.sdo.com/cashier/pay?orderToken=aceb5ec6de13443e8b83606047aa2ce6&orderId=P1010127026170250801162544000001
[2025-08-01 16:25:45] 返回原始支付页面URL: https://pay.sdo.com/cashier/pay?orderToken=aceb5ec6de13443e8b83606047aa2ce6&orderId=P1010127026170250801162544000001
