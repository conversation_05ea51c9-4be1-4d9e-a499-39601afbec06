﻿ html{-webkit-text-size-adjust:none}body{color:#000;font-family:Verdana,Arial,Helvetica,sans-serif}a{outline:0;text-decoration:none}a:hover{text-decoration:underline}html{zoom:1}html *{outline:0;zoom:1}html button::-moz-focus-inner{border-color:transparent!important}body{overflow-x:hidden;font-size:12px}body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,code,form,fieldset,legend,input,textarea,p,blockquote,th,td{margin:0;padding:0}table{}fieldset,a img{border:0}address,caption,cite,code,dfn,em,th,var{font-style:normal;font-weight:400}li{list-style:none}caption,th{text-align:left}h1,h2,h3,h4,h5,h6{font-size:100%;font-weight:400}q:before,q:after{content:''}input[type=submit],input[type=reset],input[type=button],button{-webkit-appearance:none}em,i{font-style:normal}.clearfix:after{content:".";display:block;height:0;clear:both;visibility:hidden}.clearfix{display:block}.clear{clear:both}.colwrapper{overflow:hidden;zoom:1;margin:5px auto}.strong{font-weight:700}.left{float:left}.right{float:right}.center{margin:0 auto;text-align:center}.show{display:block;visibility:visible}.hide{display:none;visibility:hidden}.block{display:block}.inline{display:inline}.break{word-wrap:break-word;overflow:hidden}.tal{text-align:left}.tar{text-align:right}.justify{text-align:justify;text-justify:distribute-all-lines;text-align-last:justify;-moz-text-align-last:justify;-webkit-text-align-last:justify}.toe{word-break:keep-all;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}@media screen and (-webkit-min-device-pixel-ratio:0){.justify:after{content:".";display:inline-block;width:100%;overflow:hidden;height:0}}a{color:#5d5d5e}a:hover{-webkit-transition:all .5s;-moz-transition:all .5s;transition:all .5s}body{font-family:microsoft yahei,sans-serif}.css3{-webkit-transform:translate3d(0,-20px,0);-ms-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0);-webkit-transition-property:opacity,-webkit-transform;transition-property:opacity,transform;-webkit-transition-duration:1000ms;transition-duration:1000ms;-webkit-transition-timing-function:cubic-bezier(.25,.46,.33,.98);transition-timing-function:cubic-bezier(.25,.46,.33,.98);-webkit-transition-delay:800ms;transition-delay:800ms}.css3.animated{-webkit-transform:translate3d(0,0,0);-ms-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}@media screen and (max-width:650px){}@media screen and (max-width:480px){}body{font-family:Microsoft Yahei,Helvetica,Neue,Helvetica,Arial,sans-serif;-webkit-text-size-adjust:100%;-webkit-overflow-scrolling:touch;overflow-scrolling:touch}