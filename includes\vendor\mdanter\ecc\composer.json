{"name": "mdanter/ecc", "description": "PHP Elliptic Curve Cryptography library", "type": "library", "homepage": "https://github.com/phpecc/phpecc", "keywords": ["secp256k1", "secp256r1", "nistp192", "nistp224", "nistp256", "nistp384", "nistp521", "ECDSA", "diffie", "hellman", "ECDH", "elliptic", "curve", "phpecc"], "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "homepage": "http://matejdanter.com/", "role": "Author"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://aztech.io", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Maintainer"}], "require": {"php": "^7.0||^8.0", "ext-gmp": "*", "fgrosse/phpasn1": "^2.0"}, "require-dev": {"phpunit/phpunit": "^6.0||^8.0||^9.0", "squizlabs/php_codesniffer": "^2.0", "symfony/yaml": "^2.6|^3.0"}, "autoload": {"psr-4": {"Mdanter\\Ecc\\": "src/"}}, "autoload-dev": {"psr-4": {"Mdanter\\Ecc\\Tests\\": "tests/unit", "Mdanter\\Ecc\\WycheProof\\": "tests/wycheproof"}}}