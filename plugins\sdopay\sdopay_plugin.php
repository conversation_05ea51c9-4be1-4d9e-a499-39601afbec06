<?php
/**
 * SDO游戏充值支付插件
 * 基于kspay插件结构重新设计
 * 支持传奇新百区等SDO游戏的充值功能
 */

class sdopay_plugin
{
    /**
     * 写入日志到插件目录
     */
    static private function writeLog($message)
    {
        $logFile = __DIR__ . '/debug.log';
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] {$message}" . PHP_EOL;
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
    
    static public $info = [
        'name' => 'sdopay',
        'showname' => 'SDO游戏充值',
        'author' => 'AI Assistant',
        'link' => 'https://pay.sdo.com/',
        'types' => ['alipay', 'wxpay'],
        'inputs' => [
            'appurl' => [
                'name' => 'SDO登录Cookie',
                'type' => 'textarea',
                'note' => '登录SDO后复制浏览器中的Cookie',
            ],
            'appid' => [
                'name' => '游戏ID',
                'type' => 'input',
                'note' => '默认：GWPAY-*********（传奇新百区）',
            ],
            'appsecret' => [
                'name' => '游戏区服',
                'type' => 'input',
                'note' => '格式：1-盟重、2-比奇等',
            ],
        ],
        'select' => null,
        'note' => '请先登录SDO账户，然后复制浏览器中的Cookie。支付金额将自动转换为对应的游戏币数量。',
        'bindwxmp' => false,
        'bindwxa' => false,
    ];

    static public function submit()
    {
        global $order, $sitename;
        return ['type' => 'jump', 'url' => '/pay/' . $order['typename'] . '/' . TRADE_NO . '/?sitename=' . $sitename];
    }

    static public function mapi()
    {
        global $order;
        $typename = $order['typename'];
        return self::$typename();
    }

    // 支付宝下单
    static public function alipay()
    {
        $code_url = self::qrcode();
        if (is_array($code_url)) {
            return $code_url;
        } else {
            return ['type' => 'qrcode', 'page' => 'alipay_qrcode', 'url' => $code_url];
        }
    }

    // 微信下单
    static public function wxpay()
    {
        $code_url = self::qrcode();
        if (is_array($code_url)) {
            return $code_url;
        } else {
            return ['type' => 'qrcode', 'page' => 'wxpay_qrcode', 'url' => $code_url];
        }
    }

    static public function qrcode()
    {
        global $channel, $order, $ordername, $DB;

        // 获取配置
        $cookie = $channel['appurl'];
        $gameId = $channel['appid'] ?: 'GWPAY-*********';
        $gameServer = $channel['appsecret'] ?: '1-盟重';
        $amount = $order['money'];

        // 验证Cookie
        if (empty($cookie)) {
            throw new Exception('Cookie配置不能为空，请先登录SDO账户并配置Cookie');
        }

        // 解析游戏区服
        $serverParts = explode('-', $gameServer);
        $areaId = isset($serverParts[0]) ? intval($serverParts[0]) : 1;
        $serverName = isset($serverParts[1]) ? $serverParts[1] : '盟重';

        try {
            // 步骤1：访问充值页面，验证登录状态
            $gameUrl = 'https://pay.sdo.com/item/' . $gameId;
            self::writeLog('开始访问充值页面: ' . $gameUrl);
            $gamePageResponse = self::curlRequest($gameUrl, 'GET', $cookie);

            if (!$gamePageResponse || strpos($gamePageResponse, '请登录') !== false) {
                self::writeLog('Cookie验证失败，页面包含登录提示');
                throw new Exception('Cookie已过期或无效，请重新登录SDO账户');
            }
            
            self::writeLog('充值页面访问成功，页面长度: ' . strlen($gamePageResponse));

            // 步骤2：提取页面中的必要参数
            self::writeLog('开始提取表单数据，金额: ' . $amount . ', 区服: ' . $areaId . '-' . $serverName);
            $formData = self::extractFormData($gamePageResponse, $amount, $areaId, $serverName);
            self::writeLog('表单数据提取完成: ' . json_encode($formData, JSON_UNESCAPED_UNICODE));
            
            // 步骤3：提交充值订单
            self::writeLog('开始提交充值订单');
            $orderResult = self::submitOrder($formData, $cookie);
            self::writeLog('订单提交完成，响应类型: ' . gettype($orderResult));
            
            if (is_string($orderResult)) {
                self::writeLog('订单响应内容(前500字符): ' . substr($orderResult, 0, 500));
            } elseif (is_array($orderResult)) {
                self::writeLog('订单响应数组: ' . json_encode($orderResult, JSON_UNESCAPED_UNICODE));
            }
            
            // 步骤4：处理订单响应，获取支付链接
            self::writeLog('开始处理订单响应，支付方式: ' . $order['typename']);
            $paymentUrl = self::processOrderResponse($orderResult, $cookie, $order['typename']);
            self::writeLog('支付链接提取完成: ' . $paymentUrl);
            
            // 步骤5：返回支付结果
            return self::formatPaymentResult($paymentUrl, $order['typename']);

        } catch (Exception $e) {
            // 记录详细错误信息
            self::writeLog('SDO支付插件错误: ' . $e->getMessage());
            self::writeLog('SDO支付插件错误堆栈: ' . $e->getTraceAsString());
            throw new Exception('SDO支付创建失败: ' . $e->getMessage());
        }
    }

    // 根据服务器名称获取服务器ID
    static private function getServerIdByName($serverName)
    {
        // 服务器名称到ID的映射
        $serverMap = [
            '盟重' => '101',
            '比奇' => '102',
            '沙巴克' => '103',
            '祖玛' => '104',
            '赤月' => '105',
            '白日门' => '106',
            '封魔谷' => '107',
            '幻境' => '108',
            '新手村' => '109',
            '毒蛇山谷' => '110'
        ];
        return isset($serverMap[$serverName]) ? $serverMap[$serverName] : '101';
    }

    // 请求方法 - 支持GZIP解压缩
    static private function curlRequest($url, $method = 'GET', $cookie = '', $data = [])
    {
        self::writeLog('发起HTTP请求 - URL: ' . $url . ', 方法: ' . $method);
        if (!empty($data)) {
            self::writeLog('请求数据: ' . json_encode($data, JSON_UNESCAPED_UNICODE));
        }
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false); // 手动处理重定向
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_ENCODING, 'gzip, deflate'); // 支持压缩

        if (!empty($cookie)) {
            curl_setopt($ch, CURLOPT_COOKIE, $cookie);
        }

        $headers = [
            'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding: gzip, deflate, br',
            'Connection: keep-alive',
            'Upgrade-Insecure-Requests: 1',
        ];

        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            $headers[] = 'Content-Type: application/x-www-form-urlencoded';
            $headers[] = 'Origin: https://pay.sdo.com';
            $headers[] = 'Referer: https://pay.sdo.com/item/GWPAY-*********';
        }

        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception('HTTP请求失败: ' . $error);
        }

        // 处理重定向
        if ($httpCode >= 300 && $httpCode < 400) {
            return [
                'body' => $response,
                'httpCode' => $httpCode,
                'redirected' => true
            ];
        }

        // 记录响应信息
        self::writeLog('HTTP响应 - 状态码: ' . $httpCode . ', 响应长度: ' . strlen($response));
        if ($httpCode >= 400) {
            self::writeLog('HTTP错误响应: ' . substr($response, 0, 500));
        }

        // 检查响应中是否包含JavaScript重定向
        if ($method === 'POST' && is_string($response)) {
            if (preg_match('/window\.location\.href\s*=\s*["\']([^"\']+)["\']/', $response, $matches)) {
                self::writeLog('检测到JavaScript重定向: ' . $matches[1]);
                return [
                    'body' => $response,
                    'finalUrl' => $matches[1],
                    'httpCode' => $httpCode,
                    'redirected' => true
                ];
            }

            // 检查表单自动提交
            if (preg_match('/<form[^>]*action=["\']([^"\']*)["\'][^>]*>.*?<\/form>/s', $response, $matches)) {
                if (strpos($matches[1], 'cashier') !== false || strpos($matches[1], 'pay') !== false) {
                    self::writeLog('检测到支付表单重定向: ' . $matches[1]);
                    return [
                        'body' => $response,
                        'finalUrl' => $matches[1],
                        'httpCode' => $httpCode,
                        'redirected' => true
                    ];
                }
            }
        }

        return $response;
    }

    /**
     * 提取表单数据
     */
    static private function extractFormData($pageContent, $amount, $areaId, $serverName)
    {
        // 提取必要的token
        $itemToken = '';
        $captchaToken = '';
        $version = '';
        $appId = '';
        $platformCode = '';

        // 增强的itemToken提取 - 多种模式
        $itemTokenPatterns = [
            '/value=["\']([^"\']+)["\'][^>]*id=["\']current_itemToken["\']/',
            '/id=["\']current_itemToken["\'][^>]*value=["\']([^"\']+)["\']/',
            '/itemToken["\']?\s*[:=]\s*["\']([^"\']+)["\']/',
            '/current_itemToken["\']?\s*[:=]\s*["\']([^"\']+)["\']/',
            '/"itemToken"\s*:\s*"([^"]+)"/',
            '/itemToken\s*=\s*["\']([^"\']+)["\']/',
            '/name=["\']itemToken["\'][^>]*value=["\']([^"\']+)["\']/',
            '/data-item-token=["\']([^"\']+)["\']/'
        ];

        foreach ($itemTokenPatterns as $pattern) {
            if (preg_match($pattern, $pageContent, $matches)) {
                $itemToken = $matches[1];
                self::writeLog('使用模式提取到itemToken: ' . $pattern);
                break;
            }
        }

        // 增强的captchaToken提取
        $captchaTokenPatterns = [
            '/value=["\']([^"\']+)["\'][^>]*id=["\']current_captchaToken["\']/',
            '/id=["\']current_captchaToken["\'][^>]*value=["\']([^"\']+)["\']/',
            '/captchaToken["\']?\s*[:=]\s*["\']([^"\']+)["\']/',
            '/"captchaToken"\s*:\s*"([^"]+)"/',
            '/name=["\']captchaToken["\'][^>]*value=["\']([^"\']+)["\']/'
        ];

        foreach ($captchaTokenPatterns as $pattern) {
            if (preg_match($pattern, $pageContent, $matches)) {
                $captchaToken = $matches[1];
                break;
            }
        }

        // 提取其他参数
        if (preg_match('/id=["\']current_version["\'][^>]*value=["\']([^"\']+)["\']/', $pageContent, $matches)) {
            $version = $matches[1];
        }
        if (preg_match('/id=["\']current_appId["\'][^>]*value=["\']([^"\']+)["\']/', $pageContent, $matches)) {
            $appId = $matches[1];
        }
        if (preg_match('/id=["\']current_platformCode["\'][^>]*value=["\']([^"\']+)["\']/', $pageContent, $matches)) {
            $platformCode = $matches[1];
        }

        if (empty($itemToken)) {
            self::writeLog('无法提取itemToken，页面内容片段: ' . substr($pageContent, 0, 1000));
            self::writeLog('页面长度: ' . strlen($pageContent));

            // 检查页面是否为空
            if (empty($pageContent) || strlen($pageContent) < 100) {
                throw new Exception('Cookie已过期或无效，请重新获取Cookie');
            }

            // 检查是否是登录页面
            if (strpos($pageContent, '请登录') !== false ||
                strpos($pageContent, 'login') !== false ||
                strpos($pageContent, '登录') !== false ||
                strpos($pageContent, 'passport.sdo.com') !== false) {
                throw new Exception('Cookie已过期，需要重新登录SDO账户');
            }

            // 检查是否是错误页面
            if (strpos($pageContent, '404') !== false ||
                strpos($pageContent, 'error') !== false ||
                strpos($pageContent, '页面不存在') !== false) {
                throw new Exception('页面访问错误，可能是游戏ID不正确');
            }

            // 检查是否是乱码内容
            if (self::isGarbageResponse($pageContent)) {
                throw new Exception('页面返回乱码，Cookie可能已过期');
            }

            // 检查是否包含SDO相关内容
            if (strpos($pageContent, 'sdo.com') === false &&
                strpos($pageContent, '盛大') === false &&
                strpos($pageContent, '充值') === false) {
                throw new Exception('返回的页面不是SDO充值页面，Cookie可能已过期');
            }

            throw new Exception('无法获取itemToken，页面格式可能已变更，请检查Cookie是否有效');
        }

        self::writeLog('成功提取token - itemToken: ' . $itemToken . ', captchaToken: ' . $captchaToken);

        // 将金额转换为传奇币数量，然后计算对应的分值
        $coinAmount = self::convertAmountToCoinCount($amount);
        $chargeAmountInCents = $coinAmount * 116; // 1个传奇币 = 116分
        self::writeLog('充值金额: ' . $amount . '元, 传奇币数量: ' . $coinAmount . '个, chargeAmount: ' . $chargeAmountInCents . '分');

        // 构造表单数据
        return [
            'account' => '***********', // 固定账号
            'accountType' => '0',
            'productName' => '传奇币',
            'gameArea' => self::getServerIdByName($serverName),
            'areaName' => $areaId . '区',
            'chargeAmount' => strval($chargeAmountInCents), // 传递以分为单位的金额
            'version' => $version ?: '***********',
            'productId' => '2609',
            'appId' => $appId ?: '*********',
            'platformCode' => $platformCode ?: 'GWPAY',
            'productParentId' => '',
            'captchaToken' => $captchaToken,
            'itemToken' => $itemToken,
            'gameRole' => '',
            'ptNumId' => '',
            'gameid' => '',
            'extend' => '1'
        ];
    }

    /**
     * 提交订单
     */
    static private function submitOrder($formData, $cookie)
    {
        $submitUrl = 'https://pay.sdo.com/order';
        $response = self::curlRequest($submitUrl, 'POST', $cookie, $formData);

        if (!$response) {
            throw new Exception('订单提交失败，无响应');
        }

        return $response;
    }

    /**
     * 处理订单响应
     */
    static private function processOrderResponse($response, $cookie, $paymentType)
    {
        self::writeLog('开始处理订单响应，响应类型: ' . gettype($response));

        // 如果是JSON响应
        if (is_string($response) && (strpos($response, '{') === 0 || strpos($response, '[') === 0)) {
            self::writeLog('检测到JSON响应，开始解析');
            $data = json_decode($response, true);
            if ($data) {
                self::writeLog('JSON解析成功: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

                // 检查是否是SDO错误响应
                if (isset($data['return_code']) && $data['return_code'] != 0) {
                    $errorMsg = 'SDO服务器返回错误: ';
                    $errorMsg .= '错误码: ' . $data['return_code'];
                    if (isset($data['return_message'])) {
                        $errorMsg .= ', 错误信息: ' . $data['return_message'];
                    }
                    if (isset($data['data']['msg'])) {
                        $errorMsg .= ', 详细信息: ' . $data['data']['msg'];
                    }
                    self::writeLog($errorMsg);
                    throw new Exception($errorMsg);
                }

                // 处理成功响应
                if (isset($data['data'])) {
                    if (isset($data['data']['payUrl'])) {
                        self::writeLog('找到payUrl: ' . $data['data']['payUrl']);
                        return $data['data']['payUrl'];
                    }
                    if (isset($data['data']['orderToken']) && isset($data['data']['orderId'])) {
                        $payUrl = 'https://pay.sdo.com/cashier/pay?orderToken=' . $data['data']['orderToken'] . '&orderId=' . $data['data']['orderId'];
                        self::writeLog('构造支付URL: ' . $payUrl);
                        return $payUrl;
                    }
                }
            } else {
                self::writeLog('JSON解析失败');
            }
        }

        // 如果是重定向响应
        if (is_array($response) && isset($response['finalUrl'])) {
            self::writeLog('检测到重定向响应: ' . $response['finalUrl']);
            return $response['finalUrl'];
        }

        // 如果是HTML页面，查找支付链接
        if (is_string($response)) {
            self::writeLog('检测到HTML响应，开始查找支付链接');

            // 查找JavaScript重定向
            if (preg_match('/window\.location\.href\s*=\s*["\']([^"\']+)["\']/', $response, $matches)) {
                $url = $matches[1];
                self::writeLog('找到JavaScript重定向: ' . $url);
                if (strpos($url, 'cashier') !== false || strpos($url, 'pay') !== false) {
                    self::writeLog('重定向URL包含支付关键词，返回: ' . $url);
                    return $url;
                }
            }

            // 查找支付链接
            if (preg_match('/https:\/\/[^"\'>\s]*(?:cashier|pay)[^"\'>\s]*/', $response, $matches)) {
                self::writeLog('找到支付链接: ' . $matches[0]);
                return $matches[0];
            }

            // 记录HTML内容的关键部分用于调试
            self::writeLog('HTML响应中未找到支付链接，内容片段: ' . substr($response, 0, 1000));
        }

        self::writeLog('所有方法都无法提取支付链接');
        throw new Exception('无法从订单响应中提取支付链接');
    }

    /**
     * 格式化支付结果
     */
    static private function formatPaymentResult($paymentUrl, $paymentType)
    {
        if (empty($paymentUrl)) {
            throw new Exception('支付链接为空');
        }

        // 处理完整的SDO支付流程，获取最终支付URL
        $finalPaymentUrl = self::extractQrcodeFromPaymentUrl($paymentUrl, $paymentType);

        self::writeLog('extractQrcodeFromPaymentUrl返回结果类型: ' . gettype($finalPaymentUrl));
        if (is_array($finalPaymentUrl)) {
            self::writeLog('返回数组内容: ' . json_encode($finalPaymentUrl, JSON_UNESCAPED_UNICODE));
        } else {
            self::writeLog('返回字符串内容: ' . $finalPaymentUrl);
        }



        if ($finalPaymentUrl && is_string($finalPaymentUrl) && $finalPaymentUrl !== $paymentUrl) {
            // 如果获取到了最终的支付URL（如支付宝URL），直接跳转
            self::writeLog('返回最终支付URL进行跳转: ' . $finalPaymentUrl);
            return ['type' => 'jump', 'url' => $finalPaymentUrl];
        } else {
            // 如果无法获取最终URL，返回原始支付页面让用户手动操作
            self::writeLog('返回原始支付页面URL: ' . $paymentUrl);
            return ['type' => 'jump', 'url' => $paymentUrl];
        }
    }

    /**
     * 从支付URL提取二维码 - 处理完整的SDO支付流程
     */
    static private function extractQrcodeFromPaymentUrl($paymentUrl, $paymentType)
    {
        global $channel;
        $cookie = $channel['appurl'];

        self::writeLog('开始处理SDO支付流程，支付URL: ' . $paymentUrl);

        try {
            // 从URL中提取orderToken和orderId
            parse_str(parse_url($paymentUrl, PHP_URL_QUERY), $params);
            $orderToken = $params['orderToken'] ?? '';
            $orderId = $params['orderId'] ?? '';

            if (empty($orderToken) || empty($orderId)) {
                throw new Exception('无法从支付URL中提取orderToken或orderId');
            }

            self::writeLog('提取参数成功 - orderToken: ' . $orderToken . ', orderId: ' . $orderId);

            // 步骤1：访问支付页面，获取payToken
            $payToken = self::getPayToken($paymentUrl, $cookie);

            // 步骤2：选择支付方式，获取payChannelId
            $payChannelId = self::selectPaymentMethod($orderToken, $orderId, $payToken, $paymentType, $cookie);

            // 步骤3：完成支付流程，获取最终支付URL
            $finalPaymentUrl = self::completePaymentFlow($orderToken, $orderId, $payToken, $payChannelId, $cookie);

            self::writeLog('SDO支付流程完成，最终支付URL: ' . $finalPaymentUrl);
            return $finalPaymentUrl;

        } catch (Exception $e) {
            self::writeLog('SDO支付流程失败: ' . $e->getMessage());
            // 如果流程失败，返回原始URL
            return $paymentUrl;
        }
    }



    /**
     * 将金额转换为传奇币数量
     * 根据SDO的充值规则：1.16元 = 1个传奇币
     */
    static private function convertAmountToCoinCount($amount)
    {
        // SDO传奇币充值比例：1.16元 = 1个传奇币
        $coinPrice = 1.16;

        // 计算传奇币数量
        $coinCount = round($amount / $coinPrice);

        // 确保至少充值1个传奇币
        if ($coinCount < 1) {
            $coinCount = 1;
        }

        // 检查是否是有效的充值数量（SDO支持的档位）
        $validAmounts = [1, 3, 5, 10, 30, 100, 300, 1000, 20000];

        // 找到最接近的有效数量
        $closestAmount = $validAmounts[0];
        $minDiff = abs($coinCount - $closestAmount);

        foreach ($validAmounts as $validAmount) {
            $diff = abs($coinCount - $validAmount);
            if ($diff < $minDiff) {
                $minDiff = $diff;
                $closestAmount = $validAmount;
            }
        }

        return $closestAmount;
    }

    /**
     * 获取payToken
     */
    static private function getPayToken($paymentUrl, $cookie)
    {
        self::writeLog('步骤1: 访问支付页面获取payToken');
        $response = self::curlRequest($paymentUrl, 'GET', $cookie);

        if (!$response) {
            throw new Exception('无法访问支付页面');
        }

        // 从页面中提取payToken
        if (preg_match('/payToken["\']?\s*[:=]\s*["\']([^"\']+)["\']/', $response, $matches)) {
            $payToken = $matches[1];
            self::writeLog('成功提取payToken: ' . $payToken);
            return $payToken;
        }

        // 尝试其他可能的提取方式
        if (preg_match('/value=["\']([^"\']+)["\'][^>]*name=["\']payToken["\']/', $response, $matches) ||
            preg_match('/name=["\']payToken["\'][^>]*value=["\']([^"\']+)["\']/', $response, $matches)) {
            $payToken = $matches[1];
            self::writeLog('成功提取payToken(方式2): ' . $payToken);
            return $payToken;
        }

        throw new Exception('无法从支付页面提取payToken');
    }

    /**
     * 选择支付方式 - 第一次请求
     */
    static private function selectPaymentMethod($orderToken, $orderId, $payToken, $paymentType, $cookie)
    {
        self::writeLog('步骤2: 第一次请求 - 选择支付方式 ' . $paymentType);

        // 根据支付类型确定payChannelId
        $payChannelId = ($paymentType === 'alipay') ? '3' : '4'; // 3=支付宝, 4=微信

        // 第一次请求：选择支付方式（带routerFlg）
        $postData = [
            'orderId' => $orderId,
            'orderToken' => $orderToken,
            'payChannelId' => $payChannelId,
            'payToken' => $payToken,
            'routerFlg' => '1'
        ];

        self::writeLog('第一次请求参数: ' . json_encode($postData, JSON_UNESCAPED_UNICODE));
        $response = self::curlRequest('https://pay.sdo.com/cashier/go', 'POST', $cookie, $postData);

        if (!$response) {
            throw new Exception('第一次请求失败');
        }

        $data = json_decode($response, true);
        if (!$data) {
            throw new Exception('第一次请求响应解析失败');
        }

        self::writeLog('第一次请求响应: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        if ($data['return_code'] == 0 && isset($data['payChannelId'])) {
            $finalPayChannelId = $data['payChannelId'];
            self::writeLog('第一次请求成功，获得最终payChannelId: ' . $finalPayChannelId);
            return $finalPayChannelId;
        }

        throw new Exception('第一次请求失败: ' . ($data['return_message'] ?? '未知错误'));
    }

    /**
     * 完成支付流程
     */
    static private function completePaymentFlow($orderToken, $orderId, $payToken, $payChannelId, $cookie)
    {
        self::writeLog('步骤3: 完成支付流程，payChannelId: ' . $payChannelId);

        // 第一次请求：尝试直接支付
        $postData = [
            'orderId' => $orderId,
            'orderToken' => $orderToken,
            'payChannelId' => $payChannelId,
            'payToken' => $payToken
        ];

        $response = self::curlRequest('https://pay.sdo.com/cashier/go', 'POST', $cookie, $postData);

        if (!$response) {
            throw new Exception('完成支付请求失败');
        }

        $data = json_decode($response, true);
        if (!$data) {
            throw new Exception('完成支付响应解析失败');
        }

        self::writeLog('完成支付响应: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        // 检查是否需要验证码
        if ($data['return_code'] == 0 && isset($data['data']['nextStepContent']['url'])) {
            $paymentUrl = $data['data']['nextStepContent']['url'];
            $queryString = $data['data']['nextStepContent']['queryString'] ?? '';

            if (!empty($queryString)) {
                $paymentUrl .= '?' . $queryString;
            }

            self::writeLog('成功获取最终支付URL: ' . $paymentUrl);
            return $paymentUrl;
        }

        // 检查是否需要验证码验证
        if (isset($data['data']['captchaInfo']) || $data['return_code'] == -10220288) {
            self::writeLog('检测到需要验证码验证，return_code: ' . $data['return_code']);

            // 构造验证码页面URL，传递必要参数
            global $order;
            $captchaPageUrl = '/sdo_browser_captcha.php?' . http_build_query([
                'trade_no' => $order['trade_no'],
                'orderToken' => $orderToken,
                'orderId' => $orderId,
                'payToken' => $payToken,
                'payChannelId' => $payChannelId,
                'captchaInfo' => isset($data['data']['captchaInfo']) ? base64_encode($data['data']['captchaInfo']) : ''
            ]);

            self::writeLog('跳转到验证码页面: ' . $captchaPageUrl);
            return $captchaPageUrl;
        }

        // 检查是否有parentChannelId但没有captchaInfo的情况
        if (isset($data['data']['parentChannelId'])) {
            $parentChannelId = $data['data']['parentChannelId'];
            self::writeLog('检测到需要验证码验证，parentChannelId: ' . $parentChannelId);

            // 进行第三次请求，包含验证码信息
            return self::submitWithCaptcha($orderToken, $orderId, $payToken, $payChannelId, $parentChannelId, $cookie);
        }

        throw new Exception('无法获取最终支付URL: ' . ($data['return_message'] ?? '未知错误'));
    }

    /**
     * 第三次请求：提交验证码信息
     */
    static private function submitWithCaptcha($orderToken, $orderId, $payToken, $payChannelId, $parentChannelId, $cookie)
    {
        self::writeLog('步骤4: 提交验证码信息');

        // 获取极验验证码数据
        $captchaData = self::getGeetestCaptcha($cookie);

        // 构造captchaInfo参数
        $captchaInfo = [
            'picCode' => 'gtest',
            'gtData' => [
                'challenge' => $captchaData['challenge'],
                'validate' => $captchaData['validate'],
                'seccode' => $captchaData['seccode']
            ]
        ];

        // 第三次请求参数
        $postData = [
            'orderId' => $orderId,
            'orderToken' => $orderToken,
            'parentChannelId' => $parentChannelId,
            'payChannelId' => $payChannelId,
            'payToken' => $payToken,
            'captchaInfo' => json_encode($captchaInfo, JSON_UNESCAPED_UNICODE)
        ];

        self::writeLog('第三次请求参数: ' . json_encode($postData, JSON_UNESCAPED_UNICODE));

        $response = self::curlRequest('https://pay.sdo.com/cashier/go', 'POST', $cookie, $postData);

        if (!$response) {
            throw new Exception('验证码提交请求失败');
        }

        $data = json_decode($response, true);
        if (!$data) {
            throw new Exception('验证码提交响应解析失败');
        }

        self::writeLog('验证码提交响应: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        // 检查是否成功获取支付URL
        if ($data['return_code'] == 0 && isset($data['data']['nextStepContent']['url'])) {
            $paymentUrl = $data['data']['nextStepContent']['url'];
            $queryString = $data['data']['nextStepContent']['queryString'] ?? '';

            if (!empty($queryString)) {
                $paymentUrl .= '?' . $queryString;
            }

            self::writeLog('验证码验证成功，获取最终支付URL: ' . $paymentUrl);
            return $paymentUrl;
        }

        throw new Exception('验证码验证失败: ' . ($data['return_message'] ?? '未知错误'));
    }

    /**
     * 获取极验验证码数据
     */
    static private function getGeetestCaptcha($cookie)
    {
        self::writeLog('开始获取极验验证码数据');

        try {
            // 这里需要集成极验验证码的获取逻辑
            // 暂时使用模拟数据，后续需要实现真实的验证码获取
            $challenge = self::generateChallenge();
            $validate = self::generateValidate($challenge);
            $seccode = $validate . '|jordan';

            self::writeLog('生成验证码数据 - challenge: ' . $challenge . ', validate: ' . $validate);

            return [
                'challenge' => $challenge,
                'validate' => $validate,
                'seccode' => $seccode
            ];

        } catch (Exception $e) {
            self::writeLog('获取验证码数据失败: ' . $e->getMessage());
            throw new Exception('获取验证码数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成challenge值
     */
    static private function generateChallenge()
    {
        return md5(time() . rand(1000, 9999));
    }

    /**
     * 生成validate值
     */
    static private function generateValidate($challenge)
    {
        return md5($challenge . 'geetest');
    }

    /**
     * 带自定义请求头的CURL请求 - 修复Cookie过期和压缩问题
     */
    static private function curlRequestWithHeaders($url, $method = 'GET', $cookie = '', $data = null, $headers = [])
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 5);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        // 自动处理压缩内容
        curl_setopt($ch, CURLOPT_ENCODING, '');

        // 设置默认请求头
        $defaultHeaders = [
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding: gzip, deflate, br',
            'Connection: keep-alive',
            'Upgrade-Insecure-Requests: 1',
            'Cache-Control: no-cache',
            'Pragma: no-cache'
        ];

        // 合并自定义请求头
        if (!empty($headers)) {
            $defaultHeaders = array_merge($defaultHeaders, $headers);
        }

        // 确保有User-Agent
        $hasUserAgent = false;
        foreach ($defaultHeaders as $header) {
            if (stripos($header, 'User-Agent:') === 0) {
                $hasUserAgent = true;
                break;
            }
        }

        if (!$hasUserAgent) {
            $defaultHeaders[] = 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
        }

        curl_setopt($ch, CURLOPT_HTTPHEADER, $defaultHeaders);

        // 设置Cookie
        if (!empty($cookie)) {
            curl_setopt($ch, CURLOPT_COOKIE, $cookie);
        }

        // 设置POST数据
        if ($method === 'POST' && $data !== null) {
            curl_setopt($ch, CURLOPT_POST, true);
            if (is_array($data)) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            } else {
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            }
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception('CURL错误: ' . $error);
        }

        if ($httpCode >= 400) {
            throw new Exception('HTTP错误: ' . $httpCode);
        }

        // 检查响应是否为乱码（可能是Cookie过期）
        if (self::isGarbageResponse($response)) {
            throw new Exception('Cookie已过期或无效，请重新获取Cookie');
        }

        return $response;
    }

    /**
     * 检查响应是否为乱码
     */
    static private function isGarbageResponse($response)
    {
        // 检查是否包含大量非ASCII字符
        $nonAsciiCount = 0;
        $totalLength = strlen($response);

        if ($totalLength < 100) {
            return false; // 太短的响应不判断
        }

        for ($i = 0; $i < min($totalLength, 1000); $i++) {
            $char = ord($response[$i]);
            if ($char > 127 || ($char < 32 && $char != 9 && $char != 10 && $char != 13)) {
                $nonAsciiCount++;
            }
        }

        // 如果非ASCII字符超过30%，认为是乱码
        $ratio = $nonAsciiCount / min($totalLength, 1000);
        return $ratio > 0.3;
    }

    /**
     * 验证Cookie是否有效
     */
    static public function validateCookie($cookie, $gameId = 'GWPAY-*********')
    {
        try {
            $testUrl = 'https://pay.sdo.com/item/' . $gameId;
            $response = self::curlRequest($testUrl, 'GET', $cookie);

            // 检查是否需要登录
            if (strpos($response, '请登录') !== false || strpos($response, 'login') !== false) {
                return [
                    'valid' => false,
                    'message' => 'Cookie已过期，需要重新登录'
                ];
            }

            // 检查是否是乱码
            if (self::isGarbageResponse($response)) {
                return [
                    'valid' => false,
                    'message' => 'Cookie无效，返回乱码内容'
                ];
            }

            // 尝试提取itemToken
            $itemTokenPatterns = [
                '/value=["\']([^"\']+)["\'][^>]*id=["\']current_itemToken["\']/',
                '/id=["\']current_itemToken["\'][^>]*value=["\']([^"\']+)["\']/',
                '/itemToken["\']?\s*[:=]\s*["\']([^"\']+)["\']/'
            ];

            $hasItemToken = false;
            foreach ($itemTokenPatterns as $pattern) {
                if (preg_match($pattern, $response, $matches)) {
                    $hasItemToken = true;
                    break;
                }
            }

            if (!$hasItemToken) {
                return [
                    'valid' => false,
                    'message' => '无法提取itemToken，页面格式可能已变更'
                ];
            }

            return [
                'valid' => true,
                'message' => 'Cookie有效',
                'response_length' => strlen($response)
            ];

        } catch (Exception $e) {
            return [
                'valid' => false,
                'message' => 'Cookie验证失败: ' . $e->getMessage()
            ];
        }
    }
}
